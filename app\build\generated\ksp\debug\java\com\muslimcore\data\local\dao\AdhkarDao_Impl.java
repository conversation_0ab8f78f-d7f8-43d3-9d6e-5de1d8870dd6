package com.muslimcore.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.muslimcore.data.local.entities.DailyDhikrCount;
import com.muslimcore.data.local.entities.DhikrEntity;
import com.muslimcore.data.local.entities.DhikrProgressEntity;
import com.muslimcore.data.local.entities.DhikrStatistic;
import com.muslimcore.data.local.entities.TasbeehSessionEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AdhkarDao_Impl implements AdhkarDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<DhikrEntity> __insertionAdapterOfDhikrEntity;

  private final EntityInsertionAdapter<DhikrProgressEntity> __insertionAdapterOfDhikrProgressEntity;

  private final EntityInsertionAdapter<TasbeehSessionEntity> __insertionAdapterOfTasbeehSessionEntity;

  private final EntityDeletionOrUpdateAdapter<TasbeehSessionEntity> __deletionAdapterOfTasbeehSessionEntity;

  private final EntityDeletionOrUpdateAdapter<DhikrProgressEntity> __updateAdapterOfDhikrProgressEntity;

  private final EntityDeletionOrUpdateAdapter<TasbeehSessionEntity> __updateAdapterOfTasbeehSessionEntity;

  private final SharedSQLiteStatement __preparedStmtOfToggleFavorite;

  private final SharedSQLiteStatement __preparedStmtOfResetDailyProgress;

  private final SharedSQLiteStatement __preparedStmtOfCompleteTasbeehSession;

  private final SharedSQLiteStatement __preparedStmtOfDeleteTasbeehSessionById;

  public AdhkarDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfDhikrEntity = new EntityInsertionAdapter<DhikrEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `dhikr` (`id`,`arabicText`,`transliteration`,`translation`,`reference`,`count`,`category`,`isFavorite`,`benefits`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final DhikrEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getArabicText());
        statement.bindString(3, entity.getTransliteration());
        statement.bindString(4, entity.getTranslation());
        if (entity.getReference() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getReference());
        }
        statement.bindLong(6, entity.getCount());
        statement.bindString(7, entity.getCategory());
        final int _tmp = entity.isFavorite() ? 1 : 0;
        statement.bindLong(8, _tmp);
        if (entity.getBenefits() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getBenefits());
        }
      }
    };
    this.__insertionAdapterOfDhikrProgressEntity = new EntityInsertionAdapter<DhikrProgressEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `dhikr_progress` (`id`,`dhikrId`,`date`,`currentCount`,`targetCount`,`isCompleted`,`completedAt`,`userId`) VALUES (nullif(?, 0),?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final DhikrProgressEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getDhikrId());
        statement.bindString(3, entity.getDate());
        statement.bindLong(4, entity.getCurrentCount());
        statement.bindLong(5, entity.getTargetCount());
        final int _tmp = entity.isCompleted() ? 1 : 0;
        statement.bindLong(6, _tmp);
        if (entity.getCompletedAt() == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, entity.getCompletedAt());
        }
        if (entity.getUserId() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getUserId());
        }
      }
    };
    this.__insertionAdapterOfTasbeehSessionEntity = new EntityInsertionAdapter<TasbeehSessionEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `tasbeeh_sessions` (`id`,`dhikrText`,`count`,`targetCount`,`startTime`,`endTime`,`isCompleted`,`userId`) VALUES (nullif(?, 0),?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TasbeehSessionEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getDhikrText());
        statement.bindLong(3, entity.getCount());
        statement.bindLong(4, entity.getTargetCount());
        statement.bindLong(5, entity.getStartTime());
        if (entity.getEndTime() == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, entity.getEndTime());
        }
        final int _tmp = entity.isCompleted() ? 1 : 0;
        statement.bindLong(7, _tmp);
        if (entity.getUserId() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getUserId());
        }
      }
    };
    this.__deletionAdapterOfTasbeehSessionEntity = new EntityDeletionOrUpdateAdapter<TasbeehSessionEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `tasbeeh_sessions` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TasbeehSessionEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfDhikrProgressEntity = new EntityDeletionOrUpdateAdapter<DhikrProgressEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `dhikr_progress` SET `id` = ?,`dhikrId` = ?,`date` = ?,`currentCount` = ?,`targetCount` = ?,`isCompleted` = ?,`completedAt` = ?,`userId` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final DhikrProgressEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getDhikrId());
        statement.bindString(3, entity.getDate());
        statement.bindLong(4, entity.getCurrentCount());
        statement.bindLong(5, entity.getTargetCount());
        final int _tmp = entity.isCompleted() ? 1 : 0;
        statement.bindLong(6, _tmp);
        if (entity.getCompletedAt() == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, entity.getCompletedAt());
        }
        if (entity.getUserId() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getUserId());
        }
        statement.bindLong(9, entity.getId());
      }
    };
    this.__updateAdapterOfTasbeehSessionEntity = new EntityDeletionOrUpdateAdapter<TasbeehSessionEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `tasbeeh_sessions` SET `id` = ?,`dhikrText` = ?,`count` = ?,`targetCount` = ?,`startTime` = ?,`endTime` = ?,`isCompleted` = ?,`userId` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final TasbeehSessionEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getDhikrText());
        statement.bindLong(3, entity.getCount());
        statement.bindLong(4, entity.getTargetCount());
        statement.bindLong(5, entity.getStartTime());
        if (entity.getEndTime() == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, entity.getEndTime());
        }
        final int _tmp = entity.isCompleted() ? 1 : 0;
        statement.bindLong(7, _tmp);
        if (entity.getUserId() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getUserId());
        }
        statement.bindLong(9, entity.getId());
      }
    };
    this.__preparedStmtOfToggleFavorite = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE dhikr SET isFavorite = NOT isFavorite WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfResetDailyProgress = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM dhikr_progress WHERE date = ?";
        return _query;
      }
    };
    this.__preparedStmtOfCompleteTasbeehSession = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE tasbeeh_sessions SET isCompleted = 1, endTime = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteTasbeehSessionById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM tasbeeh_sessions WHERE id = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertDhikr(final List<DhikrEntity> dhikr,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfDhikrEntity.insert(dhikr);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertDhikrProgress(final DhikrProgressEntity progress,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfDhikrProgressEntity.insert(progress);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertTasbeehSession(final TasbeehSessionEntity session,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfTasbeehSessionEntity.insertAndReturnId(session);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteTasbeehSession(final TasbeehSessionEntity session,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfTasbeehSessionEntity.handle(session);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateDhikrProgress(final DhikrProgressEntity progress,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfDhikrProgressEntity.handle(progress);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateTasbeehSession(final TasbeehSessionEntity session,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfTasbeehSessionEntity.handle(session);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object toggleFavorite(final long dhikrId, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfToggleFavorite.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, dhikrId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfToggleFavorite.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object resetDailyProgress(final String date,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfResetDailyProgress.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, date);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfResetDailyProgress.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object completeTasbeehSession(final long sessionId, final long endTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfCompleteTasbeehSession.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, endTime);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, sessionId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfCompleteTasbeehSession.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteTasbeehSessionById(final long sessionId,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteTasbeehSessionById.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, sessionId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteTasbeehSessionById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<DhikrEntity>> getAllDhikr() {
    final String _sql = "SELECT * FROM dhikr ORDER BY category, id ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"dhikr"}, new Callable<List<DhikrEntity>>() {
      @Override
      @NonNull
      public List<DhikrEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfArabicText = CursorUtil.getColumnIndexOrThrow(_cursor, "arabicText");
          final int _cursorIndexOfTransliteration = CursorUtil.getColumnIndexOrThrow(_cursor, "transliteration");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfReference = CursorUtil.getColumnIndexOrThrow(_cursor, "reference");
          final int _cursorIndexOfCount = CursorUtil.getColumnIndexOrThrow(_cursor, "count");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfIsFavorite = CursorUtil.getColumnIndexOrThrow(_cursor, "isFavorite");
          final int _cursorIndexOfBenefits = CursorUtil.getColumnIndexOrThrow(_cursor, "benefits");
          final List<DhikrEntity> _result = new ArrayList<DhikrEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DhikrEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpArabicText;
            _tmpArabicText = _cursor.getString(_cursorIndexOfArabicText);
            final String _tmpTransliteration;
            _tmpTransliteration = _cursor.getString(_cursorIndexOfTransliteration);
            final String _tmpTranslation;
            _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            final String _tmpReference;
            if (_cursor.isNull(_cursorIndexOfReference)) {
              _tmpReference = null;
            } else {
              _tmpReference = _cursor.getString(_cursorIndexOfReference);
            }
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final boolean _tmpIsFavorite;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsFavorite);
            _tmpIsFavorite = _tmp != 0;
            final String _tmpBenefits;
            if (_cursor.isNull(_cursorIndexOfBenefits)) {
              _tmpBenefits = null;
            } else {
              _tmpBenefits = _cursor.getString(_cursorIndexOfBenefits);
            }
            _item = new DhikrEntity(_tmpId,_tmpArabicText,_tmpTransliteration,_tmpTranslation,_tmpReference,_tmpCount,_tmpCategory,_tmpIsFavorite,_tmpBenefits);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<DhikrEntity>> getDhikrByCategory(final String category) {
    final String _sql = "SELECT * FROM dhikr WHERE category = ? ORDER BY id ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, category);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"dhikr"}, new Callable<List<DhikrEntity>>() {
      @Override
      @NonNull
      public List<DhikrEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfArabicText = CursorUtil.getColumnIndexOrThrow(_cursor, "arabicText");
          final int _cursorIndexOfTransliteration = CursorUtil.getColumnIndexOrThrow(_cursor, "transliteration");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfReference = CursorUtil.getColumnIndexOrThrow(_cursor, "reference");
          final int _cursorIndexOfCount = CursorUtil.getColumnIndexOrThrow(_cursor, "count");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfIsFavorite = CursorUtil.getColumnIndexOrThrow(_cursor, "isFavorite");
          final int _cursorIndexOfBenefits = CursorUtil.getColumnIndexOrThrow(_cursor, "benefits");
          final List<DhikrEntity> _result = new ArrayList<DhikrEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DhikrEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpArabicText;
            _tmpArabicText = _cursor.getString(_cursorIndexOfArabicText);
            final String _tmpTransliteration;
            _tmpTransliteration = _cursor.getString(_cursorIndexOfTransliteration);
            final String _tmpTranslation;
            _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            final String _tmpReference;
            if (_cursor.isNull(_cursorIndexOfReference)) {
              _tmpReference = null;
            } else {
              _tmpReference = _cursor.getString(_cursorIndexOfReference);
            }
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final boolean _tmpIsFavorite;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsFavorite);
            _tmpIsFavorite = _tmp != 0;
            final String _tmpBenefits;
            if (_cursor.isNull(_cursorIndexOfBenefits)) {
              _tmpBenefits = null;
            } else {
              _tmpBenefits = _cursor.getString(_cursorIndexOfBenefits);
            }
            _item = new DhikrEntity(_tmpId,_tmpArabicText,_tmpTransliteration,_tmpTranslation,_tmpReference,_tmpCount,_tmpCategory,_tmpIsFavorite,_tmpBenefits);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getDhikrById(final long id, final Continuation<? super DhikrEntity> $completion) {
    final String _sql = "SELECT * FROM dhikr WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<DhikrEntity>() {
      @Override
      @Nullable
      public DhikrEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfArabicText = CursorUtil.getColumnIndexOrThrow(_cursor, "arabicText");
          final int _cursorIndexOfTransliteration = CursorUtil.getColumnIndexOrThrow(_cursor, "transliteration");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfReference = CursorUtil.getColumnIndexOrThrow(_cursor, "reference");
          final int _cursorIndexOfCount = CursorUtil.getColumnIndexOrThrow(_cursor, "count");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfIsFavorite = CursorUtil.getColumnIndexOrThrow(_cursor, "isFavorite");
          final int _cursorIndexOfBenefits = CursorUtil.getColumnIndexOrThrow(_cursor, "benefits");
          final DhikrEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpArabicText;
            _tmpArabicText = _cursor.getString(_cursorIndexOfArabicText);
            final String _tmpTransliteration;
            _tmpTransliteration = _cursor.getString(_cursorIndexOfTransliteration);
            final String _tmpTranslation;
            _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            final String _tmpReference;
            if (_cursor.isNull(_cursorIndexOfReference)) {
              _tmpReference = null;
            } else {
              _tmpReference = _cursor.getString(_cursorIndexOfReference);
            }
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final boolean _tmpIsFavorite;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsFavorite);
            _tmpIsFavorite = _tmp != 0;
            final String _tmpBenefits;
            if (_cursor.isNull(_cursorIndexOfBenefits)) {
              _tmpBenefits = null;
            } else {
              _tmpBenefits = _cursor.getString(_cursorIndexOfBenefits);
            }
            _result = new DhikrEntity(_tmpId,_tmpArabicText,_tmpTransliteration,_tmpTranslation,_tmpReference,_tmpCount,_tmpCategory,_tmpIsFavorite,_tmpBenefits);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<DhikrEntity>> searchDhikr(final String query) {
    final String _sql = "SELECT * FROM dhikr WHERE arabicText LIKE '%' || ? || '%' OR transliteration LIKE '%' || ? || '%' OR translation LIKE '%' || ? || '%'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 3);
    int _argIndex = 1;
    _statement.bindString(_argIndex, query);
    _argIndex = 2;
    _statement.bindString(_argIndex, query);
    _argIndex = 3;
    _statement.bindString(_argIndex, query);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"dhikr"}, new Callable<List<DhikrEntity>>() {
      @Override
      @NonNull
      public List<DhikrEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfArabicText = CursorUtil.getColumnIndexOrThrow(_cursor, "arabicText");
          final int _cursorIndexOfTransliteration = CursorUtil.getColumnIndexOrThrow(_cursor, "transliteration");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfReference = CursorUtil.getColumnIndexOrThrow(_cursor, "reference");
          final int _cursorIndexOfCount = CursorUtil.getColumnIndexOrThrow(_cursor, "count");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfIsFavorite = CursorUtil.getColumnIndexOrThrow(_cursor, "isFavorite");
          final int _cursorIndexOfBenefits = CursorUtil.getColumnIndexOrThrow(_cursor, "benefits");
          final List<DhikrEntity> _result = new ArrayList<DhikrEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DhikrEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpArabicText;
            _tmpArabicText = _cursor.getString(_cursorIndexOfArabicText);
            final String _tmpTransliteration;
            _tmpTransliteration = _cursor.getString(_cursorIndexOfTransliteration);
            final String _tmpTranslation;
            _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            final String _tmpReference;
            if (_cursor.isNull(_cursorIndexOfReference)) {
              _tmpReference = null;
            } else {
              _tmpReference = _cursor.getString(_cursorIndexOfReference);
            }
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final boolean _tmpIsFavorite;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsFavorite);
            _tmpIsFavorite = _tmp != 0;
            final String _tmpBenefits;
            if (_cursor.isNull(_cursorIndexOfBenefits)) {
              _tmpBenefits = null;
            } else {
              _tmpBenefits = _cursor.getString(_cursorIndexOfBenefits);
            }
            _item = new DhikrEntity(_tmpId,_tmpArabicText,_tmpTransliteration,_tmpTranslation,_tmpReference,_tmpCount,_tmpCategory,_tmpIsFavorite,_tmpBenefits);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<DhikrEntity>> getFavoriteDhikr() {
    final String _sql = "SELECT * FROM dhikr WHERE isFavorite = 1 ORDER BY category, id ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"dhikr"}, new Callable<List<DhikrEntity>>() {
      @Override
      @NonNull
      public List<DhikrEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfArabicText = CursorUtil.getColumnIndexOrThrow(_cursor, "arabicText");
          final int _cursorIndexOfTransliteration = CursorUtil.getColumnIndexOrThrow(_cursor, "transliteration");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfReference = CursorUtil.getColumnIndexOrThrow(_cursor, "reference");
          final int _cursorIndexOfCount = CursorUtil.getColumnIndexOrThrow(_cursor, "count");
          final int _cursorIndexOfCategory = CursorUtil.getColumnIndexOrThrow(_cursor, "category");
          final int _cursorIndexOfIsFavorite = CursorUtil.getColumnIndexOrThrow(_cursor, "isFavorite");
          final int _cursorIndexOfBenefits = CursorUtil.getColumnIndexOrThrow(_cursor, "benefits");
          final List<DhikrEntity> _result = new ArrayList<DhikrEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DhikrEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpArabicText;
            _tmpArabicText = _cursor.getString(_cursorIndexOfArabicText);
            final String _tmpTransliteration;
            _tmpTransliteration = _cursor.getString(_cursorIndexOfTransliteration);
            final String _tmpTranslation;
            _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            final String _tmpReference;
            if (_cursor.isNull(_cursorIndexOfReference)) {
              _tmpReference = null;
            } else {
              _tmpReference = _cursor.getString(_cursorIndexOfReference);
            }
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final boolean _tmpIsFavorite;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsFavorite);
            _tmpIsFavorite = _tmp != 0;
            final String _tmpBenefits;
            if (_cursor.isNull(_cursorIndexOfBenefits)) {
              _tmpBenefits = null;
            } else {
              _tmpBenefits = _cursor.getString(_cursorIndexOfBenefits);
            }
            _item = new DhikrEntity(_tmpId,_tmpArabicText,_tmpTransliteration,_tmpTranslation,_tmpReference,_tmpCount,_tmpCategory,_tmpIsFavorite,_tmpBenefits);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<DhikrProgressEntity> getDhikrProgress(final long dhikrId, final String date) {
    final String _sql = "SELECT * FROM dhikr_progress WHERE dhikrId = ? AND date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, dhikrId);
    _argIndex = 2;
    _statement.bindString(_argIndex, date);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"dhikr_progress"}, new Callable<DhikrProgressEntity>() {
      @Override
      @Nullable
      public DhikrProgressEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDhikrId = CursorUtil.getColumnIndexOrThrow(_cursor, "dhikrId");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "currentCount");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetCount");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final DhikrProgressEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpDhikrId;
            _tmpDhikrId = _cursor.getLong(_cursorIndexOfDhikrId);
            final String _tmpDate;
            _tmpDate = _cursor.getString(_cursorIndexOfDate);
            final int _tmpCurrentCount;
            _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final Long _tmpCompletedAt;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmpCompletedAt = null;
            } else {
              _tmpCompletedAt = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            _result = new DhikrProgressEntity(_tmpId,_tmpDhikrId,_tmpDate,_tmpCurrentCount,_tmpTargetCount,_tmpIsCompleted,_tmpCompletedAt,_tmpUserId);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<DhikrProgressEntity>> getDailyProgress(final String date) {
    final String _sql = "SELECT * FROM dhikr_progress WHERE date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, date);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"dhikr_progress"}, new Callable<List<DhikrProgressEntity>>() {
      @Override
      @NonNull
      public List<DhikrProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDhikrId = CursorUtil.getColumnIndexOrThrow(_cursor, "dhikrId");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "currentCount");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetCount");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final List<DhikrProgressEntity> _result = new ArrayList<DhikrProgressEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DhikrProgressEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpDhikrId;
            _tmpDhikrId = _cursor.getLong(_cursorIndexOfDhikrId);
            final String _tmpDate;
            _tmpDate = _cursor.getString(_cursorIndexOfDate);
            final int _tmpCurrentCount;
            _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final Long _tmpCompletedAt;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmpCompletedAt = null;
            } else {
              _tmpCompletedAt = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            _item = new DhikrProgressEntity(_tmpId,_tmpDhikrId,_tmpDate,_tmpCurrentCount,_tmpTargetCount,_tmpIsCompleted,_tmpCompletedAt,_tmpUserId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<DhikrProgressEntity>> getProgressByCategory(final String category,
      final String date) {
    final String _sql = "\n"
            + "        SELECT dp.* FROM dhikr_progress dp\n"
            + "        INNER JOIN dhikr d ON dp.dhikrId = d.id\n"
            + "        WHERE d.category = ? AND dp.date = ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, category);
    _argIndex = 2;
    _statement.bindString(_argIndex, date);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"dhikr_progress",
        "dhikr"}, new Callable<List<DhikrProgressEntity>>() {
      @Override
      @NonNull
      public List<DhikrProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDhikrId = CursorUtil.getColumnIndexOrThrow(_cursor, "dhikrId");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfCurrentCount = CursorUtil.getColumnIndexOrThrow(_cursor, "currentCount");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetCount");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final List<DhikrProgressEntity> _result = new ArrayList<DhikrProgressEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DhikrProgressEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final long _tmpDhikrId;
            _tmpDhikrId = _cursor.getLong(_cursorIndexOfDhikrId);
            final String _tmpDate;
            _tmpDate = _cursor.getString(_cursorIndexOfDate);
            final int _tmpCurrentCount;
            _tmpCurrentCount = _cursor.getInt(_cursorIndexOfCurrentCount);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final Long _tmpCompletedAt;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmpCompletedAt = null;
            } else {
              _tmpCompletedAt = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            _item = new DhikrProgressEntity(_tmpId,_tmpDhikrId,_tmpDate,_tmpCurrentCount,_tmpTargetCount,_tmpIsCompleted,_tmpCompletedAt,_tmpUserId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<TasbeehSessionEntity>> getAllTasbeehSessions() {
    final String _sql = "SELECT * FROM tasbeeh_sessions ORDER BY startTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasbeeh_sessions"}, new Callable<List<TasbeehSessionEntity>>() {
      @Override
      @NonNull
      public List<TasbeehSessionEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDhikrText = CursorUtil.getColumnIndexOrThrow(_cursor, "dhikrText");
          final int _cursorIndexOfCount = CursorUtil.getColumnIndexOrThrow(_cursor, "count");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetCount");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final List<TasbeehSessionEntity> _result = new ArrayList<TasbeehSessionEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final TasbeehSessionEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpDhikrText;
            _tmpDhikrText = _cursor.getString(_cursorIndexOfDhikrText);
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final Long _tmpEndTime;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmpEndTime = null;
            } else {
              _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            }
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            _item = new TasbeehSessionEntity(_tmpId,_tmpDhikrText,_tmpCount,_tmpTargetCount,_tmpStartTime,_tmpEndTime,_tmpIsCompleted,_tmpUserId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<TasbeehSessionEntity> getActiveTasbeehSession() {
    final String _sql = "SELECT * FROM tasbeeh_sessions WHERE isCompleted = 0 ORDER BY startTime DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"tasbeeh_sessions"}, new Callable<TasbeehSessionEntity>() {
      @Override
      @Nullable
      public TasbeehSessionEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDhikrText = CursorUtil.getColumnIndexOrThrow(_cursor, "dhikrText");
          final int _cursorIndexOfCount = CursorUtil.getColumnIndexOrThrow(_cursor, "count");
          final int _cursorIndexOfTargetCount = CursorUtil.getColumnIndexOrThrow(_cursor, "targetCount");
          final int _cursorIndexOfStartTime = CursorUtil.getColumnIndexOrThrow(_cursor, "startTime");
          final int _cursorIndexOfEndTime = CursorUtil.getColumnIndexOrThrow(_cursor, "endTime");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final TasbeehSessionEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpDhikrText;
            _tmpDhikrText = _cursor.getString(_cursorIndexOfDhikrText);
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            final int _tmpTargetCount;
            _tmpTargetCount = _cursor.getInt(_cursorIndexOfTargetCount);
            final long _tmpStartTime;
            _tmpStartTime = _cursor.getLong(_cursorIndexOfStartTime);
            final Long _tmpEndTime;
            if (_cursor.isNull(_cursorIndexOfEndTime)) {
              _tmpEndTime = null;
            } else {
              _tmpEndTime = _cursor.getLong(_cursorIndexOfEndTime);
            }
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            _result = new TasbeehSessionEntity(_tmpId,_tmpDhikrText,_tmpCount,_tmpTargetCount,_tmpStartTime,_tmpEndTime,_tmpIsCompleted,_tmpUserId);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getDhikrStatistics(final String startDate, final String endDate,
      final Continuation<? super List<DhikrStatistic>> $completion) {
    final String _sql = "\n"
            + "        SELECT d.category, COUNT(dp.id) as count FROM dhikr_progress dp\n"
            + "        INNER JOIN dhikr d ON dp.dhikrId = d.id\n"
            + "        WHERE dp.isCompleted = 1 AND dp.date BETWEEN ? AND ?\n"
            + "        GROUP BY d.category\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindString(_argIndex, endDate);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<DhikrStatistic>>() {
      @Override
      @NonNull
      public List<DhikrStatistic> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCategory = 0;
          final int _cursorIndexOfCount = 1;
          final List<DhikrStatistic> _result = new ArrayList<DhikrStatistic>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DhikrStatistic _item;
            final String _tmpCategory;
            _tmpCategory = _cursor.getString(_cursorIndexOfCategory);
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            _item = new DhikrStatistic(_tmpCategory,_tmpCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<Integer> getTotalDhikrCount() {
    final String _sql = "SELECT SUM(currentCount) FROM dhikr_progress WHERE isCompleted = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"dhikr_progress"}, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Integer> getStreakForCategory(final String category) {
    final String _sql = "\n"
            + "        SELECT COUNT(DISTINCT date) FROM dhikr_progress dp\n"
            + "        INNER JOIN dhikr d ON dp.dhikrId = d.id\n"
            + "        WHERE d.category = ? AND dp.isCompleted = 1\n"
            + "        ORDER BY date DESC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, category);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"dhikr_progress",
        "dhikr"}, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getDailyDhikrCounts(final String startDate, final String endDate,
      final Continuation<? super List<DailyDhikrCount>> $completion) {
    final String _sql = "\n"
            + "        SELECT dp.date, COUNT(dp.id) as count FROM dhikr_progress dp\n"
            + "        WHERE dp.isCompleted = 1 AND dp.date BETWEEN ? AND ?\n"
            + "        GROUP BY dp.date ORDER BY dp.date ASC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindString(_argIndex, endDate);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<DailyDhikrCount>>() {
      @Override
      @NonNull
      public List<DailyDhikrCount> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfDate = 0;
          final int _cursorIndexOfCount = 1;
          final List<DailyDhikrCount> _result = new ArrayList<DailyDhikrCount>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DailyDhikrCount _item;
            final String _tmpDate;
            _tmpDate = _cursor.getString(_cursorIndexOfDate);
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            _item = new DailyDhikrCount(_tmpDate,_tmpCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
