package com.muslimcore.presentation.services;

import com.muslimcore.data.local.managers.LocationManager;
import com.muslimcore.data.local.managers.PrayerTimeManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PrayerService_MembersInjector implements MembersInjector<PrayerService> {
  private final Provider<LocationManager> locationManagerProvider;

  private final Provider<PrayerTimeManager> prayerTimeManagerProvider;

  public PrayerService_MembersInjector(Provider<LocationManager> locationManagerProvider,
      Provider<PrayerTimeManager> prayerTimeManagerProvider) {
    this.locationManagerProvider = locationManagerProvider;
    this.prayerTimeManagerProvider = prayerTimeManagerProvider;
  }

  public static MembersInjector<PrayerService> create(
      Provider<LocationManager> locationManagerProvider,
      Provider<PrayerTimeManager> prayerTimeManagerProvider) {
    return new PrayerService_MembersInjector(locationManagerProvider, prayerTimeManagerProvider);
  }

  @Override
  public void injectMembers(PrayerService instance) {
    injectLocationManager(instance, locationManagerProvider.get());
    injectPrayerTimeManager(instance, prayerTimeManagerProvider.get());
  }

  @InjectedFieldSignature("com.muslimcore.presentation.services.PrayerService.locationManager")
  public static void injectLocationManager(PrayerService instance,
      LocationManager locationManager) {
    instance.locationManager = locationManager;
  }

  @InjectedFieldSignature("com.muslimcore.presentation.services.PrayerService.prayerTimeManager")
  public static void injectPrayerTimeManager(PrayerService instance,
      PrayerTimeManager prayerTimeManager) {
    instance.prayerTimeManager = prayerTimeManager;
  }
}
