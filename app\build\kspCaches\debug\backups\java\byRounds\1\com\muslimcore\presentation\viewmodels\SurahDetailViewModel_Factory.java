package com.muslimcore.presentation.viewmodels;

import com.muslimcore.domain.repositories.QuranRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SurahDetailViewModel_Factory implements Factory<SurahDetailViewModel> {
  private final Provider<QuranRepository> quranRepositoryProvider;

  public SurahDetailViewModel_Factory(Provider<QuranRepository> quranRepositoryProvider) {
    this.quranRepositoryProvider = quranRepositoryProvider;
  }

  @Override
  public SurahDetailViewModel get() {
    return newInstance(quranRepositoryProvider.get());
  }

  public static SurahDetailViewModel_Factory create(
      Provider<QuranRepository> quranRepositoryProvider) {
    return new SurahDetailViewModel_Factory(quranRepositoryProvider);
  }

  public static SurahDetailViewModel newInstance(QuranRepository quranRepository) {
    return new SurahDetailViewModel(quranRepository);
  }
}
