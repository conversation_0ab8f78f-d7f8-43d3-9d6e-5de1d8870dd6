package com.muslimcore.presentation

import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.muslimcore.R
import com.muslimcore.databinding.ActivityMainBinding
import com.muslimcore.presentation.fragments.AdhkarFragment
import com.muslimcore.presentation.fragments.BaseFragment
import com.muslimcore.presentation.fragments.LocationPickerFragment
import com.muslimcore.presentation.fragments.NotificationSettingsFragment
import com.muslimcore.presentation.fragments.PrayerFragment
import com.muslimcore.presentation.fragments.PrayerTrackerFragment
import com.muslimcore.presentation.fragments.QuranFragment
import com.muslimcore.presentation.fragments.SettingsFragment
import com.muslimcore.presentation.utils.ThemeManager
import com.muslimcore.presentation.viewmodels.MainViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private val viewModel: MainViewModel by viewModels()

    @Inject
    lateinit var themeManager: ThemeManager

    // Fragment caching to prevent recreation and data reloading
    private var prayerFragment: PrayerFragment? = null
    private var quranFragment: QuranFragment? = null
    private var adhkarFragment: AdhkarFragment? = null
    private var settingsFragment: SettingsFragment? = null
    private var currentFragment: Fragment? = null

    // Permission launcher for location
    private val locationPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val fineLocationGranted = permissions[Manifest.permission.ACCESS_FINE_LOCATION] ?: false
        val coarseLocationGranted = permissions[Manifest.permission.ACCESS_COARSE_LOCATION] ?: false

        if (fineLocationGranted || coarseLocationGranted) {
            viewModel.onLocationPermissionGranted()
        } else {
            viewModel.onLocationPermissionDenied()
        }
    }

    // Permission launcher for notifications (Android 13+)
    private val notificationPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        viewModel.onNotificationPermissionResult(isGranted)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            binding = ActivityMainBinding.inflate(layoutInflater)
            setContentView(binding.root)

            setupBottomNavigation()
            observeViewModel()
            checkPermissions()
            applyTheme()

            // Load default fragment if no saved state
            if (savedInstanceState == null) {
                showCachedFragment {
                    if (prayerFragment == null) {
                        prayerFragment = PrayerFragment()
                    }
                    prayerFragment!!
                }
                binding.bottomNavigation.selectedItemId = R.id.nav_prayer
            }
        } catch (e: Exception) {
            // Log the error and show a simple message
            android.util.Log.e("MainActivity", "Error in onCreate", e)
            // Try to show a simple error message
            try {
                android.widget.Toast.makeText(this, "App initialization error. Please restart.", android.widget.Toast.LENGTH_LONG).show()
            } catch (ex: Exception) {
                // If even toast fails, just finish the activity
                finish()
            }
        }
    }

    private fun setupBottomNavigation() {
        binding.bottomNavigation.setOnItemSelectedListener { item ->
            when (item.itemId) {
                R.id.nav_prayer -> {
                    showCachedFragment {
                        if (prayerFragment == null) {
                            prayerFragment = PrayerFragment()
                        }
                        prayerFragment!!
                    }
                    true
                }
                R.id.nav_quran -> {
                    showCachedFragment {
                        if (quranFragment == null) {
                            quranFragment = QuranFragment()
                        }
                        quranFragment!!
                    }
                    true
                }
                R.id.nav_adhkar -> {
                    showCachedFragment {
                        if (adhkarFragment == null) {
                            adhkarFragment = AdhkarFragment()
                        }
                        adhkarFragment!!
                    }
                    true
                }
                R.id.nav_settings -> {
                    showCachedFragment {
                        if (settingsFragment == null) {
                            settingsFragment = SettingsFragment()
                        }
                        settingsFragment!!
                    }
                    true
                }
                else -> false
            }
        }
    }

    private fun showCachedFragment(fragmentProvider: () -> Fragment) {
        val fragment = fragmentProvider()

        // Only replace if it's a different fragment
        if (currentFragment != fragment) {
            currentFragment = fragment
            supportFragmentManager.beginTransaction()
                .replace(R.id.fragment_container, fragment)
                .commit()
        }
    }

    // Keep the old method for special cases like PrayerTracker
    private fun loadFragment(fragment: Fragment) {
        supportFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, fragment)
            .commit()
    }

    fun navigateToPrayerTracker() {
        // Hide bottom navigation
        hideBottomNavigation()

        supportFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, PrayerTrackerFragment())
            .addToBackStack("prayer_tracker")
            .commit()
    }

    fun navigateToLocationPicker() {
        // Hide bottom navigation
        hideBottomNavigation()

        supportFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, LocationPickerFragment())
            .addToBackStack("location_picker")
            .commit()
    }

    fun navigateToNotificationSettings() {
        // Hide bottom navigation
        hideBottomNavigation()

        supportFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, NotificationSettingsFragment())
            .addToBackStack("notification_settings")
            .commit()
    }

    fun hideBottomNavigation() {
        binding.bottomNavigation.visibility = android.view.View.GONE

        // Listen for back stack changes to show bottom nav again
        supportFragmentManager.addOnBackStackChangedListener {
            if (supportFragmentManager.backStackEntryCount == 0) {
                // Back to main fragments, show bottom navigation
                showBottomNavigation()
            }
        }
    }

    fun showBottomNavigation() {
        binding.bottomNavigation.visibility = android.view.View.VISIBLE
    }

    private fun observeViewModel() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                // Handle UI state changes
                when {
                    state.shouldRequestLocationPermission -> {
                        requestLocationPermission()
                    }
                    state.shouldRequestNotificationPermission -> {
                        requestNotificationPermission()
                    }
                }
            }
        }
    }

    private fun checkPermissions() {
        // Check location permission
        val hasLocationPermission = ContextCompat.checkSelfPermission(
            this, Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED ||
        ContextCompat.checkSelfPermission(
            this, Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED

        // Only request location permission if not granted AND not previously asked
        if (!hasLocationPermission) {
            viewModel.checkAndRequestLocationPermission()
        } else {
            // Permission already granted, save location if needed
            viewModel.onLocationPermissionGranted()
        }

        // Check notification permission (Android 13+)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val hasNotificationPermission = ContextCompat.checkSelfPermission(
                this, Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED

            if (!hasNotificationPermission) {
                viewModel.setNotificationPermissionNeeded(true)
            }
        }
    }

    private fun requestLocationPermission() {
        locationPermissionLauncher.launch(
            arrayOf(
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
            )
        )
    }

    private fun requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            notificationPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.onResume()
    }

    override fun onPause() {
        super.onPause()
        viewModel.onPause()
    }

    fun applyTheme() {
        val themeColors = themeManager.getThemeColors(this)

        // Apply theme to bottom navigation
        binding.bottomNavigation.setBackgroundColor(themeColors.bottomNavBackgroundColor)

        // Apply theme to bottom navigation items
        val colorStateList = android.content.res.ColorStateList(
            arrayOf(
                intArrayOf(android.R.attr.state_checked),
                intArrayOf(-android.R.attr.state_checked)
            ),
            intArrayOf(
                themeColors.bottomNavSelectedColor,
                themeColors.bottomNavUnselectedColor
            )
        )
        binding.bottomNavigation.itemIconTintList = colorStateList
        binding.bottomNavigation.itemTextColor = colorStateList

        // Apply theme to status bar
        window.statusBarColor = themeColors.statusBarColor

        // Apply theme to navigation bar
        window.navigationBarColor = themeColors.bottomNavBackgroundColor

        // Apply theme to main background
        binding.root.setBackgroundColor(themeColors.backgroundColor)

        // Refresh all fragments
        refreshAllFragments()
    }

    private fun refreshAllFragments() {
        // Get all fragments and refresh their themes
        val fragments = supportFragmentManager.fragments
        fragments.forEach { fragment ->
            if (fragment is BaseFragment) {
                fragment.refreshTheme()
            }
        }

        // Also refresh the current fragment by detaching and reattaching
        val currentFragment = supportFragmentManager.findFragmentById(R.id.fragment_container)
        currentFragment?.let {
            supportFragmentManager.beginTransaction()
                .detach(it)
                .attach(it)
                .commit()
        }
    }
}
