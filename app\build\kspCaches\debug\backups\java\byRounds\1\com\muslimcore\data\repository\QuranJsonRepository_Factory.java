package com.muslimcore.data.repository;

import com.muslimcore.data.local.QuranJsonDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class QuranJsonRepository_Factory implements Factory<QuranJsonRepository> {
  private final Provider<QuranJsonDataSource> quranJsonDataSourceProvider;

  public QuranJsonRepository_Factory(Provider<QuranJsonDataSource> quranJsonDataSourceProvider) {
    this.quranJsonDataSourceProvider = quranJsonDataSourceProvider;
  }

  @Override
  public QuranJsonRepository get() {
    return newInstance(quranJsonDataSourceProvider.get());
  }

  public static QuranJsonRepository_Factory create(
      Provider<QuranJsonDataSource> quranJsonDataSourceProvider) {
    return new QuranJsonRepository_Factory(quranJsonDataSourceProvider);
  }

  public static QuranJsonRepository newInstance(QuranJsonDataSource quranJsonDataSource) {
    return new QuranJsonRepository(quranJsonDataSource);
  }
}
