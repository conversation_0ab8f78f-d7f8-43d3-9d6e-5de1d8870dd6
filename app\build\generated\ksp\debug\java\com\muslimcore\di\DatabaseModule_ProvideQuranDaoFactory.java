package com.muslimcore.di;

import com.muslimcore.data.local.dao.QuranDao;
import com.muslimcore.data.local.database.MuslimCoreDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideQuranDaoFactory implements Factory<QuranDao> {
  private final Provider<MuslimCoreDatabase> databaseProvider;

  public DatabaseModule_ProvideQuranDaoFactory(Provider<MuslimCoreDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public QuranDao get() {
    return provideQuranDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideQuranDaoFactory create(
      Provider<MuslimCoreDatabase> databaseProvider) {
    return new DatabaseModule_ProvideQuranDaoFactory(databaseProvider);
  }

  public static QuranDao provideQuranDao(MuslimCoreDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideQuranDao(database));
  }
}
