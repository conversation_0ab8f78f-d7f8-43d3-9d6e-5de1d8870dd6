package com.muslimcore.domain.usecases

import com.muslimcore.domain.models.LocationResult
import com.muslimcore.domain.repositories.LocationRepository
import kotlinx.coroutines.delay
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Use case for getting current location with retry logic and debouncing
 */
@Singleton
class GetCurrentLocationUseCase @Inject constructor(
    private val locationRepository: LocationRepository
) {
    
    private var lastRequestTime = 0L
    private val debounceTimeMs = 2000L // 2 seconds debounce
    
    /**
     * Get current location with debouncing to prevent rapid successive calls
     */
    suspend fun execute(forceRefresh: Boolean = false): LocationResult {
        val currentTime = System.currentTimeMillis()
        
        // Debounce rapid calls
        if (!forceRefresh && currentTime - lastRequestTime < debounceTimeMs) {
            // Return cached location if available
            locationRepository.getCachedLocation()?.let { cached ->
                return LocationResult.Success(cached)
            }
        }
        
        lastRequestTime = currentTime
        
        return try {
            locationRepository.getCurrentLocation()
        } catch (e: Exception) {
            LocationResult.Error("Location request failed: ${e.message}", e)
        }
    }
    
    /**
     * Get current location with retry logic
     */
    suspend fun executeWithRetry(maxRetries: Int = 2): LocationResult {
        repeat(maxRetries) { attempt ->
            val result = execute(forceRefresh = attempt > 0)
            
            when (result) {
                is LocationResult.Success -> return result
                is LocationResult.PermissionDenied -> return result // Don't retry permission issues
                is LocationResult.Error -> {
                    if (attempt < maxRetries - 1) {
                        delay(1000L * (attempt + 1)) // Exponential backoff
                    }
                }
                is LocationResult.Timeout -> {
                    if (attempt < maxRetries - 1) {
                        delay(2000L) // Wait before retry on timeout
                    }
                }
            }
        }
        
        // All retries failed, return cached or default
        return locationRepository.getCachedLocation()?.let { cached ->
            LocationResult.Success(cached)
        } ?: LocationResult.Success(locationRepository.getDefaultLocation())
    }
}
