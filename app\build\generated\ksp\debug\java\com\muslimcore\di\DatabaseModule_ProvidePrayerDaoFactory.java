package com.muslimcore.di;

import com.muslimcore.data.local.dao.PrayerDao;
import com.muslimcore.data.local.database.MuslimCoreDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvidePrayerDaoFactory implements Factory<PrayerDao> {
  private final Provider<MuslimCoreDatabase> databaseProvider;

  public DatabaseModule_ProvidePrayerDaoFactory(Provider<MuslimCoreDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public PrayerDao get() {
    return providePrayerDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvidePrayerDaoFactory create(
      Provider<MuslimCoreDatabase> databaseProvider) {
    return new DatabaseModule_ProvidePrayerDaoFactory(databaseProvider);
  }

  public static PrayerDao providePrayerDao(MuslimCoreDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.providePrayerDao(database));
  }
}
