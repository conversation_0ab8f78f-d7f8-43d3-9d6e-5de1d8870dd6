package com.muslimcore.data.repositories;

import android.content.Context;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.auth.FirebaseAuth;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserRepositoryImpl_Factory implements Factory<UserRepositoryImpl> {
  private final Provider<Context> contextProvider;

  private final Provider<FirebaseAuth> firebaseAuthProvider;

  private final Provider<FirebaseAnalytics> firebaseAnalyticsProvider;

  public UserRepositoryImpl_Factory(Provider<Context> contextProvider,
      Provider<FirebaseAuth> firebaseAuthProvider,
      Provider<FirebaseAnalytics> firebaseAnalyticsProvider) {
    this.contextProvider = contextProvider;
    this.firebaseAuthProvider = firebaseAuthProvider;
    this.firebaseAnalyticsProvider = firebaseAnalyticsProvider;
  }

  @Override
  public UserRepositoryImpl get() {
    return newInstance(contextProvider.get(), firebaseAuthProvider.get(), firebaseAnalyticsProvider.get());
  }

  public static UserRepositoryImpl_Factory create(Provider<Context> contextProvider,
      Provider<FirebaseAuth> firebaseAuthProvider,
      Provider<FirebaseAnalytics> firebaseAnalyticsProvider) {
    return new UserRepositoryImpl_Factory(contextProvider, firebaseAuthProvider, firebaseAnalyticsProvider);
  }

  public static UserRepositoryImpl newInstance(Context context, FirebaseAuth firebaseAuth,
      FirebaseAnalytics firebaseAnalytics) {
    return new UserRepositoryImpl(context, firebaseAuth, firebaseAnalytics);
  }
}
