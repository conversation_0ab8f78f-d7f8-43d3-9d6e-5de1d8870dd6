package com.muslimcore.di;

import com.muslimcore.data.remote.api.QuranApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvideQuranApiFactory implements Factory<QuranApi> {
  @Override
  public QuranApi get() {
    return provideQuranApi();
  }

  public static NetworkModule_ProvideQuranApiFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static QuranApi provideQuranApi() {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideQuranApi());
  }

  private static final class InstanceHolder {
    private static final NetworkModule_ProvideQuranApiFactory INSTANCE = new NetworkModule_ProvideQuranApiFactory();
  }
}
