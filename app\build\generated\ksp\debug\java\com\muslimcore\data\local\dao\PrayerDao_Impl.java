package com.muslimcore.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.muslimcore.data.local.entities.DailyPrayerCount;
import com.muslimcore.data.local.entities.PrayerLogEntity;
import com.muslimcore.data.local.entities.PrayerStatistic;
import com.muslimcore.data.local.entities.PrayerTimesEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class PrayerDao_Impl implements PrayerDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<PrayerTimesEntity> __insertionAdapterOfPrayerTimesEntity;

  private final EntityInsertionAdapter<PrayerLogEntity> __insertionAdapterOfPrayerLogEntity;

  private final EntityDeletionOrUpdateAdapter<PrayerLogEntity> __deletionAdapterOfPrayerLogEntity;

  private final EntityDeletionOrUpdateAdapter<PrayerLogEntity> __updateAdapterOfPrayerLogEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteExpiredPrayerTimes;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldPrayerLogs;

  public PrayerDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfPrayerTimesEntity = new EntityInsertionAdapter<PrayerTimesEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `prayer_times` (`date`,`fajr`,`sunrise`,`dhuhr`,`asr`,`maghrib`,`isha`,`hijriDate`,`city`,`country`,`latitude`,`longitude`,`timezone`,`method`,`cachedAt`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PrayerTimesEntity entity) {
        statement.bindString(1, entity.getDate());
        statement.bindString(2, entity.getFajr());
        statement.bindString(3, entity.getSunrise());
        statement.bindString(4, entity.getDhuhr());
        statement.bindString(5, entity.getAsr());
        statement.bindString(6, entity.getMaghrib());
        statement.bindString(7, entity.getIsha());
        statement.bindString(8, entity.getHijriDate());
        statement.bindString(9, entity.getCity());
        statement.bindString(10, entity.getCountry());
        statement.bindDouble(11, entity.getLatitude());
        statement.bindDouble(12, entity.getLongitude());
        statement.bindString(13, entity.getTimezone());
        statement.bindLong(14, entity.getMethod());
        statement.bindLong(15, entity.getCachedAt());
      }
    };
    this.__insertionAdapterOfPrayerLogEntity = new EntityInsertionAdapter<PrayerLogEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `prayer_logs` (`id`,`date`,`prayerName`,`isCompleted`,`completedAt`,`userId`,`createdAt`) VALUES (nullif(?, 0),?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PrayerLogEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getDate());
        statement.bindString(3, entity.getPrayerName());
        final int _tmp = entity.isCompleted() ? 1 : 0;
        statement.bindLong(4, _tmp);
        if (entity.getCompletedAt() == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, entity.getCompletedAt());
        }
        if (entity.getUserId() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getUserId());
        }
        statement.bindLong(7, entity.getCreatedAt());
      }
    };
    this.__deletionAdapterOfPrayerLogEntity = new EntityDeletionOrUpdateAdapter<PrayerLogEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `prayer_logs` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PrayerLogEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfPrayerLogEntity = new EntityDeletionOrUpdateAdapter<PrayerLogEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `prayer_logs` SET `id` = ?,`date` = ?,`prayerName` = ?,`isCompleted` = ?,`completedAt` = ?,`userId` = ?,`createdAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final PrayerLogEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getDate());
        statement.bindString(3, entity.getPrayerName());
        final int _tmp = entity.isCompleted() ? 1 : 0;
        statement.bindLong(4, _tmp);
        if (entity.getCompletedAt() == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, entity.getCompletedAt());
        }
        if (entity.getUserId() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getUserId());
        }
        statement.bindLong(7, entity.getCreatedAt());
        statement.bindLong(8, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteExpiredPrayerTimes = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM prayer_times WHERE cachedAt < ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOldPrayerLogs = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM prayer_logs WHERE date < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertPrayerTimes(final PrayerTimesEntity prayerTimes,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfPrayerTimesEntity.insert(prayerTimes);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertPrayerLog(final PrayerLogEntity prayerLog,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfPrayerLogEntity.insert(prayerLog);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deletePrayerLog(final PrayerLogEntity prayerLog,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfPrayerLogEntity.handle(prayerLog);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updatePrayerLog(final PrayerLogEntity prayerLog,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfPrayerLogEntity.handle(prayerLog);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteExpiredPrayerTimes(final long expireTime,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteExpiredPrayerTimes.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, expireTime);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteExpiredPrayerTimes.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldPrayerLogs(final String cutoffDate,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldPrayerLogs.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, cutoffDate);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldPrayerLogs.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getPrayerTimes(final String date,
      final Continuation<? super PrayerTimesEntity> $completion) {
    final String _sql = "SELECT * FROM prayer_times WHERE date = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, date);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<PrayerTimesEntity>() {
      @Override
      @Nullable
      public PrayerTimesEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfFajr = CursorUtil.getColumnIndexOrThrow(_cursor, "fajr");
          final int _cursorIndexOfSunrise = CursorUtil.getColumnIndexOrThrow(_cursor, "sunrise");
          final int _cursorIndexOfDhuhr = CursorUtil.getColumnIndexOrThrow(_cursor, "dhuhr");
          final int _cursorIndexOfAsr = CursorUtil.getColumnIndexOrThrow(_cursor, "asr");
          final int _cursorIndexOfMaghrib = CursorUtil.getColumnIndexOrThrow(_cursor, "maghrib");
          final int _cursorIndexOfIsha = CursorUtil.getColumnIndexOrThrow(_cursor, "isha");
          final int _cursorIndexOfHijriDate = CursorUtil.getColumnIndexOrThrow(_cursor, "hijriDate");
          final int _cursorIndexOfCity = CursorUtil.getColumnIndexOrThrow(_cursor, "city");
          final int _cursorIndexOfCountry = CursorUtil.getColumnIndexOrThrow(_cursor, "country");
          final int _cursorIndexOfLatitude = CursorUtil.getColumnIndexOrThrow(_cursor, "latitude");
          final int _cursorIndexOfLongitude = CursorUtil.getColumnIndexOrThrow(_cursor, "longitude");
          final int _cursorIndexOfTimezone = CursorUtil.getColumnIndexOrThrow(_cursor, "timezone");
          final int _cursorIndexOfMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "method");
          final int _cursorIndexOfCachedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "cachedAt");
          final PrayerTimesEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpDate;
            _tmpDate = _cursor.getString(_cursorIndexOfDate);
            final String _tmpFajr;
            _tmpFajr = _cursor.getString(_cursorIndexOfFajr);
            final String _tmpSunrise;
            _tmpSunrise = _cursor.getString(_cursorIndexOfSunrise);
            final String _tmpDhuhr;
            _tmpDhuhr = _cursor.getString(_cursorIndexOfDhuhr);
            final String _tmpAsr;
            _tmpAsr = _cursor.getString(_cursorIndexOfAsr);
            final String _tmpMaghrib;
            _tmpMaghrib = _cursor.getString(_cursorIndexOfMaghrib);
            final String _tmpIsha;
            _tmpIsha = _cursor.getString(_cursorIndexOfIsha);
            final String _tmpHijriDate;
            _tmpHijriDate = _cursor.getString(_cursorIndexOfHijriDate);
            final String _tmpCity;
            _tmpCity = _cursor.getString(_cursorIndexOfCity);
            final String _tmpCountry;
            _tmpCountry = _cursor.getString(_cursorIndexOfCountry);
            final double _tmpLatitude;
            _tmpLatitude = _cursor.getDouble(_cursorIndexOfLatitude);
            final double _tmpLongitude;
            _tmpLongitude = _cursor.getDouble(_cursorIndexOfLongitude);
            final String _tmpTimezone;
            _tmpTimezone = _cursor.getString(_cursorIndexOfTimezone);
            final int _tmpMethod;
            _tmpMethod = _cursor.getInt(_cursorIndexOfMethod);
            final long _tmpCachedAt;
            _tmpCachedAt = _cursor.getLong(_cursorIndexOfCachedAt);
            _result = new PrayerTimesEntity(_tmpDate,_tmpFajr,_tmpSunrise,_tmpDhuhr,_tmpAsr,_tmpMaghrib,_tmpIsha,_tmpHijriDate,_tmpCity,_tmpCountry,_tmpLatitude,_tmpLongitude,_tmpTimezone,_tmpMethod,_tmpCachedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<PrayerLogEntity>> getPrayerLogsForDate(final String date) {
    final String _sql = "SELECT * FROM prayer_logs WHERE date = ? ORDER BY createdAt ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, date);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"prayer_logs"}, new Callable<List<PrayerLogEntity>>() {
      @Override
      @NonNull
      public List<PrayerLogEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfPrayerName = CursorUtil.getColumnIndexOrThrow(_cursor, "prayerName");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final List<PrayerLogEntity> _result = new ArrayList<PrayerLogEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PrayerLogEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpDate;
            _tmpDate = _cursor.getString(_cursorIndexOfDate);
            final String _tmpPrayerName;
            _tmpPrayerName = _cursor.getString(_cursorIndexOfPrayerName);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final Long _tmpCompletedAt;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmpCompletedAt = null;
            } else {
              _tmpCompletedAt = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item = new PrayerLogEntity(_tmpId,_tmpDate,_tmpPrayerName,_tmpIsCompleted,_tmpCompletedAt,_tmpUserId,_tmpCreatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<PrayerLogEntity>> getPrayerLogsForDateRange(final String startDate,
      final String endDate) {
    final String _sql = "SELECT * FROM prayer_logs WHERE date BETWEEN ? AND ? ORDER BY date ASC, createdAt ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindString(_argIndex, endDate);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"prayer_logs"}, new Callable<List<PrayerLogEntity>>() {
      @Override
      @NonNull
      public List<PrayerLogEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfPrayerName = CursorUtil.getColumnIndexOrThrow(_cursor, "prayerName");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final List<PrayerLogEntity> _result = new ArrayList<PrayerLogEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PrayerLogEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpDate;
            _tmpDate = _cursor.getString(_cursorIndexOfDate);
            final String _tmpPrayerName;
            _tmpPrayerName = _cursor.getString(_cursorIndexOfPrayerName);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final Long _tmpCompletedAt;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmpCompletedAt = null;
            } else {
              _tmpCompletedAt = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item = new PrayerLogEntity(_tmpId,_tmpDate,_tmpPrayerName,_tmpIsCompleted,_tmpCompletedAt,_tmpUserId,_tmpCreatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getPrayerLogForPrayerAndDate(final String prayerName, final String date,
      final Continuation<? super PrayerLogEntity> $completion) {
    final String _sql = "SELECT * FROM prayer_logs WHERE prayerName = ? AND date = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, prayerName);
    _argIndex = 2;
    _statement.bindString(_argIndex, date);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<PrayerLogEntity>() {
      @Override
      @Nullable
      public PrayerLogEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfPrayerName = CursorUtil.getColumnIndexOrThrow(_cursor, "prayerName");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfCompletedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "completedAt");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final PrayerLogEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpDate;
            _tmpDate = _cursor.getString(_cursorIndexOfDate);
            final String _tmpPrayerName;
            _tmpPrayerName = _cursor.getString(_cursorIndexOfPrayerName);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final Long _tmpCompletedAt;
            if (_cursor.isNull(_cursorIndexOfCompletedAt)) {
              _tmpCompletedAt = null;
            } else {
              _tmpCompletedAt = _cursor.getLong(_cursorIndexOfCompletedAt);
            }
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _result = new PrayerLogEntity(_tmpId,_tmpDate,_tmpPrayerName,_tmpIsCompleted,_tmpCompletedAt,_tmpUserId,_tmpCreatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getCompletedPrayersCount(final String startDate, final String endDate,
      final Continuation<? super Integer> $completion) {
    final String _sql = "\n"
            + "        SELECT COUNT(*) FROM prayer_logs\n"
            + "        WHERE isCompleted = 1 AND date BETWEEN ? AND ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindString(_argIndex, endDate);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalPrayersCount(final String startDate, final String endDate,
      final Continuation<? super Integer> $completion) {
    final String _sql = "\n"
            + "        SELECT COUNT(*) FROM prayer_logs\n"
            + "        WHERE date BETWEEN ? AND ?\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindString(_argIndex, endDate);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp;
          } else {
            _result = 0;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getPrayerStatistics(final String startDate, final String endDate,
      final Continuation<? super List<PrayerStatistic>> $completion) {
    final String _sql = "\n"
            + "        SELECT prayerName, COUNT(*) as count FROM prayer_logs\n"
            + "        WHERE isCompleted = 1 AND date BETWEEN ? AND ?\n"
            + "        GROUP BY prayerName\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindString(_argIndex, endDate);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<PrayerStatistic>>() {
      @Override
      @NonNull
      public List<PrayerStatistic> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfPrayerName = 0;
          final int _cursorIndexOfCount = 1;
          final List<PrayerStatistic> _result = new ArrayList<PrayerStatistic>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final PrayerStatistic _item;
            final String _tmpPrayerName;
            _tmpPrayerName = _cursor.getString(_cursorIndexOfPrayerName);
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            _item = new PrayerStatistic(_tmpPrayerName,_tmpCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getDailyPrayerCounts(final String startDate, final String endDate,
      final Continuation<? super List<DailyPrayerCount>> $completion) {
    final String _sql = "\n"
            + "        SELECT date, COUNT(*) as count FROM prayer_logs\n"
            + "        WHERE isCompleted = 1 AND date BETWEEN ? AND ?\n"
            + "        GROUP BY date ORDER BY date ASC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, startDate);
    _argIndex = 2;
    _statement.bindString(_argIndex, endDate);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<DailyPrayerCount>>() {
      @Override
      @NonNull
      public List<DailyPrayerCount> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfDate = 0;
          final int _cursorIndexOfCount = 1;
          final List<DailyPrayerCount> _result = new ArrayList<DailyPrayerCount>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final DailyPrayerCount _item;
            final String _tmpDate;
            _tmpDate = _cursor.getString(_cursorIndexOfDate);
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            _item = new DailyPrayerCount(_tmpDate,_tmpCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getLastPrayerDate(final Continuation<? super String> $completion) {
    final String _sql = "SELECT MAX(date) FROM prayer_logs WHERE isCompleted = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<String>() {
      @Override
      @Nullable
      public String call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final String _result;
          if (_cursor.moveToFirst()) {
            final String _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
