package com.muslimcore.presentation.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.muslimcore.domain.models.LocationResult
import com.muslimcore.domain.models.LocationState
import com.muslimcore.domain.models.UserLocation
import com.muslimcore.domain.repositories.LocationRepository
import com.muslimcore.domain.usecases.GetCurrentLocationUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel for handling location operations in Prayer-related screens
 * Uses professional location architecture with proper state management
 */
@HiltViewModel
class PrayerLocationViewModel @Inject constructor(
    private val locationRepository: LocationRepository,
    private val getCurrentLocationUseCase: GetCurrentLocationUseCase
) : ViewModel() {

    data class UiState(
        val isLocationButtonEnabled: Boolean = true,
        val locationButtonText: String = "Use Current Location",
        val showLocationPicker: Boolean = false,
        val error: String? = null
    )

    private val _uiState = MutableStateFlow(UiState())
    val uiState: StateFlow<UiState> = _uiState.asStateFlow()

    // Expose location state from repository
    val locationState: StateFlow<LocationState> = locationRepository.locationState

    private var lastLocationRequest = 0L
    private val debounceTimeMs = 2000L // 2 seconds

    init {
        // Initialize with cached location if available
        viewModelScope.launch {
            val cached = locationRepository.getCachedLocation()
            if (cached != null) {
                updateLocationButtonText(cached)
            }
        }
    }

    /**
     * Handle location button click with debouncing and proper state management
     */
    fun onLocationButtonClicked() {
        val currentTime = System.currentTimeMillis()
        
        // Debounce rapid clicks
        if (currentTime - lastLocationRequest < debounceTimeMs) {
            return
        }
        
        lastLocationRequest = currentTime

        if (!locationRepository.hasLocationPermission()) {
            _uiState.value = _uiState.value.copy(
                error = "Location permission required. Please grant permission in settings."
            )
            return
        }

        viewModelScope.launch {
            try {
                // Disable button during request
                _uiState.value = _uiState.value.copy(
                    isLocationButtonEnabled = false,
                    locationButtonText = "🌍 Getting location...",
                    error = null
                )

                val result = getCurrentLocationUseCase.executeWithRetry()
                
                when (result) {
                    is LocationResult.Success -> {
                        updateLocationButtonText(result.location)
                        _uiState.value = _uiState.value.copy(
                            isLocationButtonEnabled = true,
                            error = null
                        )
                    }
                    is LocationResult.Error -> {
                        _uiState.value = _uiState.value.copy(
                            isLocationButtonEnabled = true,
                            locationButtonText = "Select Location",
                            error = "Failed to get location: ${result.message}"
                        )
                    }
                    is LocationResult.PermissionDenied -> {
                        _uiState.value = _uiState.value.copy(
                            isLocationButtonEnabled = true,
                            locationButtonText = "Select Location",
                            error = "Location permission denied"
                        )
                    }
                    is LocationResult.Timeout -> {
                        _uiState.value = _uiState.value.copy(
                            isLocationButtonEnabled = true,
                            locationButtonText = "Select Location",
                            error = "Location request timed out"
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLocationButtonEnabled = true,
                    locationButtonText = "Select Location",
                    error = "Location error: ${e.message}"
                )
            }
        }
    }

    /**
     * Show location picker dialog
     */
    fun showLocationPicker() {
        _uiState.value = _uiState.value.copy(showLocationPicker = true)
    }

    /**
     * Hide location picker dialog
     */
    fun hideLocationPicker() {
        _uiState.value = _uiState.value.copy(showLocationPicker = false)
    }

    /**
     * Search for location by name
     */
    fun searchLocation(query: String) {
        viewModelScope.launch {
            try {
                val result = locationRepository.searchLocation(query)
                when (result) {
                    is LocationResult.Success -> {
                        updateLocationButtonText(result.location)
                        hideLocationPicker()
                    }
                    is LocationResult.Error -> {
                        _uiState.value = _uiState.value.copy(
                            error = "Location not found: ${result.message}"
                        )
                    }
                    else -> {
                        _uiState.value = _uiState.value.copy(
                            error = "Failed to search location"
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Search error: ${e.message}"
                )
            }
        }
    }

    /**
     * Clear error message
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    /**
     * Get current location for prayer times calculation
     */
    suspend fun getCurrentLocationForPrayer(): UserLocation? {
        return when (val result = getCurrentLocationUseCase.execute()) {
            is LocationResult.Success -> result.location
            else -> locationRepository.getCachedLocation() ?: locationRepository.getDefaultLocation()
        }
    }

    private fun updateLocationButtonText(location: UserLocation) {
        _uiState.value = _uiState.value.copy(
            locationButtonText = location.city
        )
    }
}
