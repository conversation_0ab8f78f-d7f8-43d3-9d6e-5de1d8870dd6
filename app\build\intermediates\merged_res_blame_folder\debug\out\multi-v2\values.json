{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.12\\com.muslimcore.app-mergeDebugResources-3:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e661d63d9c04259b2495b443a4caf917\\transformed\\window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1"}, "to": {"startLines": "531,536,537,538,1935", "startColumns": "4,4,4,4,4", "startOffsets": "27008,27187,27247,27299,127828", "endLines": "535,536,537,538,1935", "endColumns": "11,59,51,44,59", "endOffsets": "27182,27242,27294,27339,127883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\23d7c820469d7d72a3f613bbedf53593\\transformed\\material-1.10.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "10,16,27,30,31,32,33,34,37,38,39,40,41,43,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,117,118,140,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,226,228,229,231,232,233,234,235,236,237,238,388,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,468,469,470,496,509,510,511,512,513,514,515,516,517,518,519,520,524,525,526,527,529,539,540,541,542,543,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,584,585,596,606,607,645,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1163,1169,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1894,1895,1947,1948,1949,1951,1952,1953,1954,1955,1956,1959,1960,1961,1962,1963,1964,1975,1978,1981,1982,2001,2002,2003,2004,2005,2006,2007,2015,2024,2025,2030,2031,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2093,2095,2142,2148,2149,2150,2151,2152,2153,2170,2171,2172,2173,2206,2210,2215,2216,2217,2229,2231,2232,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2293,2296,2299,2300,2301,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2345,2346,2347,2348,2369,2370,2371,2372,2373,2407,2410,2411,2413,2414,2447,2451,2455,2459,2463,2464,2507,2515,2522,2692,2695,2705,2714,2723,2792,2793,2794,2795,2801,2802,2803,2804,2805,2806,2812,2813,2814,2815,2816,2821,2822,2826,2827,2833,2837,2838,2839,2840,2850,2851,2852,2856,2857,2863,2867,2940,2943,2944,2948,2949,2952,2953,2954,2955,3218,3225,3485,3491,3754,3761,4021,4027,4090,4172,4224,4306,4368,4450,4514,4566,4648,4656,4662,4673,4677,4681,4694,5469,5485,5492,5498,5515,5528,5548,5565,5574,5579,5586,5606,5619,5636,5642,5648,5655,5659,5665,5679,5682,5692,5693,5694,5742,5746,5750,5754,5755,5756,5759,5775,5782,5796,5841,5871,5877,5881,5885,5890,5897,5903,5904,5907,5911,5916,5929,5933,5938,5943,5948,5951,5954,5957,5961,6104,6105,6106,6107,6335,6336,6337,6338,6339,6340,6341,6342,6343,6344,6345,6346,6347,6348,6349,6350,6351,6352,6353,6357,6361,6365,6369,6373,6377,6381,6382,6383,6384,6385,6386,6387,6388,6392,6396,6397,6401,6402,6405,6409,6412,6415,6418,6434,6437,6440,6444,6448,6452,6456,6459,6460,6461,6462,6465,6469,6472,6475,6478,6481,6484,6487,6558,6561,6562,6565,6568,6569,6572,6573,6574,6578,6579,6584,6591,6598,6605,6612,6619,6626,6633,6640,6647,6656,6665,6674,6681,6690,6699,6702,6705,6706,6707,6708,6709,6710,6711,6712,6713,6714,6715,6716,6717,6721,6726,6731,6734,6735,6736,6737,6738,6746,6754,6755,6763,6767,6775,6783,6791,6799,6807,6808,6816,6824,6825,6828,6907,6909,6914,6916,6921,6925,6929,6930,6931,6932,6936,6940,6941,6945,6946,6947,6948,6949,6950,6951,6952,6953,6954,6955,6956,6957,6958,6959,6960,6964,6968,6969,6973,6974,6975,6980,6981,6982,6983,6984,6985,6986,6987,6988,6989,6990,6991,6992,6993,6994,6995,6996,6997,6998,6999,7000,7004,7005,7006,7012,7013,7017,7019,7020,7025,7026,7027,7028,7029,7030,7034,7035,7036,7042,7043,7047,7049,7053,7057,7061,7168,7169,7170,7171,7174,7177,7180,7183,7186,7191,7195,7198,7199,7204,7208,7213,7219,7225,7230,7234,7239,7243,7247,7288,7289,7290,7291,7292,7296,7297,7298,7299,7303,7307,7311,7315,7319,7323,7327,7331,7337,7338,7379,7393,7398,7424,7431,7434,7445,7450,7453,7456,7511,7517,7518,7521,7524,7527,7530,7533,7536,7539,7543,7546,7547,7548,7556,7564,7567,7572,7577,7582,7587,7591,7595,7596,7604,7605,7606,7607,7608,7616,7621,7626,7627,7628,7629,7654,7660,7665,7668,7672,7675,7679,7689,7692,7697,7700,7704,7805,7813,7827,7840,7844,7859,7870,7873,7884,7889,7893,7928,7929,7930,7941,7948,7955,7962,7969,7989,7992,8019,8024,8044,8047,8054,8067,8076,8079,8099,8109,8113,8117,8130,8134,8138,8142,8148,8152,8169,8177,8181,8185,8189,8192,8196,8200,8204,8214,8221,8228,8232,8258,8268,8293,8302,8322,8332,8336,8346,8371,8381,8384,8388,8389,8390,8391,8395,8401,8407,8408,8421,8422,8423,8426,8429,8432,8435,8438,8441,8444,8447,8450,8453,8456,8459,8462,8465,8468,8471,8474,8477,8480,8483,8486,8487,8492,8493,8506,8516,8520,8525,8530,8534,8537,8541,8545,8548,8552,8555,8559,8564,8569,8572,8579,8583,8587,8596,8601,8606,8607,8611,8614,8618,8631,8636,8644,8648,8652,8669,8673,8678,8696,8703,8706,8736,8739,8742,8745,8748,8751,8754,8773,8779,8787,8794,8806,8814,8819,8823,8827,8838,8842,8850,8853,8858,8859,8860,8861,8865,8869,8873,8877,8912,8915,8919,8923,8957,8960,8964,8968,8977,8983,8986,8996,9000,9001,9008,9012,9019,9020,9021,9024,9029,9034,9035,9039,9054,9073,9077,9078,9090,9100,9101,9113,9118,9142,9145,9151,9154,9163,9171,9175,9178,9181,9184,9188,9191,9208,9212,9215,9230,9233,9241,9246,9253,9258,9259,9264,9265,9271,9277,9283,9315,9326,9343,9350,9354,9357,9370,9379,9383,9388,9392,9396,9400,9404,9408,9412,9416,9421,9424,9436,9441,9450,9453,9460,9461,9465,9474,9480,9484,9485,9489,9510,9516,9520,9524,9525,9543,9544,9545,9546,9547,9552,9555,9556,9562,9563,9575,9587,9594,9595,9600,9605,9606,9610,9624,9629,9635,9641,9647,9652,9658,9664,9665,9671,9686,9691,9700,9709,9712,9726,9731,9742,9746,9755,9764,9765,9772,14521,14522,14523,14524,14525,14526,14527,14528,14529,14530,14531,14532,14533,14534,14535,14536,14537,14538,14539,14540,14541,14542,14543,14544,14545,14546,14547,14548,14549,14550,14551,14552,14553,14554,14555,14556,14557,14558,14559,14560,14561,14562,14563,14564,14565,14566,14567,14568,14569,14570,14571,14572,14573,14574,14575,14576,14577,14578,14579,14580,14581,14582,14583,14584,14585,14586,14587,14588,14589,14590,14591,14592,14593,14594,14595,14596,14597,14598,14599,14600,14601,14602,14603,14604,14605,14606,14607,14608,14609,14610,14611,14612,14613,14614,14615,14616,14617,14618,14619,14620,14621,14622,14623,14624,14625,14626,14627,14628,14629,14630,14631,14632,14633,14634,14635,14636,14637,14638,14639,14640,14641,14642,14643,14644,14645,14646,14647,14648,14649,14650,14651,14652,14653,14654,14655,14656,14657,14658,14659,14660,14661,14662,14663,14664,14665,14666,14667,14668,14669,14670,14671,14672,14673,14674,14675,14676,14677,14678,14679,14680,14681,14682,14683,14684,14685,14686,14687,14688,14689,14690,14691,14692,14693,14694,14695,14696,14697,14698,14699,14700,14701,14702,14703,14704,14705,14706,14707,14708,14709,14710,14711,14712,14713,14714,14715,14716,14717,14718,14719,14720,14721,14722,14723,14724,14725,14726,14727,14728,14729,14730,14731,14732,14733,14734,14735,14736,14737,14738,14739,14740,14741,14742,14743,14744,14745,14746,14747,14748,14749,14750,14751,14752,14753,14754,14755,14756,14757,14758,14759,14760,14761,14762,14763,14764,14765,14766,14767,14768,14769,14770,14771,14772,14773,14774,14775,14776,14777,14778,14779,14780,14781,14782,14783,14784,14785,14786,14787,14788,14789,14790,14791,14792,14793,14794,14795,14796,14797,14798,14799,14800,14801,14802,14803,14804,14805,14806,14807,14808,14809,14810,14811,14812,14813,14814,14815,14816,14817,14818,14819,14820,14821,14822,14823,14824,14825,14826,14827,14828,14829,14830,14831,14832,14833,14834,14835,14836,14837,14838,14839,14840,14841,14842,14843,14844,14845,14846,14847,14848,14849,14850,14851,14852,14853,14854,14855,14856,14857,14858,14859,14860,14861,14862,14863,14864,14865,14866,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "490,732,1185,1343,1399,1459,1520,1585,1740,1790,1840,1893,1951,2050,2420,2468,2539,2611,2683,2756,2823,2872,2926,2963,3014,3074,3121,3177,3226,3284,3338,3399,3455,3506,3566,3622,3685,3734,3790,3846,3896,3955,4010,4072,4119,4173,4229,4281,4336,4390,4444,4498,4547,4605,4659,4716,4772,4819,4872,4928,4988,5051,5110,5172,5222,5276,5330,5378,5435,5488,6112,6166,7097,7208,7270,7326,7386,7439,7500,7579,7660,7732,7811,7891,7967,8045,8114,8190,8267,8338,8411,8487,8565,8634,8710,8787,8851,8922,11435,11531,11584,11688,11755,11808,11860,11910,11968,12033,12081,19022,19152,19218,19276,19345,19403,19472,19542,19615,19689,19757,19824,19894,19960,20033,20093,20169,20229,20289,20364,20432,20498,20566,20626,20685,20742,20808,20870,20927,20995,21068,21138,21200,21261,21329,21391,21461,21530,21586,21645,21707,21769,21836,21893,21954,22015,22076,22137,22193,22249,22305,22361,22419,22477,22535,22593,22650,22707,22764,22821,22880,22939,22997,23080,23163,23236,23290,23359,23415,23496,23577,23648,23983,24036,24094,25181,25800,25846,25906,25960,26030,26100,26165,26231,26296,26364,26433,26501,26631,26684,26743,26801,26899,27344,27396,27442,27492,27548,27642,27700,27758,27820,27883,27945,28004,28064,28129,28195,28260,28322,28384,28446,28508,28570,28632,28698,28765,28831,28894,28958,29021,29089,29150,29212,29274,29337,29401,29464,29528,29606,29665,29731,29811,29872,30076,30134,30567,31018,31082,32822,37093,37167,37238,37304,37378,37447,37518,37591,37662,37730,37803,37879,37949,38027,38095,38161,38222,38291,38355,38421,38489,38555,38618,38686,38757,38822,38895,38958,39039,39103,39169,39239,39309,39379,39449,41277,41334,41392,41451,41511,41570,41629,41688,41747,41806,41865,41924,41983,42042,42101,42161,42222,42284,42345,42406,42467,42528,42589,42650,42710,42771,42832,42892,42953,43014,43075,43136,43197,43258,43319,43380,43441,43502,43563,43631,43700,43770,43839,43908,43977,44046,44115,44184,44253,44322,44391,44460,44520,44581,44643,44704,44765,44826,44887,44948,45009,45070,45131,45192,45253,45315,45378,45442,45505,45568,45631,45694,45757,45820,45883,45946,46009,46072,46133,46195,46258,46320,46382,46444,46506,46568,46630,46692,46754,46816,46878,46935,47022,47102,47192,47287,47379,47471,47561,47644,47737,47824,47921,48012,48113,48200,48303,48392,48491,48583,48683,48767,48861,48949,49047,49131,49222,49316,49415,49517,49615,49715,49802,49902,49988,50084,50172,50253,50344,50440,50533,50626,50717,50802,50896,50985,51083,51176,51278,51366,51470,51561,51661,51754,51855,51940,52035,52124,52223,52308,52400,52495,52595,52698,52797,52900,52989,53090,53177,53274,53362,53458,53550,53650,53740,53838,53923,54012,54101,54194,54281,55044,55110,55186,55255,55334,55407,55487,55567,55644,55712,55790,55866,55937,56018,56091,56174,56249,56334,56407,56488,56569,56643,56727,56797,56875,56945,57025,57103,57175,57257,57327,57404,57484,57569,57657,57741,57828,57902,57980,58058,58129,58210,58301,58384,58480,58578,58685,58750,58816,58869,58945,59011,59098,59174,68769,69147,69742,69796,69875,69953,70026,70091,70154,70220,70291,70362,70432,70494,70563,70629,70689,70756,70823,70879,70930,70983,71035,71089,71160,71223,71282,71344,71403,71476,71543,71613,71673,71736,71811,71883,71979,72050,72106,72177,72234,72291,72357,72421,72492,72549,72602,72665,72717,72775,74012,74081,74147,74206,74289,74348,74405,74472,74542,74616,74678,74747,74817,74916,75013,75112,75198,75284,75365,75440,75529,75620,75704,75763,75809,75875,75932,75999,76056,76138,76203,76269,76392,76476,76597,76662,76724,76822,76896,76979,77068,77132,77211,77285,77347,77443,77508,77567,77623,77679,77739,77846,77893,77953,78014,78078,78139,78199,78257,78300,78349,78401,78452,78504,78553,78602,78667,78733,78793,78854,78910,78969,79018,79066,79124,79181,79283,79340,79415,79463,79514,79576,79641,79693,79767,79830,79893,79961,80011,80073,80133,80190,80250,80299,80367,80473,80575,80644,80715,80771,80820,80920,80991,81101,81190,81281,81363,81461,81517,81618,81728,81827,81890,81996,82073,82185,82312,82424,82551,82621,82735,82866,82963,83031,83149,83252,83370,83431,83505,83572,83677,83799,83873,83940,84050,84149,84222,84319,84441,84559,84677,84738,84860,84977,85045,85151,85253,85333,85404,85500,85567,85641,85715,85801,85889,85979,86057,86134,86234,86305,86426,86547,86611,86736,86810,86934,87058,87125,87234,87362,87474,87553,87631,87732,87803,87925,88047,88112,88238,88350,88456,88524,88623,88727,88790,88856,88940,89053,89166,89284,89362,89434,89570,89706,89791,89931,90069,90207,90349,90431,90540,90651,90779,90907,91039,91169,91299,91433,91495,91591,91658,91775,91896,91993,92075,92162,92249,92380,92511,92646,92723,92800,92911,93025,93099,93208,93320,93422,93518,93622,93689,93783,93855,93965,94071,94144,94235,94337,94440,94535,94642,94747,94869,94991,95117,95176,95234,95358,95482,95610,95728,95846,95968,96054,96151,96285,96419,96499,96637,96769,96901,97037,97112,97188,97291,97365,97478,97559,97616,97677,97736,97796,97854,97915,97973,98023,98072,98139,98198,98257,98306,98377,98461,98532,98612,98681,98744,98812,98878,98946,99011,99077,99154,99232,99338,99444,99540,99669,99758,99885,99951,100020,100106,100172,100255,100353,100449,100545,100643,100752,100847,100936,100998,101058,101123,101180,101261,101315,101372,101469,101579,101640,101755,101876,101971,102063,102156,102258,102314,102373,102422,102514,102563,102617,102671,102725,102779,102833,102888,102998,103108,103216,103326,103436,103546,103656,103764,103870,103974,104078,104182,104277,104372,104465,104558,104662,104768,104872,104976,105069,105162,105255,105348,105456,105562,105668,105774,105871,105966,106061,106156,106262,106368,106474,106580,106678,106774,106870,106968,107033,107137,107195,107259,107320,107382,107442,107504,107572,107630,107693,107756,107823,107898,107971,108037,108089,108142,108194,108251,108335,108430,108515,108596,108676,108753,108832,108909,108983,109057,109128,109208,109280,109355,109420,109481,109541,109616,109690,109767,109840,109910,109982,110052,110125,110189,110259,110305,110374,110426,110511,110594,110651,110717,110784,110850,110931,111006,111062,111115,111176,111234,111284,111333,111382,111431,111493,111545,111590,111671,111722,111776,111829,111883,111934,111983,112049,112100,112161,112222,112284,112334,112375,112452,112511,112570,112629,112690,112746,112802,112869,112930,112995,113050,113115,113184,113252,113330,113399,113459,113530,113604,113669,113741,113811,113878,113962,114031,114098,114168,114231,114298,114366,114449,114528,114618,114695,114763,114830,114908,114965,115022,115090,115156,115212,115272,115331,115385,115435,115485,115533,115595,115646,115719,115799,115879,115943,116010,116081,116139,116200,116266,116325,116392,116452,116512,116575,116643,116704,116771,116849,116919,116968,117025,117094,117155,117243,117331,117419,117507,117594,117681,117768,117855,117913,117987,118057,118113,118184,118249,118311,118386,118459,118549,118615,118681,118742,118806,118868,118926,118997,119080,119139,119210,119276,119341,119402,119461,119532,119598,119663,119746,119822,119897,119978,120038,120107,120177,120246,120301,120357,120413,120474,120532,120588,120642,120697,120759,120816,120910,120979,121080,121131,121201,121264,121320,121378,121437,121491,121577,121661,121731,121800,121870,121985,122106,122173,122240,122315,122382,122441,122495,122549,122603,122656,122708,125223,125360,128402,128451,128501,128592,128640,128696,128754,128816,128871,129039,129110,129174,129233,129295,129361,129932,130077,130229,130274,131293,131344,131391,131436,131487,131538,131589,131973,132463,132529,132829,132892,133032,133089,133143,133198,133256,133311,133370,133426,133495,133564,133633,133703,133766,133829,133892,133955,134020,134085,134150,134215,134278,134342,134406,134470,134521,134599,134677,134748,134820,134893,134965,135031,135097,135165,135233,135299,135366,135440,135503,135560,135620,135685,135752,135817,135874,135935,135993,136097,136207,136316,136420,136498,136563,136630,136696,136766,136813,136865,137041,137168,140334,140735,140866,141050,141228,141466,141655,142870,142968,143083,143168,146332,146574,146961,147050,147207,148270,148484,148638,149149,149336,149432,149522,149618,149708,149874,149997,150120,150290,150396,150511,150626,150728,150834,150951,151108,151190,151363,151531,151679,151838,151993,152166,152283,152400,152568,152680,152794,152966,153142,153300,153433,153545,153691,153843,153975,154118,154240,154418,154554,154650,154786,154881,155048,155141,155233,155420,155576,155754,155918,156100,156417,156599,156781,156971,157203,157393,157570,157732,157889,157999,158182,158319,158539,158723,158907,159067,159225,159409,159636,159839,160010,160230,160452,160607,160807,160991,161094,161284,161425,161590,161761,161961,162165,162367,162532,162737,162936,163135,163332,163423,163572,163722,163806,163955,164100,164252,164393,164559,165841,165919,166220,166386,166541,168530,168688,168852,169078,169301,171396,171673,171945,172223,172468,172530,175358,175809,176265,187402,187550,188064,188501,188935,193275,193360,193481,193580,193985,194082,194199,194286,194409,194510,194916,195015,195134,195227,195334,195677,195784,196029,196150,196559,196807,196907,197012,197131,197640,197787,197906,198157,198290,198705,198959,204440,204687,204812,205129,205250,205478,205599,205732,205879,226510,227002,247382,247806,268482,268976,289401,289827,294668,300085,304176,309607,314349,319726,323710,327702,333093,333640,334073,334829,335059,335302,336435,384605,385509,386063,386516,387946,388690,389883,390937,391415,391708,392091,393606,394371,395514,395955,396396,396962,397236,397647,398663,398841,399594,399731,399822,402016,402282,402604,402814,402923,403042,403226,404344,404814,405565,408148,410229,410605,410833,411089,411348,411924,412278,412400,412539,412831,413091,414019,414305,414708,415110,415453,415665,415866,416079,416368,427255,427328,427415,427500,440025,440137,440243,440366,440498,440621,440751,440875,441008,441139,441264,441381,441501,441633,441761,441875,441993,442106,442227,442415,442602,442783,442966,443150,443315,443497,443617,443737,443845,443955,444067,444175,444285,444450,444616,444768,444933,445034,445154,445325,445486,445649,445810,446623,446742,446859,447039,447221,447402,447585,447740,447885,448007,448142,448305,448498,448624,448776,448918,449088,449244,449416,456179,456374,456466,456639,456801,456896,457065,457159,457248,457491,457580,457873,458289,458709,459130,459556,459973,460389,460806,461224,461638,462108,462581,463053,463464,463935,464407,464597,464803,464909,465017,465123,465235,465349,465461,465575,465691,465805,465913,466023,466131,466393,466772,467176,467323,467431,467541,467649,467763,468172,468586,468702,469120,469361,469791,470226,470636,471058,471468,471590,471999,472415,472537,472755,477914,477982,478326,478406,478762,478912,479056,479132,479244,479334,479596,479861,479969,480121,480229,480305,480417,480507,480609,480717,480825,480925,481033,481118,481222,481309,481387,481501,481593,481857,482124,482234,482387,482497,482581,482970,483068,483176,483270,483400,483508,483630,483766,483874,483994,484128,484250,484378,484520,484646,484786,484912,485030,485162,485260,485370,485670,485782,485900,486364,486480,486783,486909,487005,487406,487516,487640,487778,487888,488010,488322,488446,488576,489052,489180,489495,489633,489795,490011,490167,496231,496299,496383,496487,496690,496879,497080,497273,497478,497791,498003,498167,498283,498529,498745,499058,499484,499946,500183,500335,500595,500739,500881,504113,504227,504347,504463,504557,504878,504977,505095,505196,505475,505760,506039,506321,506574,506833,507086,507342,507766,507842,511092,512447,512891,514745,515320,515528,516538,516918,517084,517225,522245,522671,522783,522918,523071,523268,523439,523622,523797,523984,524256,524414,524498,524602,525089,525645,525803,526022,526253,526476,526711,526933,527199,527337,527936,528050,528188,528300,528424,528995,529490,530036,530181,530274,530366,532293,532863,533161,533350,533556,533749,533959,534843,534988,535380,535538,535755,543811,544243,545118,545738,545935,546883,547648,547771,548544,548765,548965,550942,551042,551132,551717,552387,553069,553749,554441,555654,555819,557432,557753,558816,558986,559556,560451,561084,561250,562736,563352,563588,563809,564767,565032,565297,565544,565958,566194,567479,567928,568115,568364,568606,568782,569023,569256,569481,570076,570551,571075,571336,572687,573162,574388,574858,575906,576358,576602,577059,578304,578787,578937,579281,579427,579565,579701,579989,580493,581002,581118,582020,582142,582254,582431,582697,582967,583233,583501,583757,584017,584273,584531,584783,585039,585291,585545,585777,586013,586265,586521,586773,587027,587259,587493,587605,588030,588154,589246,590061,590257,590581,590970,591322,591563,591777,592076,592268,592583,592790,593136,593436,593837,594056,594469,594706,595076,595800,596155,596424,596564,596818,596962,597239,598231,598640,599272,599618,599986,601060,601423,601823,603331,603916,604141,606670,606864,607082,607308,607520,607719,607926,609130,609425,609982,610372,611004,611472,611717,611974,612220,612980,613244,613667,613858,614237,614325,614433,614541,614854,615179,615498,615829,618532,618720,618981,619230,621814,622006,622271,622524,623056,623464,623663,624247,624482,624606,625018,625232,625634,625737,625867,626042,626294,626490,626630,626824,627835,628904,629192,629322,630099,630756,630902,631608,631846,633386,633536,633953,634118,634804,635274,635470,635561,635645,635789,636023,636190,637118,637404,637564,638179,638338,638666,638893,639405,639767,639846,640185,640290,640655,641026,641387,643261,643890,644966,645390,645643,645795,646843,647580,647783,648029,648276,648494,648736,649057,649321,649626,649849,650160,650349,651064,651333,651827,652053,652493,652652,652936,653681,654046,654351,654509,654747,656066,656464,656692,656912,657054,658344,658450,658580,658718,658842,659130,659299,659399,659684,659798,660681,661436,661875,661999,662245,662438,662572,662763,663542,663760,664051,664330,664647,664869,665164,665447,665551,665892,666708,667024,667585,668091,668296,669082,669487,670148,670337,670888,671454,671574,671976,824383,824478,824571,824634,824716,824809,824893,824980,825078,825169,825260,825348,825432,825528,825632,825719,825825,825928,826029,826133,826239,826338,826444,826546,826653,826762,826873,827004,827124,827240,827358,827457,827564,827680,827799,827927,828003,828098,828175,828264,828355,828448,828522,828607,828690,828788,828887,828991,829087,829189,829292,829392,829482,829567,829668,829766,829856,829951,830038,830144,830246,830340,830431,830512,830588,830680,830769,830889,831000,831083,831169,831264,831352,831448,831536,831637,831738,831832,831938,832036,832133,832228,832326,832429,832529,832632,832737,832855,832971,833066,833159,833244,833340,833434,833526,833628,833726,833800,833905,834005,834106,834211,834311,834412,834511,834613,834707,834814,834916,835019,835103,835199,835301,835404,835500,835602,835705,835802,835905,836003,836107,836212,836309,836417,836531,836646,836754,836868,836983,837085,837190,837298,837408,837524,837641,837728,837823,837920,838019,838124,838230,838329,838434,838540,838640,838746,838847,838954,839073,839172,839274,839376,839476,839579,839674,839778,839863,839967,840071,840169,840273,840379,840477,840599,840697,840810,840904,840993,841082,841165,841256,841339,841437,841527,841623,841712,841806,841894,841990,842075,842183,842284,842385,842483,842589,842680,842779,842876,842974,843070,843163,843273,843371,843466,843576,843668,843768,843867,843954,844058,844163,844262,844369,844476,844575,844684,844776,844887,844998,845109,845213,845328,845444,845571,845691,845788,845887,845979,846078,846170,846269,846355,846449,846552,846648,846751,846847,846950,847047,847145,847248,847328,847436,847526,847627,847710,847801,847886,847978,848081,848176,848272,848365,848446,848555,848634,848741,848832,848931,849024,849127,849231,849332,849433,849537,849631,849735,849839,849952,850058,850164,850272,850389,850478,850586,850686,850789,850876,850983,851062,851152,851236,851328,851401,851489,851571,851656,851741,851838,851931,852026,852125,852222,852313,852404,852496,852591,852689,852788,852890,852987,853084,853177,853264,853348,853445,853542,853635,853722,853813,853912,854011,854106,854195,854276,854375,854470,854567,854663,854760,854844,854943,855038,855135,855231,855328,855417,855518,855615,855714,855812,855911,856001,856092,856181,856270,856352,856445,856536,856647,856748,856848,856960,857073,857162,857270,857351,857451,857540,857632,857743,857853,857948,858064,858190,858316,858435,858563,858688,858813,858931,859058,859167,859276,859389,859512,859635,859751,859876,859973,860081,860194,860310,860426,860535,860623,860724,860813,860914,861001,861089,861186,861278,861378,861454", "endLines": "10,16,27,30,31,32,33,34,37,38,39,40,41,43,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,117,118,140,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,226,228,229,231,232,233,234,235,236,237,238,388,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,463,468,469,470,496,509,510,511,512,513,514,515,516,517,518,519,523,524,525,526,527,529,539,540,541,542,543,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,584,585,596,606,607,645,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1163,1169,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1894,1895,1947,1948,1949,1951,1952,1953,1954,1955,1956,1959,1960,1961,1962,1963,1964,1975,1978,1981,1982,2001,2002,2003,2004,2005,2006,2007,2015,2024,2025,2030,2031,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2093,2098,2142,2148,2149,2150,2151,2152,2153,2170,2171,2172,2173,2208,2210,2215,2216,2217,2229,2231,2232,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2292,2295,2298,2299,2300,2301,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2345,2346,2347,2348,2369,2370,2371,2372,2373,2409,2410,2411,2413,2414,2450,2454,2458,2462,2463,2467,2514,2521,2529,2694,2704,2713,2722,2731,2792,2793,2794,2800,2801,2802,2803,2804,2805,2811,2812,2813,2814,2815,2820,2821,2825,2826,2832,2836,2837,2838,2839,2849,2850,2851,2855,2856,2862,2866,2867,2942,2943,2947,2948,2951,2952,2953,2954,3217,3224,3484,3490,3753,3760,4020,4026,4089,4171,4223,4305,4367,4449,4513,4565,4647,4655,4661,4672,4676,4680,4693,4708,5484,5491,5497,5514,5527,5547,5564,5573,5578,5585,5605,5618,5635,5641,5647,5654,5658,5664,5678,5681,5691,5692,5693,5741,5745,5749,5753,5754,5755,5758,5774,5781,5795,5840,5841,5876,5880,5884,5889,5896,5902,5903,5906,5910,5915,5928,5932,5937,5942,5947,5950,5953,5956,5960,5964,6104,6105,6106,6107,6335,6336,6337,6338,6339,6340,6341,6342,6343,6344,6345,6346,6347,6348,6349,6350,6351,6352,6356,6360,6364,6368,6372,6376,6380,6381,6382,6383,6384,6385,6386,6387,6391,6395,6396,6400,6401,6404,6408,6411,6414,6417,6421,6436,6439,6443,6447,6451,6455,6458,6459,6460,6461,6464,6468,6471,6474,6477,6480,6483,6486,6490,6560,6561,6564,6567,6568,6571,6572,6573,6577,6578,6583,6590,6597,6604,6611,6618,6625,6632,6639,6646,6655,6664,6673,6680,6689,6698,6701,6704,6705,6706,6707,6708,6709,6710,6711,6712,6713,6714,6715,6716,6720,6725,6730,6733,6734,6735,6736,6737,6745,6753,6754,6762,6766,6774,6782,6790,6798,6806,6807,6815,6823,6824,6827,6830,6908,6913,6915,6920,6924,6928,6929,6930,6931,6935,6939,6940,6944,6945,6946,6947,6948,6949,6950,6951,6952,6953,6954,6955,6956,6957,6958,6959,6963,6967,6968,6972,6973,6974,6979,6980,6981,6982,6983,6984,6985,6986,6987,6988,6989,6990,6991,6992,6993,6994,6995,6996,6997,6998,6999,7003,7004,7005,7011,7012,7016,7018,7019,7024,7025,7026,7027,7028,7029,7033,7034,7035,7041,7042,7046,7048,7052,7056,7060,7064,7168,7169,7170,7173,7176,7179,7182,7185,7190,7194,7197,7198,7203,7207,7212,7218,7224,7229,7233,7238,7242,7246,7287,7288,7289,7290,7291,7295,7296,7297,7298,7302,7306,7310,7314,7318,7322,7326,7330,7336,7337,7378,7392,7397,7423,7430,7433,7444,7449,7452,7455,7510,7516,7517,7520,7523,7526,7529,7532,7535,7538,7542,7545,7546,7547,7555,7563,7566,7571,7576,7581,7586,7590,7594,7595,7603,7604,7605,7606,7607,7615,7620,7625,7626,7627,7628,7653,7659,7664,7667,7671,7674,7678,7688,7691,7696,7699,7703,7707,7812,7826,7839,7843,7858,7869,7872,7883,7888,7892,7927,7928,7929,7940,7947,7954,7961,7968,7988,7991,8018,8023,8043,8046,8053,8066,8075,8078,8098,8108,8112,8116,8129,8133,8137,8141,8147,8151,8168,8176,8180,8184,8188,8191,8195,8199,8203,8213,8220,8227,8231,8257,8267,8292,8301,8321,8331,8335,8345,8370,8380,8383,8387,8388,8389,8390,8394,8400,8406,8407,8420,8421,8422,8425,8428,8431,8434,8437,8440,8443,8446,8449,8452,8455,8458,8461,8464,8467,8470,8473,8476,8479,8482,8485,8486,8491,8492,8505,8515,8519,8524,8529,8533,8536,8540,8544,8547,8551,8554,8558,8563,8568,8571,8578,8582,8586,8595,8600,8605,8606,8610,8613,8617,8630,8635,8643,8647,8651,8668,8672,8677,8695,8702,8705,8735,8738,8741,8744,8747,8750,8753,8772,8778,8786,8793,8805,8813,8818,8822,8826,8837,8841,8849,8852,8857,8858,8859,8860,8864,8868,8872,8876,8911,8914,8918,8922,8956,8959,8963,8967,8976,8982,8985,8995,8999,9000,9007,9011,9018,9019,9020,9023,9028,9033,9034,9038,9053,9072,9076,9077,9089,9099,9100,9112,9117,9141,9144,9150,9153,9162,9170,9174,9177,9180,9183,9187,9190,9207,9211,9214,9229,9232,9240,9245,9252,9257,9258,9263,9264,9270,9276,9282,9314,9325,9342,9349,9353,9356,9369,9378,9382,9387,9391,9395,9399,9403,9407,9411,9415,9420,9423,9435,9440,9449,9452,9459,9460,9464,9473,9479,9483,9484,9488,9509,9515,9519,9523,9524,9542,9543,9544,9545,9546,9551,9554,9555,9561,9562,9574,9586,9593,9594,9599,9604,9605,9609,9623,9628,9634,9640,9646,9651,9657,9663,9664,9670,9685,9690,9699,9708,9711,9725,9730,9741,9745,9754,9763,9764,9771,9779,14521,14522,14523,14524,14525,14526,14527,14528,14529,14530,14531,14532,14533,14534,14535,14536,14537,14538,14539,14540,14541,14542,14543,14544,14545,14546,14547,14548,14549,14550,14551,14552,14553,14554,14555,14556,14557,14558,14559,14560,14561,14562,14563,14564,14565,14566,14567,14568,14569,14570,14571,14572,14573,14574,14575,14576,14577,14578,14579,14580,14581,14582,14583,14584,14585,14586,14587,14588,14589,14590,14591,14592,14593,14594,14595,14596,14597,14598,14599,14600,14601,14602,14603,14604,14605,14606,14607,14608,14609,14610,14611,14612,14613,14614,14615,14616,14617,14618,14619,14620,14621,14622,14623,14624,14625,14626,14627,14628,14629,14630,14631,14632,14633,14634,14635,14636,14637,14638,14639,14640,14641,14642,14643,14644,14645,14646,14647,14648,14649,14650,14651,14652,14653,14654,14655,14656,14657,14658,14659,14660,14661,14662,14663,14664,14665,14666,14667,14668,14669,14670,14671,14672,14673,14674,14675,14676,14677,14678,14679,14680,14681,14682,14683,14684,14685,14686,14687,14688,14689,14690,14691,14692,14693,14694,14695,14696,14697,14698,14699,14700,14701,14702,14703,14704,14705,14706,14707,14708,14709,14710,14711,14712,14713,14714,14715,14716,14717,14718,14719,14720,14721,14722,14723,14724,14725,14726,14727,14728,14729,14730,14731,14732,14733,14734,14735,14736,14737,14738,14739,14740,14741,14742,14743,14744,14745,14746,14747,14748,14749,14750,14751,14752,14753,14754,14755,14756,14757,14758,14759,14760,14761,14762,14763,14764,14765,14766,14767,14768,14769,14770,14771,14772,14773,14774,14775,14776,14777,14778,14779,14780,14781,14782,14783,14784,14785,14786,14787,14788,14789,14790,14791,14792,14793,14794,14795,14796,14797,14798,14799,14800,14801,14802,14803,14804,14805,14806,14807,14808,14809,14810,14811,14812,14813,14814,14815,14816,14817,14818,14819,14820,14821,14822,14823,14824,14825,14826,14827,14828,14829,14830,14831,14832,14833,14834,14835,14836,14837,14838,14839,14840,14841,14842,14843,14844,14845,14846,14847,14848,14849,14850,14851,14852,14853,14854,14855,14856,14857,14858,14859,14860,14861,14862,14863,14864,14865,14866,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888,14889,14890,14891,14892,14893,14894,14895", "endColumns": "55,48,54,55,59,60,64,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,59,46,55,48,57,53,60,55,50,59,55,62,48,55,55,49,58,54,61,46,53,55,51,54,53,53,53,48,57,53,56,55,46,52,55,59,62,58,61,49,53,53,47,56,52,55,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,75,77,68,75,76,63,70,71,50,52,54,66,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,67,72,69,61,60,67,61,69,68,55,58,61,61,66,56,60,60,60,60,55,55,55,55,57,57,57,57,56,56,56,56,58,58,57,82,82,72,53,68,55,80,80,70,9,52,57,57,57,45,59,53,69,69,64,65,64,67,68,67,9,52,58,57,51,49,51,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,57,50,44,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,59,60,60,59,60,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,86,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,99,83,93,87,97,83,90,93,98,101,97,99,86,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,100,84,94,88,98,84,91,94,99,102,98,102,88,100,86,96,87,95,91,99,89,97,84,88,88,92,86,90,65,75,68,78,72,79,79,76,67,77,75,70,80,72,82,74,84,72,80,80,73,83,69,77,69,79,77,71,81,69,76,79,84,87,83,86,73,77,77,70,80,90,82,95,97,106,64,65,52,75,65,86,75,75,64,54,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,98,96,98,85,85,80,74,88,90,83,58,45,65,56,66,56,81,64,65,122,83,120,64,61,97,73,82,88,63,78,73,61,95,64,58,55,55,59,106,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,48,47,57,56,101,56,74,47,50,61,64,51,73,62,62,67,49,61,59,56,59,48,67,105,101,68,70,55,48,99,70,109,88,90,81,97,55,100,109,98,62,105,76,111,126,111,126,69,113,130,96,67,117,102,117,60,73,66,104,121,73,66,109,98,72,96,121,117,117,60,121,116,67,105,101,79,70,95,66,73,73,85,87,89,77,76,99,70,120,120,63,124,73,123,123,66,108,127,111,78,77,100,70,121,121,64,125,111,105,67,98,103,62,65,83,112,112,117,77,71,135,135,84,139,137,137,141,81,108,110,127,127,131,129,129,133,61,95,66,116,120,96,81,86,86,130,130,134,76,76,110,113,73,108,111,101,95,103,66,93,71,109,105,72,90,101,102,94,106,104,121,121,125,58,57,123,123,127,117,117,121,85,96,133,133,79,137,131,131,135,74,75,102,73,112,80,56,60,58,59,57,60,57,49,48,66,58,58,48,70,83,70,79,68,62,67,65,67,64,65,76,77,105,105,95,128,88,126,65,68,85,65,82,97,95,95,97,108,94,88,61,59,64,56,80,53,56,96,109,60,114,120,94,91,92,101,55,58,48,91,48,53,53,53,53,53,54,109,109,107,109,109,109,109,107,105,103,103,103,94,94,92,92,103,105,103,103,92,92,92,92,107,105,105,105,96,94,94,94,105,105,105,105,97,95,95,97,64,103,57,63,60,61,59,61,67,57,62,62,66,74,72,65,51,52,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,64,60,59,74,73,76,72,69,71,69,72,63,69,45,68,51,84,82,56,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,53,54,61,56,93,68,100,50,69,62,55,57,58,53,85,83,69,68,69,114,120,66,66,74,66,58,53,53,53,52,51,73,136,139,48,49,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,53,54,57,54,58,55,68,68,68,69,62,62,62,62,64,64,64,64,62,63,63,63,50,77,77,70,71,72,71,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,103,109,108,103,77,64,66,65,69,46,51,49,56,12,149,130,183,177,237,188,168,97,114,84,78,11,64,88,156,156,152,153,58,186,95,89,95,89,165,122,122,169,105,114,114,101,105,116,114,81,172,167,147,158,154,172,116,116,167,111,113,171,175,157,132,111,145,151,131,142,121,177,135,95,135,94,166,92,91,186,11,11,11,181,316,181,181,189,231,189,176,161,156,109,182,136,219,183,183,159,157,183,226,202,170,219,221,154,199,183,102,189,140,164,170,199,203,201,164,204,198,198,196,90,148,149,83,148,144,151,140,165,160,77,300,165,154,101,11,163,185,222,124,10,10,10,10,61,10,10,10,10,10,10,10,10,10,84,120,98,10,96,116,86,122,100,10,98,118,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,120,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,136,90,10,10,10,10,108,118,10,10,10,10,10,94,10,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,111,105,122,131,122,129,123,132,130,124,116,119,131,127,113,117,112,120,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,151,10,100,10,10,10,10,10,10,10,10,10,10,10,10,10,144,121,134,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,10,10,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,10,75,111,89,10,10,107,10,107,75,111,89,101,107,107,99,107,84,103,86,77,113,91,10,10,109,10,109,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,10,10,10,113,119,115,93,10,98,117,100,10,10,10,10,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,145,137,135,10,10,10,115,10,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,123,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,94,92,62,81,92,83,86,97,90,90,87,83,95,103,86,105,102,100,103,105,98,105,101,106,108,110,130,119,115,117,98,106,115,118,127,75,94,76,88,90,92,73,84,82,97,98,103,95,101,102,99,89,84,100,97,89,94,86,105,101,93,90,80,75,91,88,119,110,82,85,94,87,95,87,100,100,93,105,97,96,94,97,102,99,102,104,117,115,94,92,84,95,93,91,101,97,73,104,99,100,104,99,100,98,101,93,106,101,102,83,95,101,102,95,101,102,96,102,97,103,104,96,107,113,114,107,113,114,101,104,107,109,115,116,86,94,96,98,104,105,98,104,105,99,105,100,106,118,98,101,101,99,102,94,103,84,103,103,97,103,105,97,121,97,112,93,88,88,82,90,82,97,89,95,88,93,87,95,84,107,100,100,97,105,90,98,96,97,95,92,109,97,94,109,91,99,98,86,103,104,98,106,106,98,108,91,110,110,110,103,114,115,126,119,96,98,91,98,91,98,85,93,102,95,102,95,102,96,97,102,79,107,89,100,82,90,84,91,102,94,95,92,80,108,78,106,90,98,92,102,103,100,100,103,93,103,103,112,105,105,107,116,88,107,99,102,86,106,78,89,83,91,72,87,81,84,84,96,92,94,98,96,90,90,91,94,97,98,101,96,96,92,86,83,96,96,92,86,90,98,98,94,88,80,98,94,96,95,96,83,98,94,96,95,96,88,100,96,98,97,98,89,90,88,88,81,92,90,110,100,99,111,112,88,107,80,99,88,91,110,109,94,115,125,125,118,127,124,124,117,126,108,108,112,122,122,115,124,96,107,112,115,115,108,87,100,88,100,86,87,96,91,99,75,76", "endOffsets": "541,776,1235,1394,1454,1515,1580,1635,1785,1835,1888,1946,1994,2114,2463,2534,2606,2678,2751,2818,2867,2921,2958,3009,3069,3116,3172,3221,3279,3333,3394,3450,3501,3561,3617,3680,3729,3785,3841,3891,3950,4005,4067,4114,4168,4224,4276,4331,4385,4439,4493,4542,4600,4654,4711,4767,4814,4867,4923,4983,5046,5105,5167,5217,5271,5325,5373,5430,5483,5539,6161,6217,7155,7265,7321,7381,7434,7495,7574,7655,7727,7806,7886,7962,8040,8109,8185,8262,8333,8406,8482,8560,8629,8705,8782,8846,8917,8989,11481,11579,11634,11750,11803,11855,11905,11963,12028,12076,12127,19084,19213,19271,19340,19398,19467,19537,19610,19684,19752,19819,19889,19955,20028,20088,20164,20224,20284,20359,20427,20493,20561,20621,20680,20737,20803,20865,20922,20990,21063,21133,21195,21256,21324,21386,21456,21525,21581,21640,21702,21764,21831,21888,21949,22010,22071,22132,22188,22244,22300,22356,22414,22472,22530,22588,22645,22702,22759,22816,22875,22934,22992,23075,23158,23231,23285,23354,23410,23491,23572,23643,23772,24031,24089,24147,25234,25841,25901,25955,26025,26095,26160,26226,26291,26359,26428,26496,26626,26679,26738,26796,26848,26944,27391,27437,27487,27543,27590,27695,27753,27815,27878,27940,27999,28059,28124,28190,28255,28317,28379,28441,28503,28565,28627,28693,28760,28826,28889,28953,29016,29084,29145,29207,29269,29332,29396,29459,29523,29601,29660,29726,29806,29867,29920,30129,30180,30607,31077,31136,32879,37162,37233,37299,37373,37442,37513,37586,37657,37725,37798,37874,37944,38022,38090,38156,38217,38286,38350,38416,38484,38550,38613,38681,38752,38817,38890,38953,39034,39098,39164,39234,39304,39374,39444,39511,41329,41387,41446,41506,41565,41624,41683,41742,41801,41860,41919,41978,42037,42096,42156,42217,42279,42340,42401,42462,42523,42584,42645,42705,42766,42827,42887,42948,43009,43070,43131,43192,43253,43314,43375,43436,43497,43558,43626,43695,43765,43834,43903,43972,44041,44110,44179,44248,44317,44386,44455,44515,44576,44638,44699,44760,44821,44882,44943,45004,45065,45126,45187,45248,45310,45373,45437,45500,45563,45626,45689,45752,45815,45878,45941,46004,46067,46128,46190,46253,46315,46377,46439,46501,46563,46625,46687,46749,46811,46873,46930,47017,47097,47187,47282,47374,47466,47556,47639,47732,47819,47916,48007,48108,48195,48298,48387,48486,48578,48678,48762,48856,48944,49042,49126,49217,49311,49410,49512,49610,49710,49797,49897,49983,50079,50167,50248,50339,50435,50528,50621,50712,50797,50891,50980,51078,51171,51273,51361,51465,51556,51656,51749,51850,51935,52030,52119,52218,52303,52395,52490,52590,52693,52792,52895,52984,53085,53172,53269,53357,53453,53545,53645,53735,53833,53918,54007,54096,54189,54276,54367,55105,55181,55250,55329,55402,55482,55562,55639,55707,55785,55861,55932,56013,56086,56169,56244,56329,56402,56483,56564,56638,56722,56792,56870,56940,57020,57098,57170,57252,57322,57399,57479,57564,57652,57736,57823,57897,57975,58053,58124,58205,58296,58379,58475,58573,58680,58745,58811,58864,58940,59006,59093,59169,59245,68829,69197,69791,69870,69948,70021,70086,70149,70215,70286,70357,70427,70489,70558,70624,70684,70751,70818,70874,70925,70978,71030,71084,71155,71218,71277,71339,71398,71471,71538,71608,71668,71731,71806,71878,71974,72045,72101,72172,72229,72286,72352,72416,72487,72544,72597,72660,72712,72770,72837,74076,74142,74201,74284,74343,74400,74467,74537,74611,74673,74742,74812,74911,75008,75107,75193,75279,75360,75435,75524,75615,75699,75758,75804,75870,75927,75994,76051,76133,76198,76264,76387,76471,76592,76657,76719,76817,76891,76974,77063,77127,77206,77280,77342,77438,77503,77562,77618,77674,77734,77841,77888,77948,78009,78073,78134,78194,78252,78295,78344,78396,78447,78499,78548,78597,78662,78728,78788,78849,78905,78964,79013,79061,79119,79176,79278,79335,79410,79458,79509,79571,79636,79688,79762,79825,79888,79956,80006,80068,80128,80185,80245,80294,80362,80468,80570,80639,80710,80766,80815,80915,80986,81096,81185,81276,81358,81456,81512,81613,81723,81822,81885,81991,82068,82180,82307,82419,82546,82616,82730,82861,82958,83026,83144,83247,83365,83426,83500,83567,83672,83794,83868,83935,84045,84144,84217,84314,84436,84554,84672,84733,84855,84972,85040,85146,85248,85328,85399,85495,85562,85636,85710,85796,85884,85974,86052,86129,86229,86300,86421,86542,86606,86731,86805,86929,87053,87120,87229,87357,87469,87548,87626,87727,87798,87920,88042,88107,88233,88345,88451,88519,88618,88722,88785,88851,88935,89048,89161,89279,89357,89429,89565,89701,89786,89926,90064,90202,90344,90426,90535,90646,90774,90902,91034,91164,91294,91428,91490,91586,91653,91770,91891,91988,92070,92157,92244,92375,92506,92641,92718,92795,92906,93020,93094,93203,93315,93417,93513,93617,93684,93778,93850,93960,94066,94139,94230,94332,94435,94530,94637,94742,94864,94986,95112,95171,95229,95353,95477,95605,95723,95841,95963,96049,96146,96280,96414,96494,96632,96764,96896,97032,97107,97183,97286,97360,97473,97554,97611,97672,97731,97791,97849,97910,97968,98018,98067,98134,98193,98252,98301,98372,98456,98527,98607,98676,98739,98807,98873,98941,99006,99072,99149,99227,99333,99439,99535,99664,99753,99880,99946,100015,100101,100167,100250,100348,100444,100540,100638,100747,100842,100931,100993,101053,101118,101175,101256,101310,101367,101464,101574,101635,101750,101871,101966,102058,102151,102253,102309,102368,102417,102509,102558,102612,102666,102720,102774,102828,102883,102993,103103,103211,103321,103431,103541,103651,103759,103865,103969,104073,104177,104272,104367,104460,104553,104657,104763,104867,104971,105064,105157,105250,105343,105451,105557,105663,105769,105866,105961,106056,106151,106257,106363,106469,106575,106673,106769,106865,106963,107028,107132,107190,107254,107315,107377,107437,107499,107567,107625,107688,107751,107818,107893,107966,108032,108084,108137,108189,108246,108330,108425,108510,108591,108671,108748,108827,108904,108978,109052,109123,109203,109275,109350,109415,109476,109536,109611,109685,109762,109835,109905,109977,110047,110120,110184,110254,110300,110369,110421,110506,110589,110646,110712,110779,110845,110926,111001,111057,111110,111171,111229,111279,111328,111377,111426,111488,111540,111585,111666,111717,111771,111824,111878,111929,111978,112044,112095,112156,112217,112279,112329,112370,112447,112506,112565,112624,112685,112741,112797,112864,112925,112990,113045,113110,113179,113247,113325,113394,113454,113525,113599,113664,113736,113806,113873,113957,114026,114093,114163,114226,114293,114361,114444,114523,114613,114690,114758,114825,114903,114960,115017,115085,115151,115207,115267,115326,115380,115430,115480,115528,115590,115641,115714,115794,115874,115938,116005,116076,116134,116195,116261,116320,116387,116447,116507,116570,116638,116699,116766,116844,116914,116963,117020,117089,117150,117238,117326,117414,117502,117589,117676,117763,117850,117908,117982,118052,118108,118179,118244,118306,118381,118454,118544,118610,118676,118737,118801,118863,118921,118992,119075,119134,119205,119271,119336,119397,119456,119527,119593,119658,119741,119817,119892,119973,120033,120102,120172,120241,120296,120352,120408,120469,120527,120583,120637,120692,120754,120811,120905,120974,121075,121126,121196,121259,121315,121373,121432,121486,121572,121656,121726,121795,121865,121980,122101,122168,122235,122310,122377,122436,122490,122544,122598,122651,122703,122777,125355,125495,128446,128496,128546,128635,128691,128749,128811,128866,128924,129105,129169,129228,129290,129356,129422,129970,130116,130269,130312,131339,131386,131431,131482,131533,131584,131635,132016,132524,132586,132887,132959,133084,133138,133193,133251,133306,133365,133421,133490,133559,133628,133698,133761,133824,133887,133950,134015,134080,134145,134210,134273,134337,134401,134465,134516,134594,134672,134743,134815,134888,134960,135026,135092,135160,135228,135294,135361,135435,135498,135555,135615,135680,135747,135812,135869,135930,135988,136092,136202,136311,136415,136493,136558,136625,136691,136761,136808,136860,136910,137093,137483,140479,140861,141045,141223,141461,141650,141819,142963,143078,143163,143242,146487,146634,147045,147202,147359,148418,148633,148692,149331,149427,149517,149613,149703,149869,149992,150115,150285,150391,150506,150621,150723,150829,150946,151061,151185,151358,151526,151674,151833,151988,152161,152278,152395,152563,152675,152789,152961,153137,153295,153428,153540,153686,153838,153970,154113,154235,154413,154549,154645,154781,154876,155043,155136,155228,155415,155571,155749,155913,156095,156412,156594,156776,156966,157198,157388,157565,157727,157884,157994,158177,158314,158534,158718,158902,159062,159220,159404,159631,159834,160005,160225,160447,160602,160802,160986,161089,161279,161420,161585,161756,161956,162160,162362,162527,162732,162931,163130,163327,163418,163567,163717,163801,163950,164095,164247,164388,164554,164715,165914,166215,166381,166536,166638,168683,168847,169033,169296,169421,171668,171940,172218,172463,172525,172810,175804,176260,176769,187545,188059,188496,188930,189373,193355,193476,193575,193980,194077,194194,194281,194404,194505,194911,195010,195129,195222,195329,195672,195779,196024,196145,196554,196802,196902,197007,197126,197635,197782,197901,198152,198285,198700,198954,199066,204682,204807,205124,205245,205473,205594,205727,205874,226505,226997,247377,247801,268477,268971,289396,289822,294663,300080,304171,309602,314344,319721,323705,327697,333088,333635,334068,334824,335054,335297,336430,337359,385504,386058,386511,387941,388685,389878,390932,391410,391703,392086,393601,394366,395509,395950,396391,396957,397231,397642,398658,398836,399589,399726,399817,402011,402277,402599,402809,402918,403037,403221,404339,404809,405560,408143,408238,410600,410828,411084,411343,411919,412273,412395,412534,412826,413086,414014,414300,414703,415105,415448,415660,415861,416074,416363,416648,427323,427410,427495,427594,440132,440238,440361,440493,440616,440746,440870,441003,441134,441259,441376,441496,441628,441756,441870,441988,442101,442222,442410,442597,442778,442961,443145,443310,443492,443612,443732,443840,443950,444062,444170,444280,444445,444611,444763,444928,445029,445149,445320,445481,445644,445805,445972,446737,446854,447034,447216,447397,447580,447735,447880,448002,448137,448300,448493,448619,448771,448913,449083,449239,449411,449702,456369,456461,456634,456796,456891,457060,457154,457243,457486,457575,457868,458284,458704,459125,459551,459968,460384,460801,461219,461633,462103,462576,463048,463459,463930,464402,464592,464798,464904,465012,465118,465230,465344,465456,465570,465686,465800,465908,466018,466126,466388,466767,467171,467318,467426,467536,467644,467758,468167,468581,468697,469115,469356,469786,470221,470631,471053,471463,471585,471994,472410,472532,472750,472934,477977,478321,478401,478757,478907,479051,479127,479239,479329,479591,479856,479964,480116,480224,480300,480412,480502,480604,480712,480820,480920,481028,481113,481217,481304,481382,481496,481588,481852,482119,482229,482382,482492,482576,482965,483063,483171,483265,483395,483503,483625,483761,483869,483989,484123,484245,484373,484515,484641,484781,484907,485025,485157,485255,485365,485665,485777,485895,486359,486475,486778,486904,487000,487401,487511,487635,487773,487883,488005,488317,488441,488571,489047,489175,489490,489628,489790,490006,490162,490366,496294,496378,496482,496685,496874,497075,497268,497473,497786,497998,498162,498278,498524,498740,499053,499479,499941,500178,500330,500590,500734,500876,504108,504222,504342,504458,504552,504873,504972,505090,505191,505470,505755,506034,506316,506569,506828,507081,507337,507761,507837,511087,512442,512886,514740,515315,515523,516533,516913,517079,517220,522240,522666,522778,522913,523066,523263,523434,523617,523792,523979,524251,524409,524493,524597,525084,525640,525798,526017,526248,526471,526706,526928,527194,527332,527931,528045,528183,528295,528419,528990,529485,530031,530176,530269,530361,532288,532858,533156,533345,533551,533744,533954,534838,534983,535375,535533,535750,536011,544238,545113,545733,545930,546878,547643,547766,548539,548760,548960,550937,551037,551127,551712,552382,553064,553744,554436,555649,555814,557427,557748,558811,558981,559551,560446,561079,561245,562731,563347,563583,563804,564762,565027,565292,565539,565953,566189,567474,567923,568110,568359,568601,568777,569018,569251,569476,570071,570546,571070,571331,572682,573157,574383,574853,575901,576353,576597,577054,578299,578782,578932,579276,579422,579560,579696,579984,580488,580997,581113,582015,582137,582249,582426,582692,582962,583228,583496,583752,584012,584268,584526,584778,585034,585286,585540,585772,586008,586260,586516,586768,587022,587254,587488,587600,588025,588149,589241,590056,590252,590576,590965,591317,591558,591772,592071,592263,592578,592785,593131,593431,593832,594051,594464,594701,595071,595795,596150,596419,596559,596813,596957,597234,598226,598635,599267,599613,599981,601055,601418,601818,603326,603911,604136,606665,606859,607077,607303,607515,607714,607921,609125,609420,609977,610367,610999,611467,611712,611969,612215,612975,613239,613662,613853,614232,614320,614428,614536,614849,615174,615493,615824,618527,618715,618976,619225,621809,622001,622266,622519,623051,623459,623658,624242,624477,624601,625013,625227,625629,625732,625862,626037,626289,626485,626625,626819,627830,628899,629187,629317,630094,630751,630897,631603,631841,633381,633531,633948,634113,634799,635269,635465,635556,635640,635784,636018,636185,637113,637399,637559,638174,638333,638661,638888,639400,639762,639841,640180,640285,640650,641021,641382,643256,643885,644961,645385,645638,645790,646838,647575,647778,648024,648271,648489,648731,649052,649316,649621,649844,650155,650344,651059,651328,651822,652048,652488,652647,652931,653676,654041,654346,654504,654742,656061,656459,656687,656907,657049,658339,658445,658575,658713,658837,659125,659294,659394,659679,659793,660676,661431,661870,661994,662240,662433,662567,662758,663537,663755,664046,664325,664642,664864,665159,665442,665546,665887,666703,667019,667580,668086,668291,669077,669482,670143,670332,670883,671449,671569,671971,672505,824473,824566,824629,824711,824804,824888,824975,825073,825164,825255,825343,825427,825523,825627,825714,825820,825923,826024,826128,826234,826333,826439,826541,826648,826757,826868,826999,827119,827235,827353,827452,827559,827675,827794,827922,827998,828093,828170,828259,828350,828443,828517,828602,828685,828783,828882,828986,829082,829184,829287,829387,829477,829562,829663,829761,829851,829946,830033,830139,830241,830335,830426,830507,830583,830675,830764,830884,830995,831078,831164,831259,831347,831443,831531,831632,831733,831827,831933,832031,832128,832223,832321,832424,832524,832627,832732,832850,832966,833061,833154,833239,833335,833429,833521,833623,833721,833795,833900,834000,834101,834206,834306,834407,834506,834608,834702,834809,834911,835014,835098,835194,835296,835399,835495,835597,835700,835797,835900,835998,836102,836207,836304,836412,836526,836641,836749,836863,836978,837080,837185,837293,837403,837519,837636,837723,837818,837915,838014,838119,838225,838324,838429,838535,838635,838741,838842,838949,839068,839167,839269,839371,839471,839574,839669,839773,839858,839962,840066,840164,840268,840374,840472,840594,840692,840805,840899,840988,841077,841160,841251,841334,841432,841522,841618,841707,841801,841889,841985,842070,842178,842279,842380,842478,842584,842675,842774,842871,842969,843065,843158,843268,843366,843461,843571,843663,843763,843862,843949,844053,844158,844257,844364,844471,844570,844679,844771,844882,844993,845104,845208,845323,845439,845566,845686,845783,845882,845974,846073,846165,846264,846350,846444,846547,846643,846746,846842,846945,847042,847140,847243,847323,847431,847521,847622,847705,847796,847881,847973,848076,848171,848267,848360,848441,848550,848629,848736,848827,848926,849019,849122,849226,849327,849428,849532,849626,849730,849834,849947,850053,850159,850267,850384,850473,850581,850681,850784,850871,850978,851057,851147,851231,851323,851396,851484,851566,851651,851736,851833,851926,852021,852120,852217,852308,852399,852491,852586,852684,852783,852885,852982,853079,853172,853259,853343,853440,853537,853630,853717,853808,853907,854006,854101,854190,854271,854370,854465,854562,854658,854755,854839,854938,855033,855130,855226,855323,855412,855513,855610,855709,855807,855906,855996,856087,856176,856265,856347,856440,856531,856642,856743,856843,856955,857068,857157,857265,857346,857446,857535,857627,857738,857848,857943,858059,858185,858311,858430,858558,858683,858808,858926,859053,859162,859271,859384,859507,859630,859746,859871,859968,860076,860189,860305,860421,860530,860618,860719,860808,860909,860996,861084,861181,861273,861373,861449,861526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\91f5ac35e2aac1e5ec49fa8326c1819a\\transformed\\databinding-runtime-8.1.2\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1936", "startColumns": "4", "startOffsets": "127888", "endColumns": "40", "endOffsets": "127924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8c3942081edffb4b875bc5f4359e13d8\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2017", "startColumns": "4", "startOffsets": "132066", "endColumns": "42", "endOffsets": "132104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5ce941577190a4b7e40da0e194a20c68\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1938,1939,1967,1976,1977,2009,2010,2011,2012,2013", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "127986,128026,129527,129975,130030,131675,131729,131781,131830,131891", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "128021,128068,129565,130025,130072,131724,131776,131825,131886,131936"}}, {"source": "C:\\Muslim Core\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2224,2225,2226,2227,2228,2397", "startColumns": "4,4,4,4,4,4", "startOffsets": "147754,147836,147940,148049,148169,168055", "endColumns": "81,103,108,119,100,69", "endOffsets": "147831,147935,148044,148164,148265,168120"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fc7a596ca09ec1789d8b31b6cf9b156f\\transformed\\lottie-6.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "389,1946", "startColumns": "4,4", "startOffsets": "19089,128355", "endColumns": "62,46", "endOffsets": "19147,128397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\bd229cba7731028eaebcf7d452c4e6dc\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "692,693,694,695,696,697,698,699,2174,2175,2176,2177,2178,2179,2180,2181,2183,2184,2185,2186,2187,2188,2189,2190,2191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "35952,36042,36122,36212,36302,36382,36463,36543,143247,143352,143533,143658,143765,143945,144068,144184,144454,144642,144747,144928,145053,145228,145376,145439,145501", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "36037,36117,36207,36297,36377,36458,36538,36618,143347,143528,143653,143760,143940,144063,144179,144282,144637,144742,144923,145048,145223,145371,145434,145496,145575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2829f477d9c6056a40040b143f304a8b\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "138,141,1177", "startColumns": "4,4,4", "startOffsets": "6996,7160,69690", "endColumns": "55,47,51", "endOffsets": "7047,7203,69737"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\29aa2070c5f46de89120ba6e20f2606d\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "471,657,658,686,687,1010,1012,1170,1171,1172,1173,1174,1175,1176,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1944,1945,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2008,2094,2156,2157,2158,2159,2160,2161,2162,2416,6547,6548,6552,6553,6557,7803,7804", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24152,33586,33658,35572,35637,59250,59377,69202,69272,69340,69412,69482,69543,69617,122782,122843,122904,122966,123030,123092,123153,123221,123321,123381,123447,123520,123589,123646,123698,125500,125572,125648,125713,125772,125831,125891,125951,126011,126071,126131,126191,126251,126311,126371,126431,126490,126550,126610,126670,126730,126790,126850,126910,126970,127030,127090,127149,127209,127269,127328,127387,127446,127505,127564,128285,128320,130428,130483,130546,130601,130659,130717,130778,130841,130898,130949,130999,131060,131117,131183,131217,131640,137098,141954,142021,142093,142162,142231,142305,142377,169464,455421,455538,455739,455849,456050,543672,543744", "endLines": "471,657,658,686,687,1010,1012,1170,1171,1172,1173,1174,1175,1176,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1944,1945,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2008,2094,2156,2157,2158,2159,2160,2161,2162,2416,6547,6551,6552,6556,6557,7803,7804", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "24207,33653,33741,35632,35698,59314,59435,69267,69335,69407,69477,69538,69612,69685,122838,122899,122961,123025,123087,123148,123216,123316,123376,123442,123515,123584,123641,123693,123755,125567,125643,125708,125767,125826,125886,125946,126006,126066,126126,126186,126246,126306,126366,126426,126485,126545,126605,126665,126725,126785,126845,126905,126965,127025,127085,127144,127204,127264,127323,127382,127441,127500,127559,127618,128315,128350,130478,130541,130596,130654,130712,130773,130836,130893,130944,130994,131055,131112,131178,131212,131247,131670,137163,142016,142088,142157,142226,142300,142372,142460,169530,455533,455734,455844,456045,456174,543739,543806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\81561af282a29cce9274e37ca4fb03dd\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "680,681,682,683,1164,1165,2194,2218,2219,2220", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "35208,35266,35332,35395,68834,68905,145680,147364,147431,147510", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "35261,35327,35390,35452,68900,68972,145743,147426,147505,147574"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f603684ac85a3923c1b73c512f406c56\\transformed\\glide-4.16.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1940", "startColumns": "4", "startOffsets": "128073", "endColumns": "57", "endOffsets": "128126"}}, {"source": "C:\\Muslim Core\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2140,2141,2143,2144,2145,2146,2147,2154,2155,2163,2164,2165,2166,2167,2168,2169,2192,2195,2196,2197,2198,2200,2201,2202,2203,2204,2205,2209,2211,2212,2213,2222,2223,2230,2233,2234,2235,2236,2237,2238,2239,2240,2241,2258,2349,2350,2353,2354,2355,2356,2357,2358,2359,2360,2362,2363,2364,2365,2366,2367,2368,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,2388,2389,2390,2391,2392,2396,2398,2399,2400,2401,2402,2403,2404,2405,2412,2415,2417,2418,2419,2420,2421,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2441", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "139354,139394,139455,139527,139585,139648,139706,139769,139826,139893,139947,140007,140086,140226,140275,140484,140522,140566,140641,140687,141824,141890,142465,142507,142573,142636,142689,142748,142807,145580,145748,145788,145846,145899,145998,146062,146100,146138,146199,146266,146492,146639,146739,146826,147661,147712,148423,148697,148733,148776,148834,148907,148953,149005,149058,149103,151066,164720,164766,164983,165029,165082,165126,165178,165228,165286,165338,165416,165500,165578,165668,165724,165758,165796,166643,166784,166858,166990,167072,167115,167167,167229,167276,167330,167375,167428,167473,167527,167578,167635,167691,167749,167800,167997,168125,168165,168213,168273,168317,168357,168397,168435,169038,169426,169535,169592,169658,169728,169805,170010,170050,170096,170152,170211,170255,170306,170366,170428,170468,170512,170556,170602,170653,170703,170761,170918", "endColumns": "39,60,71,57,62,57,62,56,66,53,59,78,56,48,58,37,43,74,45,47,65,63,41,65,62,52,58,58,62,61,39,57,52,41,63,37,37,60,66,65,81,99,86,77,50,41,60,35,42,57,72,45,51,52,44,45,41,45,53,45,52,43,51,49,57,51,33,83,77,89,55,33,37,44,140,73,131,81,42,51,61,46,53,44,52,44,53,50,56,55,57,50,56,57,39,47,59,43,39,39,37,41,39,37,56,65,69,76,65,39,45,55,58,43,50,59,61,39,43,43,45,50,49,57,58,35", "endOffsets": "139389,139450,139522,139580,139643,139701,139764,139821,139888,139942,140002,140081,140138,140270,140329,140517,140561,140636,140682,140730,141885,141949,142502,142568,142631,142684,142743,142802,142865,145637,145783,145841,145894,145936,146057,146095,146133,146194,146261,146327,146569,146734,146821,146899,147707,147749,148479,148728,148771,148829,148902,148948,149000,149053,149098,149144,151103,164761,164815,165024,165077,165121,165173,165223,165281,165333,165367,165495,165573,165663,165719,165753,165791,165836,166779,166853,166985,167067,167110,167162,167224,167271,167325,167370,167423,167468,167522,167573,167630,167686,167744,167795,167852,168050,168160,168208,168268,168312,168352,168392,168430,168472,169073,169459,169587,169653,169723,169800,169866,170045,170091,170147,170206,170250,170301,170361,170423,170463,170507,170551,170597,170648,170698,170756,170815,170949"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ce93ce5bd45704ff32cf7f9b33dbc2e2\\transformed\\databinding-adapters-8.1.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "1965,1966,2000", "startColumns": "4,4,4", "startOffsets": "129427,129484,131252", "endColumns": "56,42,40", "endOffsets": "129479,129522,131288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\14523b378ddeb224f3413513055a3ace\\transformed\\fragment-1.6.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "1937,1983,2021", "startColumns": "4,4,4", "startOffsets": "127929,130317,132273", "endColumns": "56,64,63", "endOffsets": "127981,130377,132332"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cdebcb5cea68a5cacee791078fa3b341\\transformed\\core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "497,530,635,636,637,638,1879,1880,1881,1882,1883,1884,1885,2029,2868,2869,2870,5842,5844,7130,7139,7152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25239,26949,32157,32226,32298,32361,124256,124330,124406,124482,124559,124630,124699,132761,199071,199152,199244,408243,408352,493720,494180,494955", "endLines": "497,530,635,636,637,638,1879,1880,1881,1882,1883,1884,1885,2029,2868,2869,2870,5843,5845,7138,7151,7155", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "25294,27003,32221,32293,32356,32428,124325,124401,124477,124554,124625,124694,124765,132824,199147,199239,199332,408347,408468,494175,494950,495223"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ab9ce3d6fabc773074c27a4a16889e6f\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "35,688,689,690,691,1166,1167,1168,2489,5863,5865,5868", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1640,35703,35764,35826,35888,68977,69036,69093,174266,409911,409975,410101", "endLines": "35,688,689,690,691,1166,1167,1168,2495,5864,5867,5870", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "1687,35759,35821,35883,35947,69031,69088,69142,174675,409970,410096,410224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2025416948d6099286a1c5deac9fc59b\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "641,1019,1871,1872,1873,1874,1875,1876,1877,1969,1970,1971,2091,2092,2193,2214,2361,2393,2422,2439,2440,5846,6129,6132,6138,6144,6147,6153,6157,6160,6167,6173,6176,6182,6187,6192,6199,6201,6207,6213,6221,6226,6233,6238,6244,6248,6255,6259,6265,6271,6274,6278,6279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "32550,59725,123760,123824,123879,123947,124014,124079,124136,129637,129685,129733,136915,136978,145642,146904,165372,167857,169871,170820,170870,408473,428914,429019,429264,429602,429748,430088,430300,430463,430870,431208,431331,431670,431909,432166,432537,432597,432935,433221,433670,433962,434350,434655,434999,435244,435574,435781,436049,436322,436466,436667,436714", "endLines": "641,1019,1871,1872,1873,1874,1875,1876,1877,1969,1970,1971,2091,2092,2193,2214,2361,2395,2422,2439,2440,5862,6131,6137,6143,6146,6152,6156,6159,6166,6172,6175,6181,6186,6191,6198,6200,6206,6212,6220,6225,6232,6237,6243,6247,6254,6258,6264,6270,6273,6277,6278,6279", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55", "endOffsets": "32618,59789,123819,123874,123942,124009,124074,124131,124188,129680,129728,129789,136973,137036,145675,146956,165411,167992,170005,170865,170913,409906,429014,429259,429597,429743,430083,430295,430458,430865,431203,431326,431665,431904,432161,432532,432592,432930,433216,433665,433957,434345,434650,434994,435239,435569,435776,436044,436317,436461,436662,436709,436765"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dea3e6fcf5697c9423505c2045e63399\\transformed\\recyclerview-1.3.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "508,1228,1229,1230,1238,1239,1240,1943", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "25744,73015,73074,73122,73789,73864,73940,128219", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "25795,73069,73117,73173,73859,73935,74007,128280"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b6d17d87dae82d892d5ed1b2c35b7a41\\transformed\\coordinatorlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "109,9825", "startColumns": "4,4", "startOffsets": "5765,675127", "endLines": "109,9827", "endColumns": "60,12", "endOffsets": "5821,675267"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cacdfc042dfae4e43c008dc67295820e\\transformed\\navigation-ui-2.7.4\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "2027,2199,2351,2352", "startColumns": "4,4,4,4", "startOffsets": "132651,145941,164820,164896", "endColumns": "52,56,75,86", "endOffsets": "132699,145993,164891,164978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\69000e4e45894c9884b32a91ee0b736c\\transformed\\navigation-runtime-2.7.4\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1957", "startColumns": "4", "startOffsets": "128929", "endColumns": "52", "endOffsets": "128977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b3220947d7de38838cf340bae783eebe\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2019", "startColumns": "4", "startOffsets": "132169", "endColumns": "53", "endOffsets": "132218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\812136ae31eafce15fafbc5dfed6f6ac\\transformed\\play-services-basement-18.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2032,2182", "startColumns": "4,4", "startOffsets": "132964,144287", "endColumns": "67,166", "endOffsets": "133027,144449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\59f4b2c5fb29ed0d6dfb40492e70e152\\transformed\\work-runtime-2.8.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "642,643,644,646", "startColumns": "4,4,4,4", "startOffsets": "32623,32688,32758,32884", "endColumns": "64,69,63,60", "endOffsets": "32683,32753,32817,32940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f059acc78cb16502a6170c70ab7b48a3\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2139", "startColumns": "4", "startOffsets": "140143", "endColumns": "82", "endOffsets": "140221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fb1d9df47d7b984f4dadfbaace818bfe\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1942,1968", "startColumns": "4,4", "startOffsets": "128165,129570", "endColumns": "53,66", "endOffsets": "128214,129632"}}, {"source": "C:\\Muslim Core\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2,-1,-1,161,172,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,4,4,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,99,-1,-1,7858,8524,-1,-1,-1,-1,-1", "endLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,56,-1,-1,169,176,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,12,-1,-1,12,12,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,3078,-1,-1,8470,8778,-1,-1,-1,-1,-1"}, "to": {"startLines": "6422,6426,6430,6831,6839,6843,6848,6853,6857,6861,6866,7065,7120,7124,9780,9789,9794,9801,9808,9814,9819", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "445977,446192,446409,472939,473386,473607,473899,474194,474440,474681,474978,490371,493119,493303,672510,673127,673386,673784,674209,674529,674774", "endLines": "6425,6429,6433,6838,6842,6847,6852,6856,6860,6865,6870,7119,7123,7129,9788,9793,9800,9807,9813,9818,9824", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "446187,446404,446618,473381,473602,473894,474189,474435,474676,474973,475273,493114,493298,493715,673122,673381,673779,674204,674524,674769,675122"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e6699e020abbc856e9d4962d528675a8\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "137,227,230,583,639,640,647,648,649,650,651,653,654,661,662,667,668,674,675,676,677,678,679,684,685,743,744,745,746,750,751,752,753,754,755,944,945,946,947,948,949,950,951,952,953,954,955,1020,1021,1022,1023,1024,1025,1026,1027,1042,1043,1044,1045,1046,1047,1053,1054,1055,1056,1077,1078,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1226,1227,1231,1232,1233,1234,1235,1236,1237,1886,1887,1888,1889,1890,1891,1892,1893,1931,1932,1933,1934,1941,1972,1973,1984,2014,2022,2023,2026,2028,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2406,2442,2443,2444,2445,2446,2468,2476,2477,2481,2485,2496,2501,2530,2537,2541,2545,2550,2554,2558,2562,2566,2570,2574,2580,2584,2590,2594,2600,2604,2609,2613,2616,2620,2626,2630,2636,2640,2646,2649,2653,2657,2661,2665,2669,2670,2671,2672,2675,2678,2681,2684,2688,2689,2690,2691,2732,2735,2737,2739,2741,2746,2747,2751,2757,2761,2762,2764,2776,2777,2781,2787,2791,2871,2872,2876,2903,2907,2908,2912,4709,4881,4907,5078,5104,5135,5143,5149,5165,5187,5192,5197,5207,5216,5225,5229,5236,5255,5262,5263,5272,5275,5278,5282,5286,5290,5293,5294,5299,5304,5314,5319,5326,5332,5333,5336,5340,5345,5347,5349,5352,5355,5357,5361,5364,5371,5374,5377,5381,5383,5387,5389,5391,5393,5397,5405,5413,5425,5431,5440,5443,5454,5457,5458,5463,5464,5965,6034,6108,6109,6119,6128,6280,6282,6286,6289,6292,6295,6298,6301,6304,6307,6311,6314,6317,6320,6324,6327,6331,6491,6492,6493,6494,6495,6496,6497,6498,6499,6500,6501,6502,6503,6504,6505,6506,6507,6508,6509,6510,6511,6513,6515,6516,6517,6518,6519,6520,6521,6522,6524,6525,6527,6528,6530,6532,6533,6535,6536,6537,6538,6539,6540,6542,6543,6544,6545,6546,6871,6873,6875,6877,6878,6879,6880,6881,6882,6883,6884,6885,6886,6887,6888,6889,6891,6892,6893,6894,6895,6896,6897,6899,6903,7156,7157,7158,7159,7160,7161,7165,7166,7167,7708,7710,7712,7714,7716,7718,7719,7720,7721,7723,7725,7727,7728,7729,7730,7731,7732,7733,7734,7735,7736,7737,7738,7741,7742,7743,7744,7746,7748,7749,7751,7752,7754,7756,7758,7759,7760,7761,7762,7763,7764,7765,7766,7767,7768,7769,7771,7772,7773,7774,7776,7777,7778,7779,7780,7782,7784,7786,7788,7789,7790,7791,7792,7793,7794,7795,7796,7797,7798,7799,7800,7801,7802", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6941,11486,11639,30035,32433,32488,32945,33009,33079,33140,33215,33337,33414,33851,33936,34264,34340,34682,34759,34837,34943,35049,35128,35457,35514,39516,39590,39665,39730,39925,39985,40046,40118,40191,40258,54372,54431,54490,54549,54608,54667,54721,54775,54828,54882,54936,54990,59794,59868,59947,60020,60094,60165,60237,60309,61110,61167,61225,61298,61372,61446,61763,61835,61908,61978,63069,63129,63320,63389,63458,63528,63602,63678,63742,63819,63895,63972,64037,64106,64183,64258,64327,64395,64472,64538,64599,64696,64761,64830,64929,65000,65059,65117,65174,65233,65297,65368,65440,65512,65584,65656,65723,65791,65859,65918,65981,66045,66135,66226,66286,66352,66419,66485,66555,66619,66672,66739,66800,66867,66980,67038,67101,67166,67231,67306,67379,67451,67495,67542,67588,67637,67698,67759,67820,67882,67946,68010,68074,68139,68202,68262,68323,68389,68448,68508,68570,68641,68701,72842,72928,73178,73268,73355,73443,73525,73608,73698,124770,124822,124880,124925,124991,125055,125112,125169,127623,127680,127728,127777,128131,129794,129841,130382,131941,132337,132401,132591,132704,137488,137562,137632,137710,137764,137834,137919,137967,138013,138074,138137,138203,138267,138338,138401,138466,138530,138591,138652,138704,138777,138851,138920,138995,139069,139143,139284,168477,170954,171032,171122,171210,171306,172815,173397,173486,173733,174014,174680,174965,176774,177251,177473,177695,177971,178198,178428,178658,178888,179118,179345,179764,179990,180415,180645,181073,181292,181575,181783,181914,182141,182567,182792,183219,183440,183865,183985,184261,184562,184886,185177,185491,185628,185759,185864,186106,186273,186477,186685,186956,187068,187180,187285,189378,189592,189738,189878,189964,190312,190400,190646,191064,191313,191395,191493,192150,192250,192502,192926,193181,199337,199426,199663,201687,201929,202031,202284,337364,348045,349561,360256,361784,363541,364167,364587,365848,367113,367369,367605,368152,368646,369251,369449,370029,371397,371772,371890,372428,372585,372781,373054,373310,373480,373621,373685,374050,374417,375093,375357,375695,376048,376142,376328,376634,376896,377021,377148,377387,377598,377717,377910,378087,378542,378723,378845,379104,379217,379404,379506,379613,379742,380017,380525,381021,381898,382192,382762,382911,383643,383815,383899,384235,384327,416653,421884,427599,427661,428239,428823,436770,436883,437112,437272,437424,437595,437761,437930,438097,438260,438503,438673,438846,439017,439291,439490,439695,449707,449791,449887,449983,450081,450181,450283,450385,450487,450589,450691,450791,450887,450999,451128,451251,451382,451513,451611,451725,451819,451959,452093,452189,452301,452401,452517,452613,452725,452825,452965,453101,453265,453395,453553,453703,453844,453988,454123,454235,454385,454513,454641,454777,454909,455039,455169,455281,475278,475424,475568,475706,475772,475862,475938,476042,476132,476234,476342,476450,476550,476630,476722,476820,476930,476982,477060,477166,477258,477362,477472,477594,477757,495228,495308,495408,495498,495608,495698,495939,496033,496139,536016,536116,536228,536342,536458,536574,536668,536782,536894,536996,537116,537238,537320,537424,537544,537670,537768,537862,537950,538062,538178,538300,538412,538587,538703,538789,538881,538993,539117,539184,539310,539378,539506,539650,539778,539847,539942,540057,540170,540269,540378,540489,540600,540701,540806,540906,541036,541127,541250,541344,541456,541542,541646,541742,541830,541948,542052,542156,542282,542370,542478,542578,542668,542778,542862,542964,543048,543102,543166,543272,543358,543468,543552", "endLines": "137,227,230,583,639,640,647,648,649,650,651,653,654,661,662,667,668,674,675,676,677,678,679,684,685,743,744,745,746,750,751,752,753,754,755,944,945,946,947,948,949,950,951,952,953,954,955,1020,1021,1022,1023,1024,1025,1026,1027,1042,1043,1044,1045,1046,1047,1053,1054,1055,1056,1077,1078,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1226,1227,1231,1232,1233,1234,1235,1236,1237,1886,1887,1888,1889,1890,1891,1892,1893,1931,1932,1933,1934,1941,1972,1973,1984,2014,2022,2023,2026,2028,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2406,2442,2443,2444,2445,2446,2475,2476,2480,2484,2488,2500,2506,2536,2540,2544,2549,2553,2557,2561,2565,2569,2573,2579,2583,2589,2593,2599,2603,2608,2612,2615,2619,2625,2629,2635,2639,2645,2648,2652,2656,2660,2664,2668,2669,2670,2671,2674,2677,2680,2683,2687,2688,2689,2690,2691,2734,2736,2738,2740,2745,2746,2750,2756,2760,2761,2763,2775,2776,2780,2786,2790,2791,2871,2875,2902,2906,2907,2911,2939,4880,4906,5077,5103,5134,5142,5148,5164,5186,5191,5196,5206,5215,5224,5228,5235,5254,5261,5262,5271,5274,5277,5281,5285,5289,5292,5293,5298,5303,5313,5318,5325,5331,5332,5335,5339,5344,5346,5348,5351,5354,5356,5360,5363,5370,5373,5376,5380,5382,5386,5388,5390,5392,5396,5404,5412,5424,5430,5439,5442,5453,5456,5457,5462,5463,5468,6033,6103,6108,6118,6127,6128,6281,6285,6288,6291,6294,6297,6300,6303,6306,6310,6313,6316,6319,6323,6326,6330,6334,6491,6492,6493,6494,6495,6496,6497,6498,6499,6500,6501,6502,6503,6504,6505,6506,6507,6508,6509,6510,6512,6514,6515,6516,6517,6518,6519,6520,6521,6523,6524,6526,6527,6529,6531,6532,6534,6535,6536,6537,6538,6539,6541,6542,6543,6544,6545,6546,6872,6874,6876,6877,6878,6879,6880,6881,6882,6883,6884,6885,6886,6887,6888,6890,6891,6892,6893,6894,6895,6896,6898,6902,6906,7156,7157,7158,7159,7160,7164,7165,7166,7167,7709,7711,7713,7715,7717,7718,7719,7720,7722,7724,7726,7727,7728,7729,7730,7731,7732,7733,7734,7735,7736,7737,7740,7741,7742,7743,7745,7747,7748,7750,7751,7753,7755,7757,7758,7759,7760,7761,7762,7763,7764,7765,7766,7767,7768,7770,7771,7772,7773,7775,7776,7777,7778,7779,7781,7783,7785,7787,7788,7789,7790,7791,7792,7793,7794,7795,7796,7797,7798,7799,7800,7801,7802", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "6991,11526,11683,30071,32483,32545,33004,33074,33135,33210,33286,33409,33487,33931,34013,34335,34411,34754,34832,34938,35044,35123,35203,35509,35567,39585,39660,39725,39791,39980,40041,40113,40186,40253,40321,54426,54485,54544,54603,54662,54716,54770,54823,54877,54931,54985,55039,59863,59942,60015,60089,60160,60232,60304,60377,61162,61220,61293,61367,61441,61516,61830,61903,61973,62044,63124,63185,63384,63453,63523,63597,63673,63737,63814,63890,63967,64032,64101,64178,64253,64322,64390,64467,64533,64594,64691,64756,64825,64924,64995,65054,65112,65169,65228,65292,65363,65435,65507,65579,65651,65718,65786,65854,65913,65976,66040,66130,66221,66281,66347,66414,66480,66550,66614,66667,66734,66795,66862,66975,67033,67096,67161,67226,67301,67374,67446,67490,67537,67583,67632,67693,67754,67815,67877,67941,68005,68069,68134,68197,68257,68318,68384,68443,68503,68565,68636,68696,68764,72923,73010,73263,73350,73438,73520,73603,73693,73784,124817,124875,124920,124986,125050,125107,125164,125218,127675,127723,127772,127823,128160,129836,129885,130423,131968,132396,132458,132646,132756,137557,137627,137705,137759,137829,137914,137962,138008,138069,138132,138198,138262,138333,138396,138461,138525,138586,138647,138699,138772,138846,138915,138990,139064,139138,139279,139349,168525,171027,171117,171205,171301,171391,173392,173481,173728,174009,174261,174960,175353,177246,177468,177690,177966,178193,178423,178653,178883,179113,179340,179759,179985,180410,180640,181068,181287,181570,181778,181909,182136,182562,182787,183214,183435,183860,183980,184256,184557,184881,185172,185486,185623,185754,185859,186101,186268,186472,186680,186951,187063,187175,187280,187397,189587,189733,189873,189959,190307,190395,190641,191059,191308,191390,191488,192145,192245,192497,192921,193176,193270,199421,199658,201682,201924,202026,202279,204435,348040,349556,360251,361779,363536,364162,364582,365843,367108,367364,367600,368147,368641,369246,369444,370024,371392,371767,371885,372423,372580,372776,373049,373305,373475,373616,373680,374045,374412,375088,375352,375690,376043,376137,376323,376629,376891,377016,377143,377382,377593,377712,377905,378082,378537,378718,378840,379099,379212,379399,379501,379608,379737,380012,380520,381016,381893,382187,382757,382906,383638,383810,383894,384230,384322,384600,421879,427250,427656,428234,428818,428909,436878,437107,437267,437419,437590,437756,437925,438092,438255,438498,438668,438841,439012,439286,439485,439690,440020,449786,449882,449978,450076,450176,450278,450380,450482,450584,450686,450786,450882,450994,451123,451246,451377,451508,451606,451720,451814,451954,452088,452184,452296,452396,452512,452608,452720,452820,452960,453096,453260,453390,453548,453698,453839,453983,454118,454230,454380,454508,454636,454772,454904,455034,455164,455276,455416,475419,475563,475701,475767,475857,475933,476037,476127,476229,476337,476445,476545,476625,476717,476815,476925,476977,477055,477161,477253,477357,477467,477589,477752,477909,495303,495403,495493,495603,495693,495934,496028,496134,496226,536111,536223,536337,536453,536569,536663,536777,536889,536991,537111,537233,537315,537419,537539,537665,537763,537857,537945,538057,538173,538295,538407,538582,538698,538784,538876,538988,539112,539179,539305,539373,539501,539645,539773,539842,539937,540052,540165,540264,540373,540484,540595,540696,540801,540901,541031,541122,541245,541339,541451,541537,541641,541737,541825,541943,542047,542151,542277,542365,542473,542573,542663,542773,542857,542959,543043,543097,543161,543267,543353,543463,543547,543667"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f2e0173b205ab009bcb83bc37b0023a7\\transformed\\navigation-fragment-2.7.4\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "1878,1958,1979,1980", "startColumns": "4,4,4,4", "startOffsets": "124193,128982,130121,130180", "endColumns": "62,56,58,48", "endOffsets": "124251,129034,130175,130224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\657d4b970ca786a19c67b9546b583e6a\\transformed\\firebase-messaging-23.3.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2221", "startColumns": "4", "startOffsets": "147579", "endColumns": "81", "endOffsets": "147656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fd9e756f56bf090cd291cb3d53d819c4\\transformed\\constraintlayout-2.1.4\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,9,11,17,18,26,28,29,36,42,44,45,46,47,48,105,106,107,108,110,114,115,116,119,129,139,167,168,173,174,179,184,185,186,191,192,197,198,203,204,205,211,212,213,218,224,225,239,240,246,247,248,249,252,255,258,259,262,265,266,267,268,269,272,275,276,277,278,284,289,292,295,296,297,302,303,304,307,310,311,314,317,320,323,324,325,328,331,332,337,338,344,349,352,355,356,357,358,359,360,361,362,363,364,365,366,382,464,465,466,467,472,479,485,486,487,490,495,498,506,507,528,544,581,582,586,587,597,598,599,605,608,614,618,619,620,621,622,631,1950,2016", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,434,546,781,842,1133,1240,1290,1692,1999,2119,2174,2234,2299,2358,5544,5596,5657,5719,5826,5959,6011,6061,6222,6629,7052,8994,9053,9250,9307,9502,9683,9737,9794,9986,10044,10240,10296,10490,10547,10598,10820,10872,10927,11117,11333,11383,12132,12188,12394,12455,12515,12585,12718,12849,12977,13045,13174,13300,13362,13425,13493,13560,13683,13808,13875,13940,14005,14294,14475,14596,14717,14783,14850,15060,15129,15195,15320,15446,15513,15639,15766,15891,16018,16074,16139,16265,16388,16453,16661,16728,17016,17196,17316,17436,17501,17563,17625,17689,17751,17810,17870,17931,17992,18051,18111,18771,23777,23828,23877,23925,24212,24504,24734,24781,24841,24947,25127,25299,25634,25688,26853,27595,29925,29976,30185,30237,30612,30671,30725,30963,31141,31343,31482,31528,31583,31628,31672,32020,128551,132021", "endLines": "8,9,15,17,25,26,28,29,36,42,44,45,46,47,48,105,106,107,108,113,114,115,116,128,136,139,167,172,173,178,183,184,185,190,191,196,197,202,203,204,210,211,212,217,223,224,225,239,245,246,247,248,251,254,257,258,261,264,265,266,267,268,271,274,275,276,277,283,288,291,294,295,296,301,302,303,306,309,310,313,316,319,322,323,324,327,330,331,336,337,343,348,351,354,355,356,357,358,359,360,361,362,363,364,365,381,387,464,465,466,467,478,484,485,486,489,494,495,505,506,507,528,544,581,582,586,595,597,598,604,605,613,617,618,619,620,621,630,634,1950,2016", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44", "endOffsets": "429,485,727,837,1128,1180,1285,1338,1735,2045,2169,2229,2294,2353,2415,5591,5652,5714,5760,5954,6006,6056,6107,6624,6936,7092,9048,9245,9302,9497,9678,9732,9789,9981,10039,10235,10291,10485,10542,10593,10815,10867,10922,11112,11328,11378,11430,12183,12389,12450,12510,12580,12713,12844,12972,13040,13169,13295,13357,13420,13488,13555,13678,13803,13870,13935,14000,14289,14470,14591,14712,14778,14845,15055,15124,15190,15315,15441,15508,15634,15761,15886,16013,16069,16134,16260,16383,16448,16656,16723,17011,17191,17311,17431,17496,17558,17620,17684,17746,17805,17865,17926,17987,18046,18106,18766,19017,23823,23872,23920,23978,24499,24729,24776,24836,24942,25122,25176,25629,25683,25739,26894,27637,29971,30030,30232,30562,30666,30720,30958,31013,31338,31477,31523,31578,31623,31667,32015,32152,128587,132061"}}, {"source": "C:\\Muslim Core\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "652,655,656,659,660,663,664,665,666,669,670,671,672,673,700,701,702,703,704,705,706,707,747,748,749,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,1011,1013,1014,1015,1016,1017,1018,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1048,1049,1050,1051,1052,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1079,1080,1081", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "33291,33492,33540,33746,33796,34018,34076,34139,34199,34416,34469,34527,34582,34642,36623,36679,36738,36803,36870,36920,36973,37031,39796,39838,39885,40326,40365,40412,40464,40517,40570,40624,40680,40737,40793,40851,40912,40979,41048,41100,41155,41215,59319,59440,59492,59537,59584,59630,59676,60382,60436,60489,60540,60591,60638,60685,60732,60783,60839,60891,60950,61013,61061,61521,61574,61622,61671,61721,62049,62095,62146,62194,62244,62291,62343,62392,62446,62494,62547,62596,62647,62703,62761,62814,62869,62918,62969,63018,63190,63238,63280", "endColumns": "45,47,45,49,54,57,62,59,64,52,57,54,59,39,55,58,64,66,49,52,57,61,41,46,39,38,46,51,52,52,53,55,56,55,57,60,66,68,51,54,59,61,57,51,44,46,45,45,48,53,52,50,50,46,46,46,50,55,51,58,62,47,48,52,47,48,49,41,45,50,47,49,46,51,48,53,47,52,48,50,55,57,52,54,48,50,48,50,47,41,39", "endOffsets": "33332,33535,33581,33791,33846,34071,34134,34194,34259,34464,34522,34577,34637,34677,36674,36733,36798,36865,36915,36968,37026,37088,39833,39880,39920,40360,40407,40459,40512,40565,40619,40675,40732,40788,40846,40907,40974,41043,41095,41150,41210,41272,59372,59487,59532,59579,59625,59671,59720,60431,60484,60535,60586,60633,60680,60727,60778,60834,60886,60945,61008,61056,61105,61569,61617,61666,61716,61758,62090,62141,62189,62239,62286,62338,62387,62441,62489,62542,62591,62642,62698,62756,62809,62864,62913,62964,63013,63064,63233,63275,63315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f2278bd78dd5b8de51d11a4c518bddc3\\transformed\\activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1974,2018", "startColumns": "4,4", "startOffsets": "129890,132109", "endColumns": "41,59", "endOffsets": "129927,132164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\52cfa56fd2ad7827c32e9e051048beda\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2020", "startColumns": "4", "startOffsets": "132223", "endColumns": "49", "endOffsets": "132268"}}]}, {"outputFile": "com.muslimcore.app-mergeDebugResources-3:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e661d63d9c04259b2495b443a4caf917\\transformed\\window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1"}, "to": {"startLines": "531,536,537,538,1935", "startColumns": "4,4,4,4,4", "startOffsets": "27008,27187,27247,27299,127828", "endLines": "535,536,537,538,1935", "endColumns": "11,59,51,44,59", "endOffsets": "27182,27242,27294,27339,127883"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\23d7c820469d7d72a3f613bbedf53593\\transformed\\material-1.10.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "10,16,27,30,31,32,33,34,37,38,39,40,41,43,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,117,118,140,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,226,228,229,231,232,233,234,235,236,237,238,388,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,460,468,469,470,496,509,510,511,512,513,514,515,516,517,518,519,520,524,525,526,527,529,539,540,541,542,543,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,584,585,596,606,607,645,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1163,1169,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1894,1895,1947,1948,1949,1951,1952,1953,1954,1955,1956,1959,1960,1961,1962,1963,1964,1975,1978,1981,1982,2001,2002,2003,2004,2005,2006,2007,2015,2024,2025,2030,2031,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2093,2095,2142,2148,2149,2150,2151,2152,2153,2170,2171,2172,2173,2206,2210,2215,2216,2217,2229,2231,2232,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2290,2293,2296,2299,2300,2301,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2345,2346,2347,2348,2369,2370,2371,2372,2373,2407,2410,2411,2413,2414,2447,2451,2455,2459,2463,2464,2507,2515,2522,2692,2695,2705,2714,2723,2792,2793,2794,2795,2801,2802,2803,2804,2805,2806,2812,2813,2814,2815,2816,2821,2822,2826,2827,2833,2837,2838,2839,2840,2850,2851,2852,2856,2857,2863,2867,2940,2943,2944,2948,2949,2952,2953,2954,2955,3218,3225,3485,3491,3754,3761,4021,4027,4090,4172,4224,4306,4368,4450,4514,4566,4648,4656,4662,4673,4677,4681,4694,5469,5485,5492,5498,5515,5528,5548,5565,5574,5579,5586,5606,5619,5636,5642,5648,5655,5659,5665,5679,5682,5692,5693,5694,5742,5746,5750,5754,5755,5756,5759,5775,5782,5796,5841,5871,5877,5881,5885,5890,5897,5903,5904,5907,5911,5916,5929,5933,5938,5943,5948,5951,5954,5957,5961,6104,6105,6106,6107,6335,6336,6337,6338,6339,6340,6341,6342,6343,6344,6345,6346,6347,6348,6349,6350,6351,6352,6353,6357,6361,6365,6369,6373,6377,6381,6382,6383,6384,6385,6386,6387,6388,6392,6396,6397,6401,6402,6405,6409,6412,6415,6418,6434,6437,6440,6444,6448,6452,6456,6459,6460,6461,6462,6465,6469,6472,6475,6478,6481,6484,6487,6558,6561,6562,6565,6568,6569,6572,6573,6574,6578,6579,6584,6591,6598,6605,6612,6619,6626,6633,6640,6647,6656,6665,6674,6681,6690,6699,6702,6705,6706,6707,6708,6709,6710,6711,6712,6713,6714,6715,6716,6717,6721,6726,6731,6734,6735,6736,6737,6738,6746,6754,6755,6763,6767,6775,6783,6791,6799,6807,6808,6816,6824,6825,6828,6907,6909,6914,6916,6921,6925,6929,6930,6931,6932,6936,6940,6941,6945,6946,6947,6948,6949,6950,6951,6952,6953,6954,6955,6956,6957,6958,6959,6960,6964,6968,6969,6973,6974,6975,6980,6981,6982,6983,6984,6985,6986,6987,6988,6989,6990,6991,6992,6993,6994,6995,6996,6997,6998,6999,7000,7004,7005,7006,7012,7013,7017,7019,7020,7025,7026,7027,7028,7029,7030,7034,7035,7036,7042,7043,7047,7049,7053,7057,7061,7168,7169,7170,7171,7174,7177,7180,7183,7186,7191,7195,7198,7199,7204,7208,7213,7219,7225,7230,7234,7239,7243,7247,7288,7289,7290,7291,7292,7296,7297,7298,7299,7303,7307,7311,7315,7319,7323,7327,7331,7337,7338,7379,7393,7398,7424,7431,7434,7445,7450,7453,7456,7511,7517,7518,7521,7524,7527,7530,7533,7536,7539,7543,7546,7547,7548,7556,7564,7567,7572,7577,7582,7587,7591,7595,7596,7604,7605,7606,7607,7608,7616,7621,7626,7627,7628,7629,7654,7660,7665,7668,7672,7675,7679,7689,7692,7697,7700,7704,7805,7813,7827,7840,7844,7859,7870,7873,7884,7889,7893,7928,7929,7930,7941,7948,7955,7962,7969,7989,7992,8019,8024,8044,8047,8054,8067,8076,8079,8099,8109,8113,8117,8130,8134,8138,8142,8148,8152,8169,8177,8181,8185,8189,8192,8196,8200,8204,8214,8221,8228,8232,8258,8268,8293,8302,8322,8332,8336,8346,8371,8381,8384,8388,8389,8390,8391,8395,8401,8407,8408,8421,8422,8423,8426,8429,8432,8435,8438,8441,8444,8447,8450,8453,8456,8459,8462,8465,8468,8471,8474,8477,8480,8483,8486,8487,8492,8493,8506,8516,8520,8525,8530,8534,8537,8541,8545,8548,8552,8555,8559,8564,8569,8572,8579,8583,8587,8596,8601,8606,8607,8611,8614,8618,8631,8636,8644,8648,8652,8669,8673,8678,8696,8703,8706,8736,8739,8742,8745,8748,8751,8754,8773,8779,8787,8794,8806,8814,8819,8823,8827,8838,8842,8850,8853,8858,8859,8860,8861,8865,8869,8873,8877,8912,8915,8919,8923,8957,8960,8964,8968,8977,8983,8986,8996,9000,9001,9008,9012,9019,9020,9021,9024,9029,9034,9035,9039,9054,9073,9077,9078,9090,9100,9101,9113,9118,9142,9145,9151,9154,9163,9171,9175,9178,9181,9184,9188,9191,9208,9212,9215,9230,9233,9241,9246,9253,9258,9259,9264,9265,9271,9277,9283,9315,9326,9343,9350,9354,9357,9370,9379,9383,9388,9392,9396,9400,9404,9408,9412,9416,9421,9424,9436,9441,9450,9453,9460,9461,9465,9474,9480,9484,9485,9489,9510,9516,9520,9524,9525,9543,9544,9545,9546,9547,9552,9555,9556,9562,9563,9575,9587,9594,9595,9600,9605,9606,9610,9624,9629,9635,9641,9647,9652,9658,9664,9665,9671,9686,9691,9700,9709,9712,9726,9731,9742,9746,9755,9764,9765,9772,14514,14515,14516,14517,14518,14519,14520,14521,14522,14523,14524,14525,14526,14527,14528,14529,14530,14531,14532,14533,14534,14535,14536,14537,14538,14539,14540,14541,14542,14543,14544,14545,14546,14547,14548,14549,14550,14551,14552,14553,14554,14555,14556,14557,14558,14559,14560,14561,14562,14563,14564,14565,14566,14567,14568,14569,14570,14571,14572,14573,14574,14575,14576,14577,14578,14579,14580,14581,14582,14583,14584,14585,14586,14587,14588,14589,14590,14591,14592,14593,14594,14595,14596,14597,14598,14599,14600,14601,14602,14603,14604,14605,14606,14607,14608,14609,14610,14611,14612,14613,14614,14615,14616,14617,14618,14619,14620,14621,14622,14623,14624,14625,14626,14627,14628,14629,14630,14631,14632,14633,14634,14635,14636,14637,14638,14639,14640,14641,14642,14643,14644,14645,14646,14647,14648,14649,14650,14651,14652,14653,14654,14655,14656,14657,14658,14659,14660,14661,14662,14663,14664,14665,14666,14667,14668,14669,14670,14671,14672,14673,14674,14675,14676,14677,14678,14679,14680,14681,14682,14683,14684,14685,14686,14687,14688,14689,14690,14691,14692,14693,14694,14695,14696,14697,14698,14699,14700,14701,14702,14703,14704,14705,14706,14707,14708,14709,14710,14711,14712,14713,14714,14715,14716,14717,14718,14719,14720,14721,14722,14723,14724,14725,14726,14727,14728,14729,14730,14731,14732,14733,14734,14735,14736,14737,14738,14739,14740,14741,14742,14743,14744,14745,14746,14747,14748,14749,14750,14751,14752,14753,14754,14755,14756,14757,14758,14759,14760,14761,14762,14763,14764,14765,14766,14767,14768,14769,14770,14771,14772,14773,14774,14775,14776,14777,14778,14779,14780,14781,14782,14783,14784,14785,14786,14787,14788,14789,14790,14791,14792,14793,14794,14795,14796,14797,14798,14799,14800,14801,14802,14803,14804,14805,14806,14807,14808,14809,14810,14811,14812,14813,14814,14815,14816,14817,14818,14819,14820,14821,14822,14823,14824,14825,14826,14827,14828,14829,14830,14831,14832,14833,14834,14835,14836,14837,14838,14839,14840,14841,14842,14843,14844,14845,14846,14847,14848,14849,14850,14851,14852,14853,14854,14855,14856,14857,14858,14859,14860,14861,14862,14863,14864,14865,14866,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "490,732,1185,1343,1399,1459,1520,1585,1740,1790,1840,1893,1951,2050,2420,2468,2539,2611,2683,2756,2823,2872,2926,2963,3014,3074,3121,3177,3226,3284,3338,3399,3455,3506,3566,3622,3685,3734,3790,3846,3896,3955,4010,4072,4119,4173,4229,4281,4336,4390,4444,4498,4547,4605,4659,4716,4772,4819,4872,4928,4988,5051,5110,5172,5222,5276,5330,5378,5435,5488,6112,6166,7097,7208,7270,7326,7386,7439,7500,7579,7660,7732,7811,7891,7967,8045,8114,8190,8267,8338,8411,8487,8565,8634,8710,8787,8851,8922,11435,11531,11584,11688,11755,11808,11860,11910,11968,12033,12081,19022,19152,19218,19276,19345,19403,19472,19542,19615,19689,19757,19824,19894,19960,20033,20093,20169,20229,20289,20364,20432,20498,20566,20626,20685,20742,20808,20870,20927,20995,21068,21138,21200,21261,21329,21391,21461,21530,21586,21645,21707,21769,21836,21893,21954,22015,22076,22137,22193,22249,22305,22361,22419,22477,22535,22593,22650,22707,22764,22821,22880,22939,22997,23080,23163,23236,23290,23359,23415,23496,23577,23648,23983,24036,24094,25181,25800,25846,25906,25960,26030,26100,26165,26231,26296,26364,26433,26501,26631,26684,26743,26801,26899,27344,27396,27442,27492,27548,27642,27700,27758,27820,27883,27945,28004,28064,28129,28195,28260,28322,28384,28446,28508,28570,28632,28698,28765,28831,28894,28958,29021,29089,29150,29212,29274,29337,29401,29464,29528,29606,29665,29731,29811,29872,30076,30134,30567,31018,31082,32822,37093,37167,37238,37304,37378,37447,37518,37591,37662,37730,37803,37879,37949,38027,38095,38161,38222,38291,38355,38421,38489,38555,38618,38686,38757,38822,38895,38958,39039,39103,39169,39239,39309,39379,39449,41277,41334,41392,41451,41511,41570,41629,41688,41747,41806,41865,41924,41983,42042,42101,42161,42222,42284,42345,42406,42467,42528,42589,42650,42710,42771,42832,42892,42953,43014,43075,43136,43197,43258,43319,43380,43441,43502,43563,43631,43700,43770,43839,43908,43977,44046,44115,44184,44253,44322,44391,44460,44520,44581,44643,44704,44765,44826,44887,44948,45009,45070,45131,45192,45253,45315,45378,45442,45505,45568,45631,45694,45757,45820,45883,45946,46009,46072,46133,46195,46258,46320,46382,46444,46506,46568,46630,46692,46754,46816,46878,46935,47022,47102,47192,47287,47379,47471,47561,47644,47737,47824,47921,48012,48113,48200,48303,48392,48491,48583,48683,48767,48861,48949,49047,49131,49222,49316,49415,49517,49615,49715,49802,49902,49988,50084,50172,50253,50344,50440,50533,50626,50717,50802,50896,50985,51083,51176,51278,51366,51470,51561,51661,51754,51855,51940,52035,52124,52223,52308,52400,52495,52595,52698,52797,52900,52989,53090,53177,53274,53362,53458,53550,53650,53740,53838,53923,54012,54101,54194,54281,55044,55110,55186,55255,55334,55407,55487,55567,55644,55712,55790,55866,55937,56018,56091,56174,56249,56334,56407,56488,56569,56643,56727,56797,56875,56945,57025,57103,57175,57257,57327,57404,57484,57569,57657,57741,57828,57902,57980,58058,58129,58210,58301,58384,58480,58578,58685,58750,58816,58869,58945,59011,59098,59174,68769,69147,69742,69796,69875,69953,70026,70091,70154,70220,70291,70362,70432,70494,70563,70629,70689,70756,70823,70879,70930,70983,71035,71089,71160,71223,71282,71344,71403,71476,71543,71613,71673,71736,71811,71883,71979,72050,72106,72177,72234,72291,72357,72421,72492,72549,72602,72665,72717,72775,74012,74081,74147,74206,74289,74348,74405,74472,74542,74616,74678,74747,74817,74916,75013,75112,75198,75284,75365,75440,75529,75620,75704,75763,75809,75875,75932,75999,76056,76138,76203,76269,76392,76476,76597,76662,76724,76822,76896,76979,77068,77132,77211,77285,77347,77443,77508,77567,77623,77679,77739,77846,77893,77953,78014,78078,78139,78199,78257,78300,78349,78401,78452,78504,78553,78602,78667,78733,78793,78854,78910,78969,79018,79066,79124,79181,79283,79340,79415,79463,79514,79576,79641,79693,79767,79830,79893,79961,80011,80073,80133,80190,80250,80299,80367,80473,80575,80644,80715,80771,80820,80920,80991,81101,81190,81281,81363,81461,81517,81618,81728,81827,81890,81996,82073,82185,82312,82424,82551,82621,82735,82866,82963,83031,83149,83252,83370,83431,83505,83572,83677,83799,83873,83940,84050,84149,84222,84319,84441,84559,84677,84738,84860,84977,85045,85151,85253,85333,85404,85500,85567,85641,85715,85801,85889,85979,86057,86134,86234,86305,86426,86547,86611,86736,86810,86934,87058,87125,87234,87362,87474,87553,87631,87732,87803,87925,88047,88112,88238,88350,88456,88524,88623,88727,88790,88856,88940,89053,89166,89284,89362,89434,89570,89706,89791,89931,90069,90207,90349,90431,90540,90651,90779,90907,91039,91169,91299,91433,91495,91591,91658,91775,91896,91993,92075,92162,92249,92380,92511,92646,92723,92800,92911,93025,93099,93208,93320,93422,93518,93622,93689,93783,93855,93965,94071,94144,94235,94337,94440,94535,94642,94747,94869,94991,95117,95176,95234,95358,95482,95610,95728,95846,95968,96054,96151,96285,96419,96499,96637,96769,96901,97037,97112,97188,97291,97365,97478,97559,97616,97677,97736,97796,97854,97915,97973,98023,98072,98139,98198,98257,98306,98377,98461,98532,98612,98681,98744,98812,98878,98946,99011,99077,99154,99232,99338,99444,99540,99669,99758,99885,99951,100020,100106,100172,100255,100353,100449,100545,100643,100752,100847,100936,100998,101058,101123,101180,101261,101315,101372,101469,101579,101640,101755,101876,101971,102063,102156,102258,102314,102373,102422,102514,102563,102617,102671,102725,102779,102833,102888,102998,103108,103216,103326,103436,103546,103656,103764,103870,103974,104078,104182,104277,104372,104465,104558,104662,104768,104872,104976,105069,105162,105255,105348,105456,105562,105668,105774,105871,105966,106061,106156,106262,106368,106474,106580,106678,106774,106870,106968,107033,107137,107195,107259,107320,107382,107442,107504,107572,107630,107693,107756,107823,107898,107971,108037,108089,108142,108194,108251,108335,108430,108515,108596,108676,108753,108832,108909,108983,109057,109128,109208,109280,109355,109420,109481,109541,109616,109690,109767,109840,109910,109982,110052,110125,110189,110259,110305,110374,110426,110511,110594,110651,110717,110784,110850,110931,111006,111062,111115,111176,111234,111284,111333,111382,111431,111493,111545,111590,111671,111722,111776,111829,111883,111934,111983,112049,112100,112161,112222,112284,112334,112375,112452,112511,112570,112629,112690,112746,112802,112869,112930,112995,113050,113115,113184,113252,113330,113399,113459,113530,113604,113669,113741,113811,113878,113962,114031,114098,114168,114231,114298,114366,114449,114528,114618,114695,114763,114830,114908,114965,115022,115090,115156,115212,115272,115331,115385,115435,115485,115533,115595,115646,115719,115799,115879,115943,116010,116081,116139,116200,116266,116325,116392,116452,116512,116575,116643,116704,116771,116849,116919,116968,117025,117094,117155,117243,117331,117419,117507,117594,117681,117768,117855,117913,117987,118057,118113,118184,118249,118311,118386,118459,118549,118615,118681,118742,118806,118868,118926,118997,119080,119139,119210,119276,119341,119402,119461,119532,119598,119663,119746,119822,119897,119978,120038,120107,120177,120246,120301,120357,120413,120474,120532,120588,120642,120697,120759,120816,120910,120979,121080,121131,121201,121264,121320,121378,121437,121491,121577,121661,121731,121800,121870,121985,122106,122173,122240,122315,122382,122441,122495,122549,122603,122656,122708,125223,125360,128402,128451,128501,128592,128640,128696,128754,128816,128871,129039,129110,129174,129233,129295,129361,129932,130077,130229,130274,131293,131344,131391,131436,131487,131538,131589,131973,132463,132529,132829,132892,133032,133089,133143,133198,133256,133311,133370,133426,133495,133564,133633,133703,133766,133829,133892,133955,134020,134085,134150,134215,134278,134342,134406,134470,134521,134599,134677,134748,134820,134893,134965,135031,135097,135165,135233,135299,135366,135440,135503,135560,135620,135685,135752,135817,135874,135935,135993,136097,136207,136316,136420,136498,136563,136630,136696,136766,136813,136865,137041,137168,140334,140735,140866,141050,141228,141466,141655,142870,142968,143083,143168,146332,146574,146961,147050,147207,148270,148484,148638,149149,149336,149432,149522,149618,149708,149874,149997,150120,150290,150396,150511,150626,150728,150834,150951,151108,151190,151363,151531,151679,151838,151993,152166,152283,152400,152568,152680,152794,152966,153142,153300,153433,153545,153691,153843,153975,154118,154240,154418,154554,154650,154786,154881,155048,155141,155233,155420,155576,155754,155918,156100,156417,156599,156781,156971,157203,157393,157570,157732,157889,157999,158182,158319,158539,158723,158907,159067,159225,159409,159636,159839,160010,160230,160452,160607,160807,160991,161094,161284,161425,161590,161761,161961,162165,162367,162532,162737,162936,163135,163332,163423,163572,163722,163806,163955,164100,164252,164393,164559,165841,165919,166220,166386,166541,168530,168688,168852,169078,169301,171396,171673,171945,172223,172468,172530,175358,175809,176265,187402,187550,188064,188501,188935,193275,193360,193481,193580,193985,194082,194199,194286,194409,194510,194916,195015,195134,195227,195334,195677,195784,196029,196150,196559,196807,196907,197012,197131,197640,197787,197906,198157,198290,198705,198959,204440,204687,204812,205129,205250,205478,205599,205732,205879,226510,227002,247382,247806,268482,268976,289401,289827,294668,300085,304176,309607,314349,319726,323710,327702,333093,333640,334073,334829,335059,335302,336435,384605,385509,386063,386516,387946,388690,389883,390937,391415,391708,392091,393606,394371,395514,395955,396396,396962,397236,397647,398663,398841,399594,399731,399822,402016,402282,402604,402814,402923,403042,403226,404344,404814,405565,408148,410229,410605,410833,411089,411348,411924,412278,412400,412539,412831,413091,414019,414305,414708,415110,415453,415665,415866,416079,416368,427255,427328,427415,427500,440025,440137,440243,440366,440498,440621,440751,440875,441008,441139,441264,441381,441501,441633,441761,441875,441993,442106,442227,442415,442602,442783,442966,443150,443315,443497,443617,443737,443845,443955,444067,444175,444285,444450,444616,444768,444933,445034,445154,445325,445486,445649,445810,446623,446742,446859,447039,447221,447402,447585,447740,447885,448007,448142,448305,448498,448624,448776,448918,449088,449244,449416,456179,456374,456466,456639,456801,456896,457065,457159,457248,457491,457580,457873,458289,458709,459130,459556,459973,460389,460806,461224,461638,462108,462581,463053,463464,463935,464407,464597,464803,464909,465017,465123,465235,465349,465461,465575,465691,465805,465913,466023,466131,466393,466772,467176,467323,467431,467541,467649,467763,468172,468586,468702,469120,469361,469791,470226,470636,471058,471468,471590,471999,472415,472537,472755,477914,477982,478326,478406,478762,478912,479056,479132,479244,479334,479596,479861,479969,480121,480229,480305,480417,480507,480609,480717,480825,480925,481033,481118,481222,481309,481387,481501,481593,481857,482124,482234,482387,482497,482581,482970,483068,483176,483270,483400,483508,483630,483766,483874,483994,484128,484250,484378,484520,484646,484786,484912,485030,485162,485260,485370,485670,485782,485900,486364,486480,486783,486909,487005,487406,487516,487640,487778,487888,488010,488322,488446,488576,489052,489180,489495,489633,489795,490011,490167,496231,496299,496383,496487,496690,496879,497080,497273,497478,497791,498003,498167,498283,498529,498745,499058,499484,499946,500183,500335,500595,500739,500881,504113,504227,504347,504463,504557,504878,504977,505095,505196,505475,505760,506039,506321,506574,506833,507086,507342,507766,507842,511092,512447,512891,514745,515320,515528,516538,516918,517084,517225,522245,522671,522783,522918,523071,523268,523439,523622,523797,523984,524256,524414,524498,524602,525089,525645,525803,526022,526253,526476,526711,526933,527199,527337,527936,528050,528188,528300,528424,528995,529490,530036,530181,530274,530366,532293,532863,533161,533350,533556,533749,533959,534843,534988,535380,535538,535755,543811,544243,545118,545738,545935,546883,547648,547771,548544,548765,548965,550942,551042,551132,551717,552387,553069,553749,554441,555654,555819,557432,557753,558816,558986,559556,560451,561084,561250,562736,563352,563588,563809,564767,565032,565297,565544,565958,566194,567479,567928,568115,568364,568606,568782,569023,569256,569481,570076,570551,571075,571336,572687,573162,574388,574858,575906,576358,576602,577059,578304,578787,578937,579281,579427,579565,579701,579989,580493,581002,581118,582020,582142,582254,582431,582697,582967,583233,583501,583757,584017,584273,584531,584783,585039,585291,585545,585777,586013,586265,586521,586773,587027,587259,587493,587605,588030,588154,589246,590061,590257,590581,590970,591322,591563,591777,592076,592268,592583,592790,593136,593436,593837,594056,594469,594706,595076,595800,596155,596424,596564,596818,596962,597239,598231,598640,599272,599618,599986,601060,601423,601823,603331,603916,604141,606670,606864,607082,607308,607520,607719,607926,609130,609425,609982,610372,611004,611472,611717,611974,612220,612980,613244,613667,613858,614237,614325,614433,614541,614854,615179,615498,615829,618532,618720,618981,619230,621814,622006,622271,622524,623056,623464,623663,624247,624482,624606,625018,625232,625634,625737,625867,626042,626294,626490,626630,626824,627835,628904,629192,629322,630099,630756,630902,631608,631846,633386,633536,633953,634118,634804,635274,635470,635561,635645,635789,636023,636190,637118,637404,637564,638179,638338,638666,638893,639405,639767,639846,640185,640290,640655,641026,641387,643261,643890,644966,645390,645643,645795,646843,647580,647783,648029,648276,648494,648736,649057,649321,649626,649849,650160,650349,651064,651333,651827,652053,652493,652652,652936,653681,654046,654351,654509,654747,656066,656464,656692,656912,657054,658344,658450,658580,658718,658842,659130,659299,659399,659684,659798,660681,661436,661875,661999,662245,662438,662572,662763,663542,663760,664051,664330,664647,664869,665164,665447,665551,665892,666708,667024,667585,668091,668296,669082,669487,670148,670337,670888,671454,671574,671976,823927,824022,824115,824178,824260,824353,824437,824524,824622,824713,824804,824892,824976,825072,825176,825263,825369,825472,825573,825677,825783,825882,825988,826090,826197,826306,826417,826548,826668,826784,826902,827001,827108,827224,827343,827471,827547,827642,827719,827808,827899,827992,828066,828151,828234,828332,828431,828535,828631,828733,828836,828936,829026,829111,829212,829310,829400,829495,829582,829688,829790,829884,829975,830056,830132,830224,830313,830433,830544,830627,830713,830808,830896,830992,831080,831181,831282,831376,831482,831580,831677,831772,831870,831973,832073,832176,832281,832399,832515,832610,832703,832788,832884,832978,833070,833172,833270,833344,833449,833549,833650,833755,833855,833956,834055,834157,834251,834358,834460,834563,834647,834743,834845,834948,835044,835146,835249,835346,835449,835547,835651,835756,835853,835961,836075,836190,836298,836412,836527,836629,836734,836842,836952,837068,837185,837272,837367,837464,837563,837668,837774,837873,837978,838084,838184,838290,838391,838498,838617,838716,838818,838920,839020,839123,839218,839322,839407,839511,839615,839713,839817,839923,840021,840143,840241,840354,840448,840537,840626,840709,840800,840883,840981,841071,841167,841256,841350,841438,841534,841619,841727,841828,841929,842027,842133,842224,842323,842420,842518,842614,842707,842817,842915,843010,843120,843212,843312,843411,843498,843602,843707,843806,843913,844020,844119,844228,844320,844431,844542,844653,844757,844872,844988,845115,845235,845332,845431,845523,845622,845714,845813,845899,845993,846096,846192,846295,846391,846494,846591,846689,846792,846872,846980,847070,847171,847254,847345,847430,847522,847625,847720,847816,847909,847990,848099,848178,848285,848376,848475,848568,848671,848775,848876,848977,849081,849175,849279,849383,849496,849602,849708,849816,849933,850022,850130,850230,850333,850420,850527,850606,850696,850780,850872,850945,851033,851115,851200,851285,851382,851475,851570,851669,851766,851857,851948,852040,852135,852233,852332,852434,852531,852628,852721,852808,852892,852989,853086,853179,853266,853357,853456,853555,853650,853739,853820,853919,854014,854111,854207,854304,854388,854487,854582,854679,854775,854872,854961,855062,855159,855258,855356,855455,855545,855636,855725,855814,855896,855989,856080,856191,856292,856392,856504,856617,856706,856814,856895,856995,857084,857176,857287,857397,857492,857608,857734,857860,857979,858107,858232,858357,858475,858602,858711,858820,858933,859056,859179,859295,859420,859517,859625,859738,859854,859970,860079,860167,860268,860357,860458,860545,860633,860730,860822,860922,860998", "endLines": "10,16,27,30,31,32,33,34,37,38,39,40,41,43,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,117,118,140,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,226,228,229,231,232,233,234,235,236,237,238,388,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450,451,452,453,454,455,456,457,458,459,463,468,469,470,496,509,510,511,512,513,514,515,516,517,518,519,523,524,525,526,527,529,539,540,541,542,543,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,584,585,596,606,607,645,708,709,710,711,712,713,714,715,716,717,718,719,720,721,722,723,724,725,726,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,804,805,806,807,808,809,810,811,812,813,814,815,816,817,818,819,820,821,822,823,824,825,826,827,828,829,830,831,832,833,834,835,836,837,838,839,840,841,842,843,844,845,846,847,848,849,850,851,852,853,854,855,856,857,858,859,860,861,862,863,864,865,866,867,868,869,870,871,872,873,874,875,876,877,878,879,880,881,882,883,884,885,886,887,888,889,890,891,892,893,894,895,896,897,898,899,900,901,902,903,904,905,906,907,908,909,910,911,912,913,914,915,916,917,918,919,920,921,922,923,924,925,926,927,928,929,930,931,932,933,934,935,936,937,938,939,940,941,942,943,956,957,958,959,960,961,962,963,964,965,966,967,968,969,970,971,972,973,974,975,976,977,978,979,980,981,982,983,984,985,986,987,988,989,990,991,992,993,994,995,996,997,998,999,1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1163,1169,1178,1179,1180,1181,1182,1183,1184,1185,1186,1187,1188,1189,1190,1191,1192,1193,1194,1195,1196,1197,1198,1199,1200,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1241,1242,1243,1244,1245,1246,1247,1248,1249,1250,1251,1252,1253,1254,1255,1256,1257,1258,1259,1260,1261,1262,1263,1264,1265,1266,1267,1268,1269,1270,1271,1272,1273,1274,1275,1276,1277,1278,1279,1280,1281,1282,1283,1284,1285,1286,1287,1288,1289,1290,1291,1292,1293,1294,1295,1296,1297,1298,1299,1300,1301,1302,1303,1304,1305,1306,1307,1308,1309,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1344,1345,1346,1347,1348,1349,1350,1351,1352,1353,1354,1355,1356,1357,1358,1359,1360,1361,1362,1363,1364,1365,1366,1367,1368,1369,1370,1371,1372,1373,1374,1375,1376,1377,1378,1379,1380,1381,1382,1383,1384,1385,1386,1387,1388,1389,1390,1391,1392,1393,1394,1395,1396,1397,1398,1399,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1419,1420,1421,1422,1423,1424,1425,1426,1427,1428,1429,1430,1431,1432,1433,1434,1435,1436,1437,1438,1439,1440,1441,1442,1443,1444,1445,1446,1447,1448,1449,1450,1451,1452,1453,1454,1455,1456,1457,1458,1459,1460,1461,1462,1463,1464,1465,1466,1467,1468,1469,1470,1471,1472,1473,1474,1475,1476,1477,1478,1479,1480,1481,1482,1483,1484,1485,1486,1487,1488,1489,1490,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1533,1534,1535,1536,1537,1538,1539,1540,1541,1542,1543,1544,1545,1546,1547,1548,1549,1550,1551,1552,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1569,1570,1571,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1587,1588,1589,1590,1591,1592,1593,1594,1595,1596,1597,1598,1599,1600,1601,1602,1603,1604,1605,1606,1607,1608,1609,1610,1611,1612,1613,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1628,1629,1630,1631,1632,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1693,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1721,1722,1723,1724,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1771,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1846,1847,1848,1849,1850,1851,1852,1853,1854,1855,1894,1895,1947,1948,1949,1951,1952,1953,1954,1955,1956,1959,1960,1961,1962,1963,1964,1975,1978,1981,1982,2001,2002,2003,2004,2005,2006,2007,2015,2024,2025,2030,2031,2033,2034,2035,2036,2037,2038,2039,2040,2041,2042,2043,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2065,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2088,2089,2090,2093,2098,2142,2148,2149,2150,2151,2152,2153,2170,2171,2172,2173,2208,2210,2215,2216,2217,2229,2231,2232,2242,2243,2244,2245,2246,2247,2248,2249,2250,2251,2252,2253,2254,2255,2256,2257,2259,2260,2261,2262,2263,2264,2265,2266,2267,2268,2269,2270,2271,2272,2273,2274,2275,2276,2277,2278,2279,2280,2281,2282,2283,2284,2285,2286,2287,2288,2289,2292,2295,2298,2299,2300,2301,2302,2303,2304,2305,2306,2307,2308,2309,2310,2311,2312,2313,2314,2315,2316,2317,2318,2319,2320,2321,2322,2323,2324,2325,2326,2327,2328,2329,2330,2331,2332,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2345,2346,2347,2348,2369,2370,2371,2372,2373,2409,2410,2411,2413,2414,2450,2454,2458,2462,2463,2467,2514,2521,2529,2694,2704,2713,2722,2731,2792,2793,2794,2800,2801,2802,2803,2804,2805,2811,2812,2813,2814,2815,2820,2821,2825,2826,2832,2836,2837,2838,2839,2849,2850,2851,2855,2856,2862,2866,2867,2942,2943,2947,2948,2951,2952,2953,2954,3217,3224,3484,3490,3753,3760,4020,4026,4089,4171,4223,4305,4367,4449,4513,4565,4647,4655,4661,4672,4676,4680,4693,4708,5484,5491,5497,5514,5527,5547,5564,5573,5578,5585,5605,5618,5635,5641,5647,5654,5658,5664,5678,5681,5691,5692,5693,5741,5745,5749,5753,5754,5755,5758,5774,5781,5795,5840,5841,5876,5880,5884,5889,5896,5902,5903,5906,5910,5915,5928,5932,5937,5942,5947,5950,5953,5956,5960,5964,6104,6105,6106,6107,6335,6336,6337,6338,6339,6340,6341,6342,6343,6344,6345,6346,6347,6348,6349,6350,6351,6352,6356,6360,6364,6368,6372,6376,6380,6381,6382,6383,6384,6385,6386,6387,6391,6395,6396,6400,6401,6404,6408,6411,6414,6417,6421,6436,6439,6443,6447,6451,6455,6458,6459,6460,6461,6464,6468,6471,6474,6477,6480,6483,6486,6490,6560,6561,6564,6567,6568,6571,6572,6573,6577,6578,6583,6590,6597,6604,6611,6618,6625,6632,6639,6646,6655,6664,6673,6680,6689,6698,6701,6704,6705,6706,6707,6708,6709,6710,6711,6712,6713,6714,6715,6716,6720,6725,6730,6733,6734,6735,6736,6737,6745,6753,6754,6762,6766,6774,6782,6790,6798,6806,6807,6815,6823,6824,6827,6830,6908,6913,6915,6920,6924,6928,6929,6930,6931,6935,6939,6940,6944,6945,6946,6947,6948,6949,6950,6951,6952,6953,6954,6955,6956,6957,6958,6959,6963,6967,6968,6972,6973,6974,6979,6980,6981,6982,6983,6984,6985,6986,6987,6988,6989,6990,6991,6992,6993,6994,6995,6996,6997,6998,6999,7003,7004,7005,7011,7012,7016,7018,7019,7024,7025,7026,7027,7028,7029,7033,7034,7035,7041,7042,7046,7048,7052,7056,7060,7064,7168,7169,7170,7173,7176,7179,7182,7185,7190,7194,7197,7198,7203,7207,7212,7218,7224,7229,7233,7238,7242,7246,7287,7288,7289,7290,7291,7295,7296,7297,7298,7302,7306,7310,7314,7318,7322,7326,7330,7336,7337,7378,7392,7397,7423,7430,7433,7444,7449,7452,7455,7510,7516,7517,7520,7523,7526,7529,7532,7535,7538,7542,7545,7546,7547,7555,7563,7566,7571,7576,7581,7586,7590,7594,7595,7603,7604,7605,7606,7607,7615,7620,7625,7626,7627,7628,7653,7659,7664,7667,7671,7674,7678,7688,7691,7696,7699,7703,7707,7812,7826,7839,7843,7858,7869,7872,7883,7888,7892,7927,7928,7929,7940,7947,7954,7961,7968,7988,7991,8018,8023,8043,8046,8053,8066,8075,8078,8098,8108,8112,8116,8129,8133,8137,8141,8147,8151,8168,8176,8180,8184,8188,8191,8195,8199,8203,8213,8220,8227,8231,8257,8267,8292,8301,8321,8331,8335,8345,8370,8380,8383,8387,8388,8389,8390,8394,8400,8406,8407,8420,8421,8422,8425,8428,8431,8434,8437,8440,8443,8446,8449,8452,8455,8458,8461,8464,8467,8470,8473,8476,8479,8482,8485,8486,8491,8492,8505,8515,8519,8524,8529,8533,8536,8540,8544,8547,8551,8554,8558,8563,8568,8571,8578,8582,8586,8595,8600,8605,8606,8610,8613,8617,8630,8635,8643,8647,8651,8668,8672,8677,8695,8702,8705,8735,8738,8741,8744,8747,8750,8753,8772,8778,8786,8793,8805,8813,8818,8822,8826,8837,8841,8849,8852,8857,8858,8859,8860,8864,8868,8872,8876,8911,8914,8918,8922,8956,8959,8963,8967,8976,8982,8985,8995,8999,9000,9007,9011,9018,9019,9020,9023,9028,9033,9034,9038,9053,9072,9076,9077,9089,9099,9100,9112,9117,9141,9144,9150,9153,9162,9170,9174,9177,9180,9183,9187,9190,9207,9211,9214,9229,9232,9240,9245,9252,9257,9258,9263,9264,9270,9276,9282,9314,9325,9342,9349,9353,9356,9369,9378,9382,9387,9391,9395,9399,9403,9407,9411,9415,9420,9423,9435,9440,9449,9452,9459,9460,9464,9473,9479,9483,9484,9488,9509,9515,9519,9523,9524,9542,9543,9544,9545,9546,9551,9554,9555,9561,9562,9574,9586,9593,9594,9599,9604,9605,9609,9623,9628,9634,9640,9646,9651,9657,9663,9664,9670,9685,9690,9699,9708,9711,9725,9730,9741,9745,9754,9763,9764,9771,9779,14514,14515,14516,14517,14518,14519,14520,14521,14522,14523,14524,14525,14526,14527,14528,14529,14530,14531,14532,14533,14534,14535,14536,14537,14538,14539,14540,14541,14542,14543,14544,14545,14546,14547,14548,14549,14550,14551,14552,14553,14554,14555,14556,14557,14558,14559,14560,14561,14562,14563,14564,14565,14566,14567,14568,14569,14570,14571,14572,14573,14574,14575,14576,14577,14578,14579,14580,14581,14582,14583,14584,14585,14586,14587,14588,14589,14590,14591,14592,14593,14594,14595,14596,14597,14598,14599,14600,14601,14602,14603,14604,14605,14606,14607,14608,14609,14610,14611,14612,14613,14614,14615,14616,14617,14618,14619,14620,14621,14622,14623,14624,14625,14626,14627,14628,14629,14630,14631,14632,14633,14634,14635,14636,14637,14638,14639,14640,14641,14642,14643,14644,14645,14646,14647,14648,14649,14650,14651,14652,14653,14654,14655,14656,14657,14658,14659,14660,14661,14662,14663,14664,14665,14666,14667,14668,14669,14670,14671,14672,14673,14674,14675,14676,14677,14678,14679,14680,14681,14682,14683,14684,14685,14686,14687,14688,14689,14690,14691,14692,14693,14694,14695,14696,14697,14698,14699,14700,14701,14702,14703,14704,14705,14706,14707,14708,14709,14710,14711,14712,14713,14714,14715,14716,14717,14718,14719,14720,14721,14722,14723,14724,14725,14726,14727,14728,14729,14730,14731,14732,14733,14734,14735,14736,14737,14738,14739,14740,14741,14742,14743,14744,14745,14746,14747,14748,14749,14750,14751,14752,14753,14754,14755,14756,14757,14758,14759,14760,14761,14762,14763,14764,14765,14766,14767,14768,14769,14770,14771,14772,14773,14774,14775,14776,14777,14778,14779,14780,14781,14782,14783,14784,14785,14786,14787,14788,14789,14790,14791,14792,14793,14794,14795,14796,14797,14798,14799,14800,14801,14802,14803,14804,14805,14806,14807,14808,14809,14810,14811,14812,14813,14814,14815,14816,14817,14818,14819,14820,14821,14822,14823,14824,14825,14826,14827,14828,14829,14830,14831,14832,14833,14834,14835,14836,14837,14838,14839,14840,14841,14842,14843,14844,14845,14846,14847,14848,14849,14850,14851,14852,14853,14854,14855,14856,14857,14858,14859,14860,14861,14862,14863,14864,14865,14866,14867,14868,14869,14870,14871,14872,14873,14874,14875,14876,14877,14878,14879,14880,14881,14882,14883,14884,14885,14886,14887,14888", "endColumns": "55,48,54,55,59,60,64,54,49,49,52,57,47,68,47,70,71,71,72,66,48,53,36,50,59,46,55,48,57,53,60,55,50,59,55,62,48,55,55,49,58,54,61,46,53,55,51,54,53,53,53,48,57,53,56,55,46,52,55,59,62,58,61,49,53,53,47,56,52,55,53,55,62,61,55,59,52,60,78,80,71,78,79,75,77,68,75,76,70,72,75,77,68,75,76,63,70,71,50,52,54,66,52,51,49,57,64,47,50,66,65,57,68,57,68,69,72,73,67,66,69,65,72,59,75,59,59,74,67,65,67,59,58,56,65,61,56,67,72,69,61,60,67,61,69,68,55,58,61,61,66,56,60,60,60,60,55,55,55,55,57,57,57,57,56,56,56,56,58,58,57,82,82,72,53,68,55,80,80,70,9,52,57,57,57,45,59,53,69,69,64,65,64,67,68,67,9,52,58,57,51,49,51,45,49,55,46,57,57,61,62,61,58,59,64,65,64,61,61,61,61,61,61,65,66,65,62,63,62,67,60,61,61,62,63,62,63,77,58,65,79,60,52,57,50,44,63,58,61,73,70,65,73,68,70,72,70,67,72,75,69,77,67,65,60,68,63,65,67,65,62,67,70,64,72,62,80,63,65,69,69,69,69,66,56,57,58,59,58,58,58,58,58,58,58,58,58,58,59,60,61,60,60,60,60,60,60,59,60,60,59,60,60,60,60,60,60,60,60,60,60,60,67,68,69,68,68,68,68,68,68,68,68,68,68,59,60,61,60,60,60,60,60,60,60,60,60,60,61,62,63,62,62,62,62,62,62,62,62,62,62,60,61,62,61,61,61,61,61,61,61,61,61,61,56,86,79,89,94,91,91,89,82,92,86,96,90,100,86,102,88,98,91,99,83,93,87,97,83,90,93,98,101,97,99,86,99,85,95,87,80,90,95,92,92,90,84,93,88,97,92,101,87,103,90,99,92,100,84,94,88,98,84,91,94,99,102,98,102,88,100,86,96,87,95,91,99,89,97,84,88,88,92,86,90,65,75,68,78,72,79,79,76,67,77,75,70,80,72,82,74,84,72,80,80,73,83,69,77,69,79,77,71,81,69,76,79,84,87,83,86,73,77,77,70,80,90,82,95,97,106,64,65,52,75,65,86,75,75,64,54,53,78,77,72,64,62,65,70,70,69,61,68,65,59,66,66,55,50,52,51,53,70,62,58,61,58,72,66,69,59,62,74,71,95,70,55,70,56,56,65,63,70,56,52,62,51,57,66,68,65,58,82,58,56,66,69,73,61,68,69,98,96,98,85,85,80,74,88,90,83,58,45,65,56,66,56,81,64,65,122,83,120,64,61,97,73,82,88,63,78,73,61,95,64,58,55,55,59,106,46,59,60,63,60,59,57,42,48,51,50,51,48,48,64,65,59,60,55,58,48,47,57,56,101,56,74,47,50,61,64,51,73,62,62,67,49,61,59,56,59,48,67,105,101,68,70,55,48,99,70,109,88,90,81,97,55,100,109,98,62,105,76,111,126,111,126,69,113,130,96,67,117,102,117,60,73,66,104,121,73,66,109,98,72,96,121,117,117,60,121,116,67,105,101,79,70,95,66,73,73,85,87,89,77,76,99,70,120,120,63,124,73,123,123,66,108,127,111,78,77,100,70,121,121,64,125,111,105,67,98,103,62,65,83,112,112,117,77,71,135,135,84,139,137,137,141,81,108,110,127,127,131,129,129,133,61,95,66,116,120,96,81,86,86,130,130,134,76,76,110,113,73,108,111,101,95,103,66,93,71,109,105,72,90,101,102,94,106,104,121,121,125,58,57,123,123,127,117,117,121,85,96,133,133,79,137,131,131,135,74,75,102,73,112,80,56,60,58,59,57,60,57,49,48,66,58,58,48,70,83,70,79,68,62,67,65,67,64,65,76,77,105,105,95,128,88,126,65,68,85,65,82,97,95,95,97,108,94,88,61,59,64,56,80,53,56,96,109,60,114,120,94,91,92,101,55,58,48,91,48,53,53,53,53,53,54,109,109,107,109,109,109,109,107,105,103,103,103,94,94,92,92,103,105,103,103,92,92,92,92,107,105,105,105,96,94,94,94,105,105,105,105,97,95,95,97,64,103,57,63,60,61,59,61,67,57,62,62,66,74,72,65,51,52,51,56,83,94,84,80,79,76,78,76,73,73,70,79,71,74,64,60,59,74,73,76,72,69,71,69,72,63,69,45,68,51,84,82,56,65,66,65,80,74,55,52,60,57,49,48,48,48,61,51,44,80,50,53,52,53,50,48,65,50,60,60,61,49,40,76,58,58,58,60,55,55,66,60,64,54,64,68,67,77,68,59,70,73,64,71,69,66,83,68,66,69,62,66,67,82,78,89,76,67,66,77,56,56,67,65,55,59,58,53,49,49,47,61,50,72,79,79,63,66,70,57,60,65,58,66,59,59,62,67,60,66,77,69,48,56,68,60,87,87,87,87,86,86,86,86,57,73,69,55,70,64,61,74,72,89,65,65,60,63,61,57,70,82,58,70,65,64,60,58,70,65,64,82,75,74,80,59,68,69,68,54,55,55,60,57,55,53,54,61,56,93,68,100,50,69,62,55,57,58,53,85,83,69,68,69,114,120,66,66,74,66,58,53,53,53,52,51,73,136,139,48,49,49,47,55,57,61,54,57,70,63,58,61,65,65,42,43,44,42,50,46,44,50,50,50,50,47,65,61,62,71,56,53,54,57,54,58,55,68,68,68,69,62,62,62,62,64,64,64,64,62,63,63,63,50,77,77,70,71,72,71,65,65,67,67,65,66,73,62,56,59,64,66,64,56,60,57,103,109,108,103,77,64,66,65,69,46,51,49,56,12,149,130,183,177,237,188,168,97,114,84,78,11,64,88,156,156,152,153,58,186,95,89,95,89,165,122,122,169,105,114,114,101,105,116,114,81,172,167,147,158,154,172,116,116,167,111,113,171,175,157,132,111,145,151,131,142,121,177,135,95,135,94,166,92,91,186,11,11,11,181,316,181,181,189,231,189,176,161,156,109,182,136,219,183,183,159,157,183,226,202,170,219,221,154,199,183,102,189,140,164,170,199,203,201,164,204,198,198,196,90,148,149,83,148,144,151,140,165,160,77,300,165,154,101,11,163,185,222,124,10,10,10,10,61,10,10,10,10,10,10,10,10,10,84,120,98,10,96,116,86,122,100,10,98,118,92,106,10,106,10,120,10,10,99,104,118,10,146,118,10,132,10,10,111,10,124,10,120,10,120,132,146,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,136,90,10,10,10,10,108,118,10,10,10,10,10,94,10,10,10,10,10,10,121,10,10,10,10,10,10,10,10,10,10,10,10,10,72,86,84,98,111,105,122,131,122,129,123,132,130,124,116,119,131,127,113,117,112,120,10,10,10,10,10,10,10,119,119,107,109,111,107,109,10,10,151,10,100,10,10,10,10,10,10,10,10,10,10,10,10,10,144,121,134,10,10,10,10,10,10,10,10,10,10,91,10,10,94,10,93,88,10,88,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,105,107,105,111,113,111,113,115,113,107,109,107,10,10,10,10,107,109,107,113,10,10,115,10,10,10,10,10,10,10,121,10,10,121,10,10,10,10,10,10,10,10,75,111,89,10,10,107,10,107,75,111,89,101,107,107,99,107,84,103,86,77,113,91,10,10,109,10,109,83,10,97,107,93,129,107,121,135,107,119,133,121,127,141,125,139,125,117,131,97,109,10,111,117,10,115,10,10,95,10,109,123,137,109,121,10,123,129,10,127,10,10,10,10,10,10,67,83,103,10,10,10,10,10,10,10,10,115,10,10,10,10,10,10,10,10,10,10,10,113,119,115,93,10,98,117,100,10,10,10,10,10,10,10,10,10,75,10,10,10,10,10,10,10,10,10,10,10,10,111,10,10,10,10,10,10,10,10,10,83,103,10,10,10,10,10,10,10,10,10,137,10,113,137,111,123,10,10,10,144,92,91,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,99,89,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,145,137,135,10,10,10,115,10,121,111,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,111,10,123,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,139,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,87,107,107,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,123,10,10,10,102,129,10,10,10,139,10,10,10,10,129,10,10,145,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,78,10,104,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,158,10,10,10,10,157,10,10,10,10,10,141,10,105,129,137,123,10,10,99,10,113,10,10,10,123,10,10,133,10,10,10,10,10,10,10,10,10,103,10,10,10,10,10,10,10,10,10,10,10,10,119,10,10,94,92,62,81,92,83,86,97,90,90,87,83,95,103,86,105,102,100,103,105,98,105,101,106,108,110,130,119,115,117,98,106,115,118,127,75,94,76,88,90,92,73,84,82,97,98,103,95,101,102,99,89,84,100,97,89,94,86,105,101,93,90,80,75,91,88,119,110,82,85,94,87,95,87,100,100,93,105,97,96,94,97,102,99,102,104,117,115,94,92,84,95,93,91,101,97,73,104,99,100,104,99,100,98,101,93,106,101,102,83,95,101,102,95,101,102,96,102,97,103,104,96,107,113,114,107,113,114,101,104,107,109,115,116,86,94,96,98,104,105,98,104,105,99,105,100,106,118,98,101,101,99,102,94,103,84,103,103,97,103,105,97,121,97,112,93,88,88,82,90,82,97,89,95,88,93,87,95,84,107,100,100,97,105,90,98,96,97,95,92,109,97,94,109,91,99,98,86,103,104,98,106,106,98,108,91,110,110,110,103,114,115,126,119,96,98,91,98,91,98,85,93,102,95,102,95,102,96,97,102,79,107,89,100,82,90,84,91,102,94,95,92,80,108,78,106,90,98,92,102,103,100,100,103,93,103,103,112,105,105,107,116,88,107,99,102,86,106,78,89,83,91,72,87,81,84,84,96,92,94,98,96,90,90,91,94,97,98,101,96,96,92,86,83,96,96,92,86,90,98,98,94,88,80,98,94,96,95,96,83,98,94,96,95,96,88,100,96,98,97,98,89,90,88,88,81,92,90,110,100,99,111,112,88,107,80,99,88,91,110,109,94,115,125,125,118,127,124,124,117,126,108,108,112,122,122,115,124,96,107,112,115,115,108,87,100,88,100,86,87,96,91,99,75,76", "endOffsets": "541,776,1235,1394,1454,1515,1580,1635,1785,1835,1888,1946,1994,2114,2463,2534,2606,2678,2751,2818,2867,2921,2958,3009,3069,3116,3172,3221,3279,3333,3394,3450,3501,3561,3617,3680,3729,3785,3841,3891,3950,4005,4067,4114,4168,4224,4276,4331,4385,4439,4493,4542,4600,4654,4711,4767,4814,4867,4923,4983,5046,5105,5167,5217,5271,5325,5373,5430,5483,5539,6161,6217,7155,7265,7321,7381,7434,7495,7574,7655,7727,7806,7886,7962,8040,8109,8185,8262,8333,8406,8482,8560,8629,8705,8782,8846,8917,8989,11481,11579,11634,11750,11803,11855,11905,11963,12028,12076,12127,19084,19213,19271,19340,19398,19467,19537,19610,19684,19752,19819,19889,19955,20028,20088,20164,20224,20284,20359,20427,20493,20561,20621,20680,20737,20803,20865,20922,20990,21063,21133,21195,21256,21324,21386,21456,21525,21581,21640,21702,21764,21831,21888,21949,22010,22071,22132,22188,22244,22300,22356,22414,22472,22530,22588,22645,22702,22759,22816,22875,22934,22992,23075,23158,23231,23285,23354,23410,23491,23572,23643,23772,24031,24089,24147,25234,25841,25901,25955,26025,26095,26160,26226,26291,26359,26428,26496,26626,26679,26738,26796,26848,26944,27391,27437,27487,27543,27590,27695,27753,27815,27878,27940,27999,28059,28124,28190,28255,28317,28379,28441,28503,28565,28627,28693,28760,28826,28889,28953,29016,29084,29145,29207,29269,29332,29396,29459,29523,29601,29660,29726,29806,29867,29920,30129,30180,30607,31077,31136,32879,37162,37233,37299,37373,37442,37513,37586,37657,37725,37798,37874,37944,38022,38090,38156,38217,38286,38350,38416,38484,38550,38613,38681,38752,38817,38890,38953,39034,39098,39164,39234,39304,39374,39444,39511,41329,41387,41446,41506,41565,41624,41683,41742,41801,41860,41919,41978,42037,42096,42156,42217,42279,42340,42401,42462,42523,42584,42645,42705,42766,42827,42887,42948,43009,43070,43131,43192,43253,43314,43375,43436,43497,43558,43626,43695,43765,43834,43903,43972,44041,44110,44179,44248,44317,44386,44455,44515,44576,44638,44699,44760,44821,44882,44943,45004,45065,45126,45187,45248,45310,45373,45437,45500,45563,45626,45689,45752,45815,45878,45941,46004,46067,46128,46190,46253,46315,46377,46439,46501,46563,46625,46687,46749,46811,46873,46930,47017,47097,47187,47282,47374,47466,47556,47639,47732,47819,47916,48007,48108,48195,48298,48387,48486,48578,48678,48762,48856,48944,49042,49126,49217,49311,49410,49512,49610,49710,49797,49897,49983,50079,50167,50248,50339,50435,50528,50621,50712,50797,50891,50980,51078,51171,51273,51361,51465,51556,51656,51749,51850,51935,52030,52119,52218,52303,52395,52490,52590,52693,52792,52895,52984,53085,53172,53269,53357,53453,53545,53645,53735,53833,53918,54007,54096,54189,54276,54367,55105,55181,55250,55329,55402,55482,55562,55639,55707,55785,55861,55932,56013,56086,56169,56244,56329,56402,56483,56564,56638,56722,56792,56870,56940,57020,57098,57170,57252,57322,57399,57479,57564,57652,57736,57823,57897,57975,58053,58124,58205,58296,58379,58475,58573,58680,58745,58811,58864,58940,59006,59093,59169,59245,68829,69197,69791,69870,69948,70021,70086,70149,70215,70286,70357,70427,70489,70558,70624,70684,70751,70818,70874,70925,70978,71030,71084,71155,71218,71277,71339,71398,71471,71538,71608,71668,71731,71806,71878,71974,72045,72101,72172,72229,72286,72352,72416,72487,72544,72597,72660,72712,72770,72837,74076,74142,74201,74284,74343,74400,74467,74537,74611,74673,74742,74812,74911,75008,75107,75193,75279,75360,75435,75524,75615,75699,75758,75804,75870,75927,75994,76051,76133,76198,76264,76387,76471,76592,76657,76719,76817,76891,76974,77063,77127,77206,77280,77342,77438,77503,77562,77618,77674,77734,77841,77888,77948,78009,78073,78134,78194,78252,78295,78344,78396,78447,78499,78548,78597,78662,78728,78788,78849,78905,78964,79013,79061,79119,79176,79278,79335,79410,79458,79509,79571,79636,79688,79762,79825,79888,79956,80006,80068,80128,80185,80245,80294,80362,80468,80570,80639,80710,80766,80815,80915,80986,81096,81185,81276,81358,81456,81512,81613,81723,81822,81885,81991,82068,82180,82307,82419,82546,82616,82730,82861,82958,83026,83144,83247,83365,83426,83500,83567,83672,83794,83868,83935,84045,84144,84217,84314,84436,84554,84672,84733,84855,84972,85040,85146,85248,85328,85399,85495,85562,85636,85710,85796,85884,85974,86052,86129,86229,86300,86421,86542,86606,86731,86805,86929,87053,87120,87229,87357,87469,87548,87626,87727,87798,87920,88042,88107,88233,88345,88451,88519,88618,88722,88785,88851,88935,89048,89161,89279,89357,89429,89565,89701,89786,89926,90064,90202,90344,90426,90535,90646,90774,90902,91034,91164,91294,91428,91490,91586,91653,91770,91891,91988,92070,92157,92244,92375,92506,92641,92718,92795,92906,93020,93094,93203,93315,93417,93513,93617,93684,93778,93850,93960,94066,94139,94230,94332,94435,94530,94637,94742,94864,94986,95112,95171,95229,95353,95477,95605,95723,95841,95963,96049,96146,96280,96414,96494,96632,96764,96896,97032,97107,97183,97286,97360,97473,97554,97611,97672,97731,97791,97849,97910,97968,98018,98067,98134,98193,98252,98301,98372,98456,98527,98607,98676,98739,98807,98873,98941,99006,99072,99149,99227,99333,99439,99535,99664,99753,99880,99946,100015,100101,100167,100250,100348,100444,100540,100638,100747,100842,100931,100993,101053,101118,101175,101256,101310,101367,101464,101574,101635,101750,101871,101966,102058,102151,102253,102309,102368,102417,102509,102558,102612,102666,102720,102774,102828,102883,102993,103103,103211,103321,103431,103541,103651,103759,103865,103969,104073,104177,104272,104367,104460,104553,104657,104763,104867,104971,105064,105157,105250,105343,105451,105557,105663,105769,105866,105961,106056,106151,106257,106363,106469,106575,106673,106769,106865,106963,107028,107132,107190,107254,107315,107377,107437,107499,107567,107625,107688,107751,107818,107893,107966,108032,108084,108137,108189,108246,108330,108425,108510,108591,108671,108748,108827,108904,108978,109052,109123,109203,109275,109350,109415,109476,109536,109611,109685,109762,109835,109905,109977,110047,110120,110184,110254,110300,110369,110421,110506,110589,110646,110712,110779,110845,110926,111001,111057,111110,111171,111229,111279,111328,111377,111426,111488,111540,111585,111666,111717,111771,111824,111878,111929,111978,112044,112095,112156,112217,112279,112329,112370,112447,112506,112565,112624,112685,112741,112797,112864,112925,112990,113045,113110,113179,113247,113325,113394,113454,113525,113599,113664,113736,113806,113873,113957,114026,114093,114163,114226,114293,114361,114444,114523,114613,114690,114758,114825,114903,114960,115017,115085,115151,115207,115267,115326,115380,115430,115480,115528,115590,115641,115714,115794,115874,115938,116005,116076,116134,116195,116261,116320,116387,116447,116507,116570,116638,116699,116766,116844,116914,116963,117020,117089,117150,117238,117326,117414,117502,117589,117676,117763,117850,117908,117982,118052,118108,118179,118244,118306,118381,118454,118544,118610,118676,118737,118801,118863,118921,118992,119075,119134,119205,119271,119336,119397,119456,119527,119593,119658,119741,119817,119892,119973,120033,120102,120172,120241,120296,120352,120408,120469,120527,120583,120637,120692,120754,120811,120905,120974,121075,121126,121196,121259,121315,121373,121432,121486,121572,121656,121726,121795,121865,121980,122101,122168,122235,122310,122377,122436,122490,122544,122598,122651,122703,122777,125355,125495,128446,128496,128546,128635,128691,128749,128811,128866,128924,129105,129169,129228,129290,129356,129422,129970,130116,130269,130312,131339,131386,131431,131482,131533,131584,131635,132016,132524,132586,132887,132959,133084,133138,133193,133251,133306,133365,133421,133490,133559,133628,133698,133761,133824,133887,133950,134015,134080,134145,134210,134273,134337,134401,134465,134516,134594,134672,134743,134815,134888,134960,135026,135092,135160,135228,135294,135361,135435,135498,135555,135615,135680,135747,135812,135869,135930,135988,136092,136202,136311,136415,136493,136558,136625,136691,136761,136808,136860,136910,137093,137483,140479,140861,141045,141223,141461,141650,141819,142963,143078,143163,143242,146487,146634,147045,147202,147359,148418,148633,148692,149331,149427,149517,149613,149703,149869,149992,150115,150285,150391,150506,150621,150723,150829,150946,151061,151185,151358,151526,151674,151833,151988,152161,152278,152395,152563,152675,152789,152961,153137,153295,153428,153540,153686,153838,153970,154113,154235,154413,154549,154645,154781,154876,155043,155136,155228,155415,155571,155749,155913,156095,156412,156594,156776,156966,157198,157388,157565,157727,157884,157994,158177,158314,158534,158718,158902,159062,159220,159404,159631,159834,160005,160225,160447,160602,160802,160986,161089,161279,161420,161585,161756,161956,162160,162362,162527,162732,162931,163130,163327,163418,163567,163717,163801,163950,164095,164247,164388,164554,164715,165914,166215,166381,166536,166638,168683,168847,169033,169296,169421,171668,171940,172218,172463,172525,172810,175804,176260,176769,187545,188059,188496,188930,189373,193355,193476,193575,193980,194077,194194,194281,194404,194505,194911,195010,195129,195222,195329,195672,195779,196024,196145,196554,196802,196902,197007,197126,197635,197782,197901,198152,198285,198700,198954,199066,204682,204807,205124,205245,205473,205594,205727,205874,226505,226997,247377,247801,268477,268971,289396,289822,294663,300080,304171,309602,314344,319721,323705,327697,333088,333635,334068,334824,335054,335297,336430,337359,385504,386058,386511,387941,388685,389878,390932,391410,391703,392086,393601,394366,395509,395950,396391,396957,397231,397642,398658,398836,399589,399726,399817,402011,402277,402599,402809,402918,403037,403221,404339,404809,405560,408143,408238,410600,410828,411084,411343,411919,412273,412395,412534,412826,413086,414014,414300,414703,415105,415448,415660,415861,416074,416363,416648,427323,427410,427495,427594,440132,440238,440361,440493,440616,440746,440870,441003,441134,441259,441376,441496,441628,441756,441870,441988,442101,442222,442410,442597,442778,442961,443145,443310,443492,443612,443732,443840,443950,444062,444170,444280,444445,444611,444763,444928,445029,445149,445320,445481,445644,445805,445972,446737,446854,447034,447216,447397,447580,447735,447880,448002,448137,448300,448493,448619,448771,448913,449083,449239,449411,449702,456369,456461,456634,456796,456891,457060,457154,457243,457486,457575,457868,458284,458704,459125,459551,459968,460384,460801,461219,461633,462103,462576,463048,463459,463930,464402,464592,464798,464904,465012,465118,465230,465344,465456,465570,465686,465800,465908,466018,466126,466388,466767,467171,467318,467426,467536,467644,467758,468167,468581,468697,469115,469356,469786,470221,470631,471053,471463,471585,471994,472410,472532,472750,472934,477977,478321,478401,478757,478907,479051,479127,479239,479329,479591,479856,479964,480116,480224,480300,480412,480502,480604,480712,480820,480920,481028,481113,481217,481304,481382,481496,481588,481852,482119,482229,482382,482492,482576,482965,483063,483171,483265,483395,483503,483625,483761,483869,483989,484123,484245,484373,484515,484641,484781,484907,485025,485157,485255,485365,485665,485777,485895,486359,486475,486778,486904,487000,487401,487511,487635,487773,487883,488005,488317,488441,488571,489047,489175,489490,489628,489790,490006,490162,490366,496294,496378,496482,496685,496874,497075,497268,497473,497786,497998,498162,498278,498524,498740,499053,499479,499941,500178,500330,500590,500734,500876,504108,504222,504342,504458,504552,504873,504972,505090,505191,505470,505755,506034,506316,506569,506828,507081,507337,507761,507837,511087,512442,512886,514740,515315,515523,516533,516913,517079,517220,522240,522666,522778,522913,523066,523263,523434,523617,523792,523979,524251,524409,524493,524597,525084,525640,525798,526017,526248,526471,526706,526928,527194,527332,527931,528045,528183,528295,528419,528990,529485,530031,530176,530269,530361,532288,532858,533156,533345,533551,533744,533954,534838,534983,535375,535533,535750,536011,544238,545113,545733,545930,546878,547643,547766,548539,548760,548960,550937,551037,551127,551712,552382,553064,553744,554436,555649,555814,557427,557748,558811,558981,559551,560446,561079,561245,562731,563347,563583,563804,564762,565027,565292,565539,565953,566189,567474,567923,568110,568359,568601,568777,569018,569251,569476,570071,570546,571070,571331,572682,573157,574383,574853,575901,576353,576597,577054,578299,578782,578932,579276,579422,579560,579696,579984,580488,580997,581113,582015,582137,582249,582426,582692,582962,583228,583496,583752,584012,584268,584526,584778,585034,585286,585540,585772,586008,586260,586516,586768,587022,587254,587488,587600,588025,588149,589241,590056,590252,590576,590965,591317,591558,591772,592071,592263,592578,592785,593131,593431,593832,594051,594464,594701,595071,595795,596150,596419,596559,596813,596957,597234,598226,598635,599267,599613,599981,601055,601418,601818,603326,603911,604136,606665,606859,607077,607303,607515,607714,607921,609125,609420,609977,610367,610999,611467,611712,611969,612215,612975,613239,613662,613853,614232,614320,614428,614536,614849,615174,615493,615824,618527,618715,618976,619225,621809,622001,622266,622519,623051,623459,623658,624242,624477,624601,625013,625227,625629,625732,625862,626037,626289,626485,626625,626819,627830,628899,629187,629317,630094,630751,630897,631603,631841,633381,633531,633948,634113,634799,635269,635465,635556,635640,635784,636018,636185,637113,637399,637559,638174,638333,638661,638888,639400,639762,639841,640180,640285,640650,641021,641382,643256,643885,644961,645385,645638,645790,646838,647575,647778,648024,648271,648489,648731,649052,649316,649621,649844,650155,650344,651059,651328,651822,652048,652488,652647,652931,653676,654041,654346,654504,654742,656061,656459,656687,656907,657049,658339,658445,658575,658713,658837,659125,659294,659394,659679,659793,660676,661431,661870,661994,662240,662433,662567,662758,663537,663755,664046,664325,664642,664864,665159,665442,665546,665887,666703,667019,667580,668086,668291,669077,669482,670143,670332,670883,671449,671569,671971,672505,824017,824110,824173,824255,824348,824432,824519,824617,824708,824799,824887,824971,825067,825171,825258,825364,825467,825568,825672,825778,825877,825983,826085,826192,826301,826412,826543,826663,826779,826897,826996,827103,827219,827338,827466,827542,827637,827714,827803,827894,827987,828061,828146,828229,828327,828426,828530,828626,828728,828831,828931,829021,829106,829207,829305,829395,829490,829577,829683,829785,829879,829970,830051,830127,830219,830308,830428,830539,830622,830708,830803,830891,830987,831075,831176,831277,831371,831477,831575,831672,831767,831865,831968,832068,832171,832276,832394,832510,832605,832698,832783,832879,832973,833065,833167,833265,833339,833444,833544,833645,833750,833850,833951,834050,834152,834246,834353,834455,834558,834642,834738,834840,834943,835039,835141,835244,835341,835444,835542,835646,835751,835848,835956,836070,836185,836293,836407,836522,836624,836729,836837,836947,837063,837180,837267,837362,837459,837558,837663,837769,837868,837973,838079,838179,838285,838386,838493,838612,838711,838813,838915,839015,839118,839213,839317,839402,839506,839610,839708,839812,839918,840016,840138,840236,840349,840443,840532,840621,840704,840795,840878,840976,841066,841162,841251,841345,841433,841529,841614,841722,841823,841924,842022,842128,842219,842318,842415,842513,842609,842702,842812,842910,843005,843115,843207,843307,843406,843493,843597,843702,843801,843908,844015,844114,844223,844315,844426,844537,844648,844752,844867,844983,845110,845230,845327,845426,845518,845617,845709,845808,845894,845988,846091,846187,846290,846386,846489,846586,846684,846787,846867,846975,847065,847166,847249,847340,847425,847517,847620,847715,847811,847904,847985,848094,848173,848280,848371,848470,848563,848666,848770,848871,848972,849076,849170,849274,849378,849491,849597,849703,849811,849928,850017,850125,850225,850328,850415,850522,850601,850691,850775,850867,850940,851028,851110,851195,851280,851377,851470,851565,851664,851761,851852,851943,852035,852130,852228,852327,852429,852526,852623,852716,852803,852887,852984,853081,853174,853261,853352,853451,853550,853645,853734,853815,853914,854009,854106,854202,854299,854383,854482,854577,854674,854770,854867,854956,855057,855154,855253,855351,855450,855540,855631,855720,855809,855891,855984,856075,856186,856287,856387,856499,856612,856701,856809,856890,856990,857079,857171,857282,857392,857487,857603,857729,857855,857974,858102,858227,858352,858470,858597,858706,858815,858928,859051,859174,859290,859415,859512,859620,859733,859849,859965,860074,860162,860263,860352,860453,860540,860628,860725,860817,860917,860993,861070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\91f5ac35e2aac1e5ec49fa8326c1819a\\transformed\\databinding-runtime-8.1.2\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1936", "startColumns": "4", "startOffsets": "127888", "endColumns": "40", "endOffsets": "127924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8c3942081edffb4b875bc5f4359e13d8\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2017", "startColumns": "4", "startOffsets": "132066", "endColumns": "42", "endOffsets": "132104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5ce941577190a4b7e40da0e194a20c68\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "1938,1939,1967,1976,1977,2009,2010,2011,2012,2013", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "127986,128026,129527,129975,130030,131675,131729,131781,131830,131891", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "128021,128068,129565,130025,130072,131724,131776,131825,131886,131936"}}, {"source": "C:\\Muslim Core\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2224,2225,2226,2227,2228,2397", "startColumns": "4,4,4,4,4,4", "startOffsets": "147754,147836,147940,148049,148169,168055", "endColumns": "81,103,108,119,100,69", "endOffsets": "147831,147935,148044,148164,148265,168120"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fc7a596ca09ec1789d8b31b6cf9b156f\\transformed\\lottie-6.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "389,1946", "startColumns": "4,4", "startOffsets": "19089,128355", "endColumns": "62,46", "endOffsets": "19147,128397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\bd229cba7731028eaebcf7d452c4e6dc\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "692,693,694,695,696,697,698,699,2174,2175,2176,2177,2178,2179,2180,2181,2183,2184,2185,2186,2187,2188,2189,2190,2191", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "35952,36042,36122,36212,36302,36382,36463,36543,143247,143352,143533,143658,143765,143945,144068,144184,144454,144642,144747,144928,145053,145228,145376,145439,145501", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "36037,36117,36207,36297,36377,36458,36538,36618,143347,143528,143653,143760,143940,144063,144179,144282,144637,144742,144923,145048,145223,145371,145434,145496,145575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2829f477d9c6056a40040b143f304a8b\\transformed\\drawerlayout-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "138,141,1177", "startColumns": "4,4,4", "startOffsets": "6996,7160,69690", "endColumns": "55,47,51", "endOffsets": "7047,7203,69737"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\29aa2070c5f46de89120ba6e20f2606d\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "471,657,658,686,687,1010,1012,1170,1171,1172,1173,1174,1175,1176,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1944,1945,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2008,2094,2156,2157,2158,2159,2160,2161,2162,2416,6547,6548,6552,6553,6557,7803,7804", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "24152,33586,33658,35572,35637,59250,59377,69202,69272,69340,69412,69482,69543,69617,122782,122843,122904,122966,123030,123092,123153,123221,123321,123381,123447,123520,123589,123646,123698,125500,125572,125648,125713,125772,125831,125891,125951,126011,126071,126131,126191,126251,126311,126371,126431,126490,126550,126610,126670,126730,126790,126850,126910,126970,127030,127090,127149,127209,127269,127328,127387,127446,127505,127564,128285,128320,130428,130483,130546,130601,130659,130717,130778,130841,130898,130949,130999,131060,131117,131183,131217,131640,137098,141954,142021,142093,142162,142231,142305,142377,169464,455421,455538,455739,455849,456050,543672,543744", "endLines": "471,657,658,686,687,1010,1012,1170,1171,1172,1173,1174,1175,1176,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1944,1945,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2008,2094,2156,2157,2158,2159,2160,2161,2162,2416,6547,6551,6552,6556,6557,7803,7804", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "24207,33653,33741,35632,35698,59314,59435,69267,69335,69407,69477,69538,69612,69685,122838,122899,122961,123025,123087,123148,123216,123316,123376,123442,123515,123584,123641,123693,123755,125567,125643,125708,125767,125826,125886,125946,126006,126066,126126,126186,126246,126306,126366,126426,126485,126545,126605,126665,126725,126785,126845,126905,126965,127025,127085,127144,127204,127264,127323,127382,127441,127500,127559,127618,128315,128350,130478,130541,130596,130654,130712,130773,130836,130893,130944,130994,131055,131112,131178,131212,131247,131670,137163,142016,142088,142157,142226,142300,142372,142460,169530,455533,455734,455844,456045,456174,543739,543806"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\81561af282a29cce9274e37ca4fb03dd\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "680,681,682,683,1164,1165,2194,2218,2219,2220", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "35208,35266,35332,35395,68834,68905,145680,147364,147431,147510", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "35261,35327,35390,35452,68900,68972,145743,147426,147505,147574"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f603684ac85a3923c1b73c512f406c56\\transformed\\glide-4.16.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1940", "startColumns": "4", "startOffsets": "128073", "endColumns": "57", "endOffsets": "128126"}}, {"source": "C:\\Muslim Core\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2140,2141,2143,2144,2145,2146,2147,2154,2155,2163,2164,2165,2166,2167,2168,2169,2192,2195,2196,2197,2198,2200,2201,2202,2203,2204,2205,2209,2211,2212,2213,2222,2223,2230,2233,2234,2235,2236,2237,2238,2239,2240,2241,2258,2349,2350,2353,2354,2355,2356,2357,2358,2359,2360,2362,2363,2364,2365,2366,2367,2368,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2384,2385,2386,2387,2388,2389,2390,2391,2392,2396,2398,2399,2400,2401,2402,2403,2404,2405,2412,2415,2417,2418,2419,2420,2421,2423,2424,2425,2426,2427,2428,2429,2430,2431,2432,2433,2434,2435,2436,2437,2438,2441", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "139354,139394,139455,139527,139585,139648,139706,139769,139826,139893,139947,140007,140086,140226,140275,140484,140522,140566,140641,140687,141824,141890,142465,142507,142573,142636,142689,142748,142807,145580,145748,145788,145846,145899,145998,146062,146100,146138,146199,146266,146492,146639,146739,146826,147661,147712,148423,148697,148733,148776,148834,148907,148953,149005,149058,149103,151066,164720,164766,164983,165029,165082,165126,165178,165228,165286,165338,165416,165500,165578,165668,165724,165758,165796,166643,166784,166858,166990,167072,167115,167167,167229,167276,167330,167375,167428,167473,167527,167578,167635,167691,167749,167800,167997,168125,168165,168213,168273,168317,168357,168397,168435,169038,169426,169535,169592,169658,169728,169805,170010,170050,170096,170152,170211,170255,170306,170366,170428,170468,170512,170556,170602,170653,170703,170761,170918", "endColumns": "39,60,71,57,62,57,62,56,66,53,59,78,56,48,58,37,43,74,45,47,65,63,41,65,62,52,58,58,62,61,39,57,52,41,63,37,37,60,66,65,81,99,86,77,50,41,60,35,42,57,72,45,51,52,44,45,41,45,53,45,52,43,51,49,57,51,33,83,77,89,55,33,37,44,140,73,131,81,42,51,61,46,53,44,52,44,53,50,56,55,57,50,56,57,39,47,59,43,39,39,37,41,39,37,56,65,69,76,65,39,45,55,58,43,50,59,61,39,43,43,45,50,49,57,58,35", "endOffsets": "139389,139450,139522,139580,139643,139701,139764,139821,139888,139942,140002,140081,140138,140270,140329,140517,140561,140636,140682,140730,141885,141949,142502,142568,142631,142684,142743,142802,142865,145637,145783,145841,145894,145936,146057,146095,146133,146194,146261,146327,146569,146734,146821,146899,147707,147749,148479,148728,148771,148829,148902,148948,149000,149053,149098,149144,151103,164761,164815,165024,165077,165121,165173,165223,165281,165333,165367,165495,165573,165663,165719,165753,165791,165836,166779,166853,166985,167067,167110,167162,167224,167271,167325,167370,167423,167468,167522,167573,167630,167686,167744,167795,167852,168050,168160,168208,168268,168312,168352,168392,168430,168472,169073,169459,169587,169653,169723,169800,169866,170045,170091,170147,170206,170250,170301,170361,170423,170463,170507,170551,170597,170648,170698,170756,170815,170949"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ce93ce5bd45704ff32cf7f9b33dbc2e2\\transformed\\databinding-adapters-8.1.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "1965,1966,2000", "startColumns": "4,4,4", "startOffsets": "129427,129484,131252", "endColumns": "56,42,40", "endOffsets": "129479,129522,131288"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\14523b378ddeb224f3413513055a3ace\\transformed\\fragment-1.6.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "1937,1983,2021", "startColumns": "4,4,4", "startOffsets": "127929,130317,132273", "endColumns": "56,64,63", "endOffsets": "127981,130377,132332"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cdebcb5cea68a5cacee791078fa3b341\\transformed\\core-splashscreen-1.0.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "497,530,635,636,637,638,1879,1880,1881,1882,1883,1884,1885,2029,2868,2869,2870,5842,5844,7130,7139,7152", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25239,26949,32157,32226,32298,32361,124256,124330,124406,124482,124559,124630,124699,132761,199071,199152,199244,408243,408352,493720,494180,494955", "endLines": "497,530,635,636,637,638,1879,1880,1881,1882,1883,1884,1885,2029,2868,2869,2870,5843,5845,7138,7151,7155", "endColumns": "59,58,68,71,62,71,73,75,75,76,70,68,70,67,80,91,92,12,12,12,12,12", "endOffsets": "25294,27003,32221,32293,32356,32428,124325,124401,124477,124554,124625,124694,124765,132824,199147,199239,199332,408347,408468,494175,494950,495223"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ab9ce3d6fabc773074c27a4a16889e6f\\transformed\\cardview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "35,688,689,690,691,1166,1167,1168,2489,5863,5865,5868", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1640,35703,35764,35826,35888,68977,69036,69093,174266,409911,409975,410101", "endLines": "35,688,689,690,691,1166,1167,1168,2495,5864,5867,5870", "endColumns": "51,60,61,61,63,58,56,53,12,12,12,12", "endOffsets": "1687,35759,35821,35883,35947,69031,69088,69142,174675,409970,410096,410224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2025416948d6099286a1c5deac9fc59b\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "641,1019,1871,1872,1873,1874,1875,1876,1877,1969,1970,1971,2091,2092,2193,2214,2361,2393,2422,2439,2440,5846,6129,6132,6138,6144,6147,6153,6157,6160,6167,6173,6176,6182,6187,6192,6199,6201,6207,6213,6221,6226,6233,6238,6244,6248,6255,6259,6265,6271,6274,6278,6279", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "32550,59725,123760,123824,123879,123947,124014,124079,124136,129637,129685,129733,136915,136978,145642,146904,165372,167857,169871,170820,170870,408473,428914,429019,429264,429602,429748,430088,430300,430463,430870,431208,431331,431670,431909,432166,432537,432597,432935,433221,433670,433962,434350,434655,434999,435244,435574,435781,436049,436322,436466,436667,436714", "endLines": "641,1019,1871,1872,1873,1874,1875,1876,1877,1969,1970,1971,2091,2092,2193,2214,2361,2395,2422,2439,2440,5862,6131,6137,6143,6146,6152,6156,6159,6166,6172,6175,6181,6186,6191,6198,6200,6206,6212,6220,6225,6232,6237,6243,6247,6254,6258,6264,6270,6273,6277,6278,6279", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55", "endOffsets": "32618,59789,123819,123874,123942,124009,124074,124131,124188,129680,129728,129789,136973,137036,145675,146956,165411,167992,170005,170865,170913,409906,429014,429259,429597,429743,430083,430295,430458,430865,431203,431326,431665,431904,432161,432532,432592,432930,433216,433665,433957,434345,434650,434994,435239,435569,435776,436044,436317,436461,436662,436709,436765"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dea3e6fcf5697c9423505c2045e63399\\transformed\\recyclerview-1.3.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "508,1228,1229,1230,1238,1239,1240,1943", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "25744,73015,73074,73122,73789,73864,73940,128219", "endColumns": "55,58,47,55,74,75,71,65", "endOffsets": "25795,73069,73117,73173,73859,73935,74007,128280"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b6d17d87dae82d892d5ed1b2c35b7a41\\transformed\\coordinatorlayout-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "109,9818", "startColumns": "4,4", "startOffsets": "5765,674671", "endLines": "109,9820", "endColumns": "60,12", "endOffsets": "5821,674811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cacdfc042dfae4e43c008dc67295820e\\transformed\\navigation-ui-2.7.4\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "2027,2199,2351,2352", "startColumns": "4,4,4,4", "startOffsets": "132651,145941,164820,164896", "endColumns": "52,56,75,86", "endOffsets": "132699,145993,164891,164978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\69000e4e45894c9884b32a91ee0b736c\\transformed\\navigation-runtime-2.7.4\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "1957", "startColumns": "4", "startOffsets": "128929", "endColumns": "52", "endOffsets": "128977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b3220947d7de38838cf340bae783eebe\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2019", "startColumns": "4", "startOffsets": "132169", "endColumns": "53", "endOffsets": "132218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\812136ae31eafce15fafbc5dfed6f6ac\\transformed\\play-services-basement-18.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "2032,2182", "startColumns": "4,4", "startOffsets": "132964,144287", "endColumns": "67,166", "endOffsets": "133027,144449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\59f4b2c5fb29ed0d6dfb40492e70e152\\transformed\\work-runtime-2.8.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "642,643,644,646", "startColumns": "4,4,4,4", "startOffsets": "32623,32688,32758,32884", "endColumns": "64,69,63,60", "endOffsets": "32683,32753,32817,32940"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f059acc78cb16502a6170c70ab7b48a3\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2139", "startColumns": "4", "startOffsets": "140143", "endColumns": "82", "endOffsets": "140221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fb1d9df47d7b984f4dadfbaace818bfe\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1942,1968", "startColumns": "4,4", "startOffsets": "128165,129570", "endColumns": "53,66", "endOffsets": "128214,129632"}}, {"source": "C:\\Muslim Core\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,2,-1,-1,161,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,-1,-1,4,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,99,-1,-1,7858,-1,-1,-1,-1,-1", "endLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,56,-1,-1,167,-1,-1,-1,-1,-1", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,12,-1,-1,12,-1,-1,-1,-1,-1", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,3078,-1,-1,8273,-1,-1,-1,-1,-1"}, "to": {"startLines": "6422,6426,6430,6831,6839,6843,6848,6853,6857,6861,6866,7065,7120,7124,9780,9787,9794,9801,9807,9812", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "445977,446192,446409,472939,473386,473607,473899,474194,474440,474681,474978,490371,493119,493303,672510,672930,673328,673753,674073,674318", "endLines": "6425,6429,6433,6838,6842,6847,6852,6856,6860,6865,6870,7119,7123,7129,9786,9793,9800,9806,9811,9817", "endColumns": "12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12", "endOffsets": "446187,446404,446618,473381,473602,473894,474189,474435,474676,474973,475273,493114,493298,493715,672925,673323,673748,674068,674313,674666"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e6699e020abbc856e9d4962d528675a8\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "137,227,230,583,639,640,647,648,649,650,651,653,654,661,662,667,668,674,675,676,677,678,679,684,685,743,744,745,746,750,751,752,753,754,755,944,945,946,947,948,949,950,951,952,953,954,955,1020,1021,1022,1023,1024,1025,1026,1027,1042,1043,1044,1045,1046,1047,1053,1054,1055,1056,1077,1078,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1226,1227,1231,1232,1233,1234,1235,1236,1237,1886,1887,1888,1889,1890,1891,1892,1893,1931,1932,1933,1934,1941,1972,1973,1984,2014,2022,2023,2026,2028,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2406,2442,2443,2444,2445,2446,2468,2476,2477,2481,2485,2496,2501,2530,2537,2541,2545,2550,2554,2558,2562,2566,2570,2574,2580,2584,2590,2594,2600,2604,2609,2613,2616,2620,2626,2630,2636,2640,2646,2649,2653,2657,2661,2665,2669,2670,2671,2672,2675,2678,2681,2684,2688,2689,2690,2691,2732,2735,2737,2739,2741,2746,2747,2751,2757,2761,2762,2764,2776,2777,2781,2787,2791,2871,2872,2876,2903,2907,2908,2912,4709,4881,4907,5078,5104,5135,5143,5149,5165,5187,5192,5197,5207,5216,5225,5229,5236,5255,5262,5263,5272,5275,5278,5282,5286,5290,5293,5294,5299,5304,5314,5319,5326,5332,5333,5336,5340,5345,5347,5349,5352,5355,5357,5361,5364,5371,5374,5377,5381,5383,5387,5389,5391,5393,5397,5405,5413,5425,5431,5440,5443,5454,5457,5458,5463,5464,5965,6034,6108,6109,6119,6128,6280,6282,6286,6289,6292,6295,6298,6301,6304,6307,6311,6314,6317,6320,6324,6327,6331,6491,6492,6493,6494,6495,6496,6497,6498,6499,6500,6501,6502,6503,6504,6505,6506,6507,6508,6509,6510,6511,6513,6515,6516,6517,6518,6519,6520,6521,6522,6524,6525,6527,6528,6530,6532,6533,6535,6536,6537,6538,6539,6540,6542,6543,6544,6545,6546,6871,6873,6875,6877,6878,6879,6880,6881,6882,6883,6884,6885,6886,6887,6888,6889,6891,6892,6893,6894,6895,6896,6897,6899,6903,7156,7157,7158,7159,7160,7161,7165,7166,7167,7708,7710,7712,7714,7716,7718,7719,7720,7721,7723,7725,7727,7728,7729,7730,7731,7732,7733,7734,7735,7736,7737,7738,7741,7742,7743,7744,7746,7748,7749,7751,7752,7754,7756,7758,7759,7760,7761,7762,7763,7764,7765,7766,7767,7768,7769,7771,7772,7773,7774,7776,7777,7778,7779,7780,7782,7784,7786,7788,7789,7790,7791,7792,7793,7794,7795,7796,7797,7798,7799,7800,7801,7802", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6941,11486,11639,30035,32433,32488,32945,33009,33079,33140,33215,33337,33414,33851,33936,34264,34340,34682,34759,34837,34943,35049,35128,35457,35514,39516,39590,39665,39730,39925,39985,40046,40118,40191,40258,54372,54431,54490,54549,54608,54667,54721,54775,54828,54882,54936,54990,59794,59868,59947,60020,60094,60165,60237,60309,61110,61167,61225,61298,61372,61446,61763,61835,61908,61978,63069,63129,63320,63389,63458,63528,63602,63678,63742,63819,63895,63972,64037,64106,64183,64258,64327,64395,64472,64538,64599,64696,64761,64830,64929,65000,65059,65117,65174,65233,65297,65368,65440,65512,65584,65656,65723,65791,65859,65918,65981,66045,66135,66226,66286,66352,66419,66485,66555,66619,66672,66739,66800,66867,66980,67038,67101,67166,67231,67306,67379,67451,67495,67542,67588,67637,67698,67759,67820,67882,67946,68010,68074,68139,68202,68262,68323,68389,68448,68508,68570,68641,68701,72842,72928,73178,73268,73355,73443,73525,73608,73698,124770,124822,124880,124925,124991,125055,125112,125169,127623,127680,127728,127777,128131,129794,129841,130382,131941,132337,132401,132591,132704,137488,137562,137632,137710,137764,137834,137919,137967,138013,138074,138137,138203,138267,138338,138401,138466,138530,138591,138652,138704,138777,138851,138920,138995,139069,139143,139284,168477,170954,171032,171122,171210,171306,172815,173397,173486,173733,174014,174680,174965,176774,177251,177473,177695,177971,178198,178428,178658,178888,179118,179345,179764,179990,180415,180645,181073,181292,181575,181783,181914,182141,182567,182792,183219,183440,183865,183985,184261,184562,184886,185177,185491,185628,185759,185864,186106,186273,186477,186685,186956,187068,187180,187285,189378,189592,189738,189878,189964,190312,190400,190646,191064,191313,191395,191493,192150,192250,192502,192926,193181,199337,199426,199663,201687,201929,202031,202284,337364,348045,349561,360256,361784,363541,364167,364587,365848,367113,367369,367605,368152,368646,369251,369449,370029,371397,371772,371890,372428,372585,372781,373054,373310,373480,373621,373685,374050,374417,375093,375357,375695,376048,376142,376328,376634,376896,377021,377148,377387,377598,377717,377910,378087,378542,378723,378845,379104,379217,379404,379506,379613,379742,380017,380525,381021,381898,382192,382762,382911,383643,383815,383899,384235,384327,416653,421884,427599,427661,428239,428823,436770,436883,437112,437272,437424,437595,437761,437930,438097,438260,438503,438673,438846,439017,439291,439490,439695,449707,449791,449887,449983,450081,450181,450283,450385,450487,450589,450691,450791,450887,450999,451128,451251,451382,451513,451611,451725,451819,451959,452093,452189,452301,452401,452517,452613,452725,452825,452965,453101,453265,453395,453553,453703,453844,453988,454123,454235,454385,454513,454641,454777,454909,455039,455169,455281,475278,475424,475568,475706,475772,475862,475938,476042,476132,476234,476342,476450,476550,476630,476722,476820,476930,476982,477060,477166,477258,477362,477472,477594,477757,495228,495308,495408,495498,495608,495698,495939,496033,496139,536016,536116,536228,536342,536458,536574,536668,536782,536894,536996,537116,537238,537320,537424,537544,537670,537768,537862,537950,538062,538178,538300,538412,538587,538703,538789,538881,538993,539117,539184,539310,539378,539506,539650,539778,539847,539942,540057,540170,540269,540378,540489,540600,540701,540806,540906,541036,541127,541250,541344,541456,541542,541646,541742,541830,541948,542052,542156,542282,542370,542478,542578,542668,542778,542862,542964,543048,543102,543166,543272,543358,543468,543552", "endLines": "137,227,230,583,639,640,647,648,649,650,651,653,654,661,662,667,668,674,675,676,677,678,679,684,685,743,744,745,746,750,751,752,753,754,755,944,945,946,947,948,949,950,951,952,953,954,955,1020,1021,1022,1023,1024,1025,1026,1027,1042,1043,1044,1045,1046,1047,1053,1054,1055,1056,1077,1078,1082,1083,1084,1085,1086,1087,1088,1089,1090,1091,1092,1093,1094,1095,1096,1097,1098,1099,1100,1101,1102,1103,1104,1105,1106,1107,1108,1109,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1136,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1148,1149,1150,1151,1152,1153,1154,1155,1156,1157,1158,1159,1160,1161,1162,1226,1227,1231,1232,1233,1234,1235,1236,1237,1886,1887,1888,1889,1890,1891,1892,1893,1931,1932,1933,1934,1941,1972,1973,1984,2014,2022,2023,2026,2028,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2121,2122,2123,2124,2125,2406,2442,2443,2444,2445,2446,2475,2476,2480,2484,2488,2500,2506,2536,2540,2544,2549,2553,2557,2561,2565,2569,2573,2579,2583,2589,2593,2599,2603,2608,2612,2615,2619,2625,2629,2635,2639,2645,2648,2652,2656,2660,2664,2668,2669,2670,2671,2674,2677,2680,2683,2687,2688,2689,2690,2691,2734,2736,2738,2740,2745,2746,2750,2756,2760,2761,2763,2775,2776,2780,2786,2790,2791,2871,2875,2902,2906,2907,2911,2939,4880,4906,5077,5103,5134,5142,5148,5164,5186,5191,5196,5206,5215,5224,5228,5235,5254,5261,5262,5271,5274,5277,5281,5285,5289,5292,5293,5298,5303,5313,5318,5325,5331,5332,5335,5339,5344,5346,5348,5351,5354,5356,5360,5363,5370,5373,5376,5380,5382,5386,5388,5390,5392,5396,5404,5412,5424,5430,5439,5442,5453,5456,5457,5462,5463,5468,6033,6103,6108,6118,6127,6128,6281,6285,6288,6291,6294,6297,6300,6303,6306,6310,6313,6316,6319,6323,6326,6330,6334,6491,6492,6493,6494,6495,6496,6497,6498,6499,6500,6501,6502,6503,6504,6505,6506,6507,6508,6509,6510,6512,6514,6515,6516,6517,6518,6519,6520,6521,6523,6524,6526,6527,6529,6531,6532,6534,6535,6536,6537,6538,6539,6541,6542,6543,6544,6545,6546,6872,6874,6876,6877,6878,6879,6880,6881,6882,6883,6884,6885,6886,6887,6888,6890,6891,6892,6893,6894,6895,6896,6898,6902,6906,7156,7157,7158,7159,7160,7164,7165,7166,7167,7709,7711,7713,7715,7717,7718,7719,7720,7722,7724,7726,7727,7728,7729,7730,7731,7732,7733,7734,7735,7736,7737,7740,7741,7742,7743,7745,7747,7748,7750,7751,7753,7755,7757,7758,7759,7760,7761,7762,7763,7764,7765,7766,7767,7768,7770,7771,7772,7773,7775,7776,7777,7778,7779,7781,7783,7785,7787,7788,7789,7790,7791,7792,7793,7794,7795,7796,7797,7798,7799,7800,7801,7802", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "6991,11526,11683,30071,32483,32545,33004,33074,33135,33210,33286,33409,33487,33931,34013,34335,34411,34754,34832,34938,35044,35123,35203,35509,35567,39585,39660,39725,39791,39980,40041,40113,40186,40253,40321,54426,54485,54544,54603,54662,54716,54770,54823,54877,54931,54985,55039,59863,59942,60015,60089,60160,60232,60304,60377,61162,61220,61293,61367,61441,61516,61830,61903,61973,62044,63124,63185,63384,63453,63523,63597,63673,63737,63814,63890,63967,64032,64101,64178,64253,64322,64390,64467,64533,64594,64691,64756,64825,64924,64995,65054,65112,65169,65228,65292,65363,65435,65507,65579,65651,65718,65786,65854,65913,65976,66040,66130,66221,66281,66347,66414,66480,66550,66614,66667,66734,66795,66862,66975,67033,67096,67161,67226,67301,67374,67446,67490,67537,67583,67632,67693,67754,67815,67877,67941,68005,68069,68134,68197,68257,68318,68384,68443,68503,68565,68636,68696,68764,72923,73010,73263,73350,73438,73520,73603,73693,73784,124817,124875,124920,124986,125050,125107,125164,125218,127675,127723,127772,127823,128160,129836,129885,130423,131968,132396,132458,132646,132756,137557,137627,137705,137759,137829,137914,137962,138008,138069,138132,138198,138262,138333,138396,138461,138525,138586,138647,138699,138772,138846,138915,138990,139064,139138,139279,139349,168525,171027,171117,171205,171301,171391,173392,173481,173728,174009,174261,174960,175353,177246,177468,177690,177966,178193,178423,178653,178883,179113,179340,179759,179985,180410,180640,181068,181287,181570,181778,181909,182136,182562,182787,183214,183435,183860,183980,184256,184557,184881,185172,185486,185623,185754,185859,186101,186268,186472,186680,186951,187063,187175,187280,187397,189587,189733,189873,189959,190307,190395,190641,191059,191308,191390,191488,192145,192245,192497,192921,193176,193270,199421,199658,201682,201924,202026,202279,204435,348040,349556,360251,361779,363536,364162,364582,365843,367108,367364,367600,368147,368641,369246,369444,370024,371392,371767,371885,372423,372580,372776,373049,373305,373475,373616,373680,374045,374412,375088,375352,375690,376043,376137,376323,376629,376891,377016,377143,377382,377593,377712,377905,378082,378537,378718,378840,379099,379212,379399,379501,379608,379737,380012,380520,381016,381893,382187,382757,382906,383638,383810,383894,384230,384322,384600,421879,427250,427656,428234,428818,428909,436878,437107,437267,437419,437590,437756,437925,438092,438255,438498,438668,438841,439012,439286,439485,439690,440020,449786,449882,449978,450076,450176,450278,450380,450482,450584,450686,450786,450882,450994,451123,451246,451377,451508,451606,451720,451814,451954,452088,452184,452296,452396,452512,452608,452720,452820,452960,453096,453260,453390,453548,453698,453839,453983,454118,454230,454380,454508,454636,454772,454904,455034,455164,455276,455416,475419,475563,475701,475767,475857,475933,476037,476127,476229,476337,476445,476545,476625,476717,476815,476925,476977,477055,477161,477253,477357,477467,477589,477752,477909,495303,495403,495493,495603,495693,495934,496028,496134,496226,536111,536223,536337,536453,536569,536663,536777,536889,536991,537111,537233,537315,537419,537539,537665,537763,537857,537945,538057,538173,538295,538407,538582,538698,538784,538876,538988,539112,539179,539305,539373,539501,539645,539773,539842,539937,540052,540165,540264,540373,540484,540595,540696,540801,540901,541031,541122,541245,541339,541451,541537,541641,541737,541825,541943,542047,542151,542277,542365,542473,542573,542663,542773,542857,542959,543043,543097,543161,543267,543353,543463,543547,543667"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f2e0173b205ab009bcb83bc37b0023a7\\transformed\\navigation-fragment-2.7.4\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1"}, "to": {"startLines": "1878,1958,1979,1980", "startColumns": "4,4,4,4", "startOffsets": "124193,128982,130121,130180", "endColumns": "62,56,58,48", "endOffsets": "124251,129034,130175,130224"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\657d4b970ca786a19c67b9546b583e6a\\transformed\\firebase-messaging-23.3.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2221", "startColumns": "4", "startOffsets": "147579", "endColumns": "81", "endOffsets": "147656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fd9e756f56bf090cd291cb3d53d819c4\\transformed\\constraintlayout-2.1.4\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,9,11,17,18,26,28,29,36,42,44,45,46,47,48,105,106,107,108,110,114,115,116,119,129,139,167,168,173,174,179,184,185,186,191,192,197,198,203,204,205,211,212,213,218,224,225,239,240,246,247,248,249,252,255,258,259,262,265,266,267,268,269,272,275,276,277,278,284,289,292,295,296,297,302,303,304,307,310,311,314,317,320,323,324,325,328,331,332,337,338,344,349,352,355,356,357,358,359,360,361,362,363,364,365,366,382,464,465,466,467,472,479,485,486,487,490,495,498,506,507,528,544,581,582,586,587,597,598,599,605,608,614,618,619,620,621,622,631,1950,2016", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,434,546,781,842,1133,1240,1290,1692,1999,2119,2174,2234,2299,2358,5544,5596,5657,5719,5826,5959,6011,6061,6222,6629,7052,8994,9053,9250,9307,9502,9683,9737,9794,9986,10044,10240,10296,10490,10547,10598,10820,10872,10927,11117,11333,11383,12132,12188,12394,12455,12515,12585,12718,12849,12977,13045,13174,13300,13362,13425,13493,13560,13683,13808,13875,13940,14005,14294,14475,14596,14717,14783,14850,15060,15129,15195,15320,15446,15513,15639,15766,15891,16018,16074,16139,16265,16388,16453,16661,16728,17016,17196,17316,17436,17501,17563,17625,17689,17751,17810,17870,17931,17992,18051,18111,18771,23777,23828,23877,23925,24212,24504,24734,24781,24841,24947,25127,25299,25634,25688,26853,27595,29925,29976,30185,30237,30612,30671,30725,30963,31141,31343,31482,31528,31583,31628,31672,32020,128551,132021", "endLines": "8,9,15,17,25,26,28,29,36,42,44,45,46,47,48,105,106,107,108,113,114,115,116,128,136,139,167,172,173,178,183,184,185,190,191,196,197,202,203,204,210,211,212,217,223,224,225,239,245,246,247,248,251,254,257,258,261,264,265,266,267,268,271,274,275,276,277,283,288,291,294,295,296,301,302,303,306,309,310,313,316,319,322,323,324,327,330,331,336,337,343,348,351,354,355,356,357,358,359,360,361,362,363,364,365,381,387,464,465,466,467,478,484,485,486,489,494,495,505,506,507,528,544,581,582,586,595,597,598,604,605,613,617,618,619,620,621,630,634,1950,2016", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44", "endOffsets": "429,485,727,837,1128,1180,1285,1338,1735,2045,2169,2229,2294,2353,2415,5591,5652,5714,5760,5954,6006,6056,6107,6624,6936,7092,9048,9245,9302,9497,9678,9732,9789,9981,10039,10235,10291,10485,10542,10593,10815,10867,10922,11112,11328,11378,11430,12183,12389,12450,12510,12580,12713,12844,12972,13040,13169,13295,13357,13420,13488,13555,13678,13803,13870,13935,14000,14289,14470,14591,14712,14778,14845,15055,15124,15190,15315,15441,15508,15634,15761,15886,16013,16069,16134,16260,16383,16448,16656,16723,17011,17191,17311,17431,17496,17558,17620,17684,17746,17805,17865,17926,17987,18046,18106,18766,19017,23823,23872,23920,23978,24499,24729,24776,24836,24942,25122,25176,25629,25683,25739,26894,27637,29971,30030,30232,30562,30666,30720,30958,31013,31338,31477,31523,31578,31623,31667,32015,32152,128587,132061"}}, {"source": "C:\\Muslim Core\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "652,655,656,659,660,663,664,665,666,669,670,671,672,673,700,701,702,703,704,705,706,707,747,748,749,756,757,758,759,760,761,762,763,764,765,766,767,768,769,770,771,772,1011,1013,1014,1015,1016,1017,1018,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1039,1040,1041,1048,1049,1050,1051,1052,1057,1058,1059,1060,1061,1062,1063,1064,1065,1066,1067,1068,1069,1070,1071,1072,1073,1074,1075,1076,1079,1080,1081", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "33291,33492,33540,33746,33796,34018,34076,34139,34199,34416,34469,34527,34582,34642,36623,36679,36738,36803,36870,36920,36973,37031,39796,39838,39885,40326,40365,40412,40464,40517,40570,40624,40680,40737,40793,40851,40912,40979,41048,41100,41155,41215,59319,59440,59492,59537,59584,59630,59676,60382,60436,60489,60540,60591,60638,60685,60732,60783,60839,60891,60950,61013,61061,61521,61574,61622,61671,61721,62049,62095,62146,62194,62244,62291,62343,62392,62446,62494,62547,62596,62647,62703,62761,62814,62869,62918,62969,63018,63190,63238,63280", "endColumns": "45,47,45,49,54,57,62,59,64,52,57,54,59,39,55,58,64,66,49,52,57,61,41,46,39,38,46,51,52,52,53,55,56,55,57,60,66,68,51,54,59,61,57,51,44,46,45,45,48,53,52,50,50,46,46,46,50,55,51,58,62,47,48,52,47,48,49,41,45,50,47,49,46,51,48,53,47,52,48,50,55,57,52,54,48,50,48,50,47,41,39", "endOffsets": "33332,33535,33581,33791,33846,34071,34134,34194,34259,34464,34522,34577,34637,34677,36674,36733,36798,36865,36915,36968,37026,37088,39833,39880,39920,40360,40407,40459,40512,40565,40619,40675,40732,40788,40846,40907,40974,41043,41095,41150,41210,41272,59372,59487,59532,59579,59625,59671,59720,60431,60484,60535,60586,60633,60680,60727,60778,60834,60886,60945,61008,61056,61105,61569,61617,61666,61716,61758,62090,62141,62189,62239,62286,62338,62387,62441,62489,62542,62591,62642,62698,62756,62809,62864,62913,62964,63013,63064,63233,63275,63315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f2278bd78dd5b8de51d11a4c518bddc3\\transformed\\activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "1974,2018", "startColumns": "4,4", "startOffsets": "129890,132109", "endColumns": "41,59", "endOffsets": "129927,132164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\52cfa56fd2ad7827c32e9e051048beda\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "2020", "startColumns": "4", "startOffsets": "132223", "endColumns": "49", "endOffsets": "132268"}}]}]}