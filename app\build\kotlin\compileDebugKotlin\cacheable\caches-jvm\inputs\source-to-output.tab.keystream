V$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\repositories\UserRepositoryImpl.ktY$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\fragments\SettingsFragment.kt^$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\fragments\PrayerTrackerFragment.ktT$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\entities\AdhkarEntities.ktZ$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\repositories\LocationRepositoryImpl.kt\$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\fragments\DuaCategoryFragment.ktY$PROJECT_DIR$\app\src\main\java\com\muslimcore\domain\usecases\prayer\LogPrayerUseCase.kta$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\viewmodels\PrayerLocationViewModel.kt^$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\viewmodels\SurahDetailViewModel.ktK$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\MainActivity.ktV$PROJECT_DIR$\app\src\main\java\com\muslimcore\domain\repositories\PrayerRepository.kt^$PROJECT_DIR$\app\src\main\java\com\muslimcore\domain\usecases\prayer\GetPrayerTimesUseCase.ktX$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\repositories\PrayerRepositoryImpl.ktY$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\services\PrayerNotificationService.ktS$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\models\QuranJsonModels.ktW$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\managers\PrayerTimeManager.ktU$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\services\PrayerService.ktV$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\fragments\QuranFragment.ktU$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\managers\LocationManager.ktT$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\utils\FontPreferences.ktX$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\viewmodels\QuranViewModel.ktM$PROJECT_DIR$\app\src\main\java\com\muslimcore\domain\entities\PrayerTimes.ktX$PROJECT_DIR$\app\src\main\java\com\muslimcore\domain\repositories\LocationRepository.ktU$PROJECT_DIR$\app\src\main\java\com\muslimcore\domain\repositories\QuranRepository.ktW$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\viewmodels\MainViewModel.kt]$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\activities\PrayerAlertActivity.ktW$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\utils\AyahCircleDrawable.kt\$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\adapters\TajweedLegendAdapter.ktO$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\models\TajweedData.ktW$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\repositories\QuranRepositoryImpl.ktP$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\remote\api\PrayerTimesApi.ktW$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\adapters\CalendarAdapter.kt`$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\fragments\TajweedSettingsFragment.ktG$PROJECT_DIR$\app\src\main\java\com\muslimcore\domain\entities\Quran.ktT$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\views\TajweedTextView.ktW$PROJECT_DIR$\app\src\main\java\com\muslimcore\domain\usecases\quran\GetSurahUseCase.ktQ$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\utils\ThemeManager.ktY$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\dialogs\FontSettingsDialog.ktS$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\adapters\AyahAdapter.ktV$PROJECT_DIR$\app\src\main\java\com\muslimcore\domain\repositories\AdhkarRepository.ktW$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\fragments\AdhkarFragment.ktF$PROJECT_DIR$\app\src\main\java\com\muslimcore\domain\entities\User.ktJ$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\dao\AdhkarDao.kt[$PROJECT_DIR$\app\src\main\java\com\muslimcore\domain\usecases\GetCurrentLocationUseCase.ktZ$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\services\MuslimCoreMessagingService.ktT$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\receivers\PrayerAlarmReceiver.ktU$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\fragments\BaseFragment.kt]$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\fragments\TajweedRulesFragment.ktW$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\adapters\LocationAdapter.ktU$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\entities\PrayerLogEntity.ktJ$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\remote\api\QuranApi.ktP$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\QuranJsonDataSource.ktW$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\utils\CustomTypefaceSpan.ktM$PROJECT_DIR$\app\src\main\java\com\muslimcore\domain\models\LocationState.ktR$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\adapters\DuaAdapter.ktY$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\viewmodels\PrayerViewModel.ktT$PROJECT_DIR$\app\src\main\java\com\muslimcore\domain\repositories\UserRepository.ktS$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\entities\QuranEntities.ktW$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\workers\PrayerNotificationWorker.ktI$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\dao\QuranDao.kte$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\fragments\NotificationSettingsFragment.ktT$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\adapters\SurahAdapter.kt_$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\fragments\LocationPickerFragment.kt]$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\datasource\QuranAssetsDataSource.ktH$PROJECT_DIR$\app\src\main\java\com\muslimcore\domain\entities\Adhkar.ktM$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\receivers\BootReceiver.ktO$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\PreferencesManager.ktG$PROJECT_DIR$\app\src\main\java\com\muslimcore\MuslimCoreApplication.ktJ$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\dao\PrayerDao.ktZ$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\adapters\PrayerTimesAdapter.ktO$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\remote\dto\QuranResponse.ktW$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\fragments\PrayerFragment.ktC$PROJECT_DIR$\app\src\main\java\com\muslimcore\di\FirebaseModule.kt\$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\fragments\SurahDetailFragment.kt[$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\dialogs\LocationPickerDialog.ktS$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\utils\QuranFontUtils.ktB$PROJECT_DIR$\app\src\main\java\com\muslimcore\di\NetworkModule.ktZ$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\utils\QuranTajweedProcessor.ktU$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\remote\dto\PrayerTimesResponse.ktX$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\entities\StatisticsEntities.ktR$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\converters\Converters.ktE$PROJECT_DIR$\app\src\main\java\com\muslimcore\di\RepositoryModule.ktW$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\entities\PrayerTimesEntity.ktC$PROJECT_DIR$\app\src\main\java\com\muslimcore\di\DatabaseModule.ktU$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\receivers\BootReceiver.kt\$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\adapters\PrayerTrackerAdapter.ktC$PROJECT_DIR$\app\src\main\java\com\muslimcore\di\LocationModule.kta$PROJECT_DIR$\app\src\main\java\com\muslimcore\presentation\utils\ProfessionalTajweedProcessor.ktY$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\repositories\QuranAssetsRepository.ktU$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\repository\QuranJsonRepository.ktX$PROJECT_DIR$\app\src\main\java\com\muslimcore\data\local\database\MuslimCoreDatabase.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   