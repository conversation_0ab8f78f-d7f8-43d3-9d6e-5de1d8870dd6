<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Muslim Core\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Muslim Core\app\src\main\res"><file name="bottom_nav_color" path="C:\Muslim Core\app\src\main\res\color\bottom_nav_color.xml" qualifiers="" type="color"/><file name="ayah_number_decoration" path="C:\Muslim Core\app\src\main\res\drawable\ayah_number_decoration.xml" qualifiers="" type="drawable"/><file name="bg_error_message" path="C:\Muslim Core\app\src\main\res\drawable\bg_error_message.xml" qualifiers="" type="drawable"/><file name="bismillah" path="C:\Muslim Core\app\src\main\res\drawable\bismillah.xml" qualifiers="" type="drawable"/><file name="blur_background" path="C:\Muslim Core\app\src\main\res\drawable\blur_background.xml" qualifiers="" type="drawable"/><file name="bold_button_selector" path="C:\Muslim Core\app\src\main\res\drawable\bold_button_selector.xml" qualifiers="" type="drawable"/><file name="button_rounded" path="C:\Muslim Core\app\src\main\res\drawable\button_rounded.xml" qualifiers="" type="drawable"/><file name="button_rounded_outline" path="C:\Muslim Core\app\src\main\res\drawable\button_rounded_outline.xml" qualifiers="" type="drawable"/><file name="button_rounded_small" path="C:\Muslim Core\app\src\main\res\drawable\button_rounded_small.xml" qualifiers="" type="drawable"/><file name="calendar_day_completed" path="C:\Muslim Core\app\src\main\res\drawable\calendar_day_completed.xml" qualifiers="" type="drawable"/><file name="calendar_day_partial" path="C:\Muslim Core\app\src\main\res\drawable\calendar_day_partial.xml" qualifiers="" type="drawable"/><file name="calendar_day_selected" path="C:\Muslim Core\app\src\main\res\drawable\calendar_day_selected.xml" qualifiers="" type="drawable"/><file name="calendar_progress_circle" path="C:\Muslim Core\app\src\main\res\drawable\calendar_progress_circle.xml" qualifiers="" type="drawable"/><file name="cancel_button_selector" path="C:\Muslim Core\app\src\main\res\drawable\cancel_button_selector.xml" qualifiers="" type="drawable"/><file name="circle_background" path="C:\Muslim Core\app\src\main\res\drawable\circle_background.xml" qualifiers="" type="drawable"/><file name="circle_primary_green" path="C:\Muslim Core\app\src\main\res\drawable\circle_primary_green.xml" qualifiers="" type="drawable"/><file name="circle_shape" path="C:\Muslim Core\app\src\main\res\drawable\circle_shape.xml" qualifiers="" type="drawable"/><file name="continue_reading_background" path="C:\Muslim Core\app\src\main\res\drawable\continue_reading_background.xml" qualifiers="" type="drawable"/><file name="dialog_background" path="C:\Muslim Core\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="done_button_selector" path="C:\Muslim Core\app\src\main\res\drawable\done_button_selector.xml" qualifiers="" type="drawable"/><file name="edit_text_background" path="C:\Muslim Core\app\src\main\res\drawable\edit_text_background.xml" qualifiers="" type="drawable"/><file name="font_button_selector" path="C:\Muslim Core\app\src\main\res\drawable\font_button_selector.xml" qualifiers="" type="drawable"/><file name="ic_adhkar" path="C:\Muslim Core\app\src\main\res\drawable\ic_adhkar.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="C:\Muslim Core\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_arrow_drop_down" path="C:\Muslim Core\app\src\main\res\drawable\ic_arrow_drop_down.xml" qualifiers="" type="drawable"/><file name="ic_arrow_forward" path="C:\Muslim Core\app\src\main\res\drawable\ic_arrow_forward.xml" qualifiers="" type="drawable"/><file name="ic_arrow_left" path="C:\Muslim Core\app\src\main\res\drawable\ic_arrow_left.xml" qualifiers="" type="drawable"/><file name="ic_arrow_right" path="C:\Muslim Core\app\src\main\res\drawable\ic_arrow_right.xml" qualifiers="" type="drawable"/><file name="ic_asr" path="C:\Muslim Core\app\src\main\res\drawable\ic_asr.xml" qualifiers="" type="drawable"/><file name="ic_asr_colored" path="C:\Muslim Core\app\src\main\res\drawable\ic_asr_colored.xml" qualifiers="" type="drawable"/><file name="ic_bold" path="C:\Muslim Core\app\src\main\res\drawable\ic_bold.xml" qualifiers="" type="drawable"/><file name="ic_book" path="C:\Muslim Core\app\src\main\res\drawable\ic_book.xml" qualifiers="" type="drawable"/><file name="ic_bookmark_border" path="C:\Muslim Core\app\src\main\res\drawable\ic_bookmark_border.xml" qualifiers="" type="drawable"/><file name="ic_check" path="C:\Muslim Core\app\src\main\res\drawable\ic_check.xml" qualifiers="" type="drawable"/><file name="ic_circle" path="C:\Muslim Core\app\src\main\res\drawable\ic_circle.xml" qualifiers="" type="drawable"/><file name="ic_dhuhr" path="C:\Muslim Core\app\src\main\res\drawable\ic_dhuhr.xml" qualifiers="" type="drawable"/><file name="ic_dhuhr_colored" path="C:\Muslim Core\app\src\main\res\drawable\ic_dhuhr_colored.xml" qualifiers="" type="drawable"/><file name="ic_fajr" path="C:\Muslim Core\app\src\main\res\drawable\ic_fajr.xml" qualifiers="" type="drawable"/><file name="ic_fajr_colored" path="C:\Muslim Core\app\src\main\res\drawable\ic_fajr_colored.xml" qualifiers="" type="drawable"/><file name="ic_info" path="C:\Muslim Core\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_isha" path="C:\Muslim Core\app\src\main\res\drawable\ic_isha.xml" qualifiers="" type="drawable"/><file name="ic_isha_colored" path="C:\Muslim Core\app\src\main\res\drawable\ic_isha_colored.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Muslim Core\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_location" path="C:\Muslim Core\app\src\main\res\drawable\ic_location.xml" qualifiers="" type="drawable"/><file name="ic_maghrib" path="C:\Muslim Core\app\src\main\res\drawable\ic_maghrib.xml" qualifiers="" type="drawable"/><file name="ic_maghrib_colored" path="C:\Muslim Core\app\src\main\res\drawable\ic_maghrib_colored.xml" qualifiers="" type="drawable"/><file name="ic_mic" path="C:\Muslim Core\app\src\main\res\drawable\ic_mic.xml" qualifiers="" type="drawable"/><file name="ic_more_horiz" path="C:\Muslim Core\app\src\main\res\drawable\ic_more_horiz.xml" qualifiers="" type="drawable"/><file name="ic_palette" path="C:\Muslim Core\app\src\main\res\drawable\ic_palette.xml" qualifiers="" type="drawable"/><file name="ic_play_arrow" path="C:\Muslim Core\app\src\main\res\drawable\ic_play_arrow.xml" qualifiers="" type="drawable"/><file name="ic_prayer" path="C:\Muslim Core\app\src\main\res\drawable\ic_prayer.xml" qualifiers="" type="drawable"/><file name="ic_quran" path="C:\Muslim Core\app\src\main\res\drawable\ic_quran.xml" qualifiers="" type="drawable"/><file name="ic_search" path="C:\Muslim Core\app\src\main\res\drawable\ic_search.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\Muslim Core\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_settings_outline" path="C:\Muslim Core\app\src\main\res\drawable\ic_settings_outline.xml" qualifiers="" type="drawable"/><file name="ic_share" path="C:\Muslim Core\app\src\main\res\drawable\ic_share.xml" qualifiers="" type="drawable"/><file name="ic_sound_notification" path="C:\Muslim Core\app\src\main\res\drawable\ic_sound_notification.xml" qualifiers="" type="drawable"/><file name="ic_sunrise" path="C:\Muslim Core\app\src\main\res\drawable\ic_sunrise.xml" qualifiers="" type="drawable"/><file name="ic_sunrise_colored" path="C:\Muslim Core\app\src\main\res\drawable\ic_sunrise_colored.xml" qualifiers="" type="drawable"/><file name="ic_text_decrease" path="C:\Muslim Core\app\src\main\res\drawable\ic_text_decrease.xml" qualifiers="" type="drawable"/><file name="ic_text_format" path="C:\Muslim Core\app\src\main\res\drawable\ic_text_format.xml" qualifiers="" type="drawable"/><file name="ic_text_increase" path="C:\Muslim Core\app\src\main\res\drawable\ic_text_increase.xml" qualifiers="" type="drawable"/><file name="ic_track" path="C:\Muslim Core\app\src\main\res\drawable\ic_track.xml" qualifiers="" type="drawable"/><file name="ic_translate" path="C:\Muslim Core\app\src\main\res\drawable\ic_translate.xml" qualifiers="" type="drawable"/><file name="ic_volume_up" path="C:\Muslim Core\app\src\main\res\drawable\ic_volume_up.xml" qualifiers="" type="drawable"/><file name="info_button_selector" path="C:\Muslim Core\app\src\main\res\drawable\info_button_selector.xml" qualifiers="" type="drawable"/><file name="islamic_ornament" path="C:\Muslim Core\app\src\main\res\drawable\islamic_ornament.png" qualifiers="" type="drawable"/><file name="mosque_background" path="C:\Muslim Core\app\src\main\res\drawable\mosque_background.xml" qualifiers="" type="drawable"/><file name="mosque_background_light" path="C:\Muslim Core\app\src\main\res\drawable\mosque_background_light.xml" qualifiers="" type="drawable"/><file name="prayer_header_gradient" path="C:\Muslim Core\app\src\main\res\drawable\prayer_header_gradient.xml" qualifiers="" type="drawable"/><file name="prayer_icon" path="C:\Muslim Core\app\src\main\res\drawable\prayer_icon.xml" qualifiers="" type="drawable"/><file name="prayer_icon_filled" path="C:\Muslim Core\app\src\main\res\drawable\prayer_icon_filled.xml" qualifiers="" type="drawable"/><file name="prayer_icon_selector" path="C:\Muslim Core\app\src\main\res\drawable\prayer_icon_selector.xml" qualifiers="" type="drawable"/><file name="prayer_item_background" path="C:\Muslim Core\app\src\main\res\drawable\prayer_item_background.xml" qualifiers="" type="drawable"/><file name="quran_ayah_circle" path="C:\Muslim Core\app\src\main\res\drawable\quran_ayah_circle.xml" qualifiers="" type="drawable"/><file name="scroll_track_border" path="C:\Muslim Core\app\src\main\res\drawable\scroll_track_border.xml" qualifiers="" type="drawable"/><file name="search_background" path="C:\Muslim Core\app\src\main\res\drawable\search_background.xml" qualifiers="" type="drawable"/><file name="status_icon_background" path="C:\Muslim Core\app\src\main\res\drawable\status_icon_background.xml" qualifiers="" type="drawable"/><file name="status_icon_background_gray" path="C:\Muslim Core\app\src\main\res\drawable\status_icon_background_gray.xml" qualifiers="" type="drawable"/><file name="tab_selected_background" path="C:\Muslim Core\app\src\main\res\drawable\tab_selected_background.xml" qualifiers="" type="drawable"/><file name="theme_button_selector" path="C:\Muslim Core\app\src\main\res\drawable\theme_button_selector.xml" qualifiers="" type="drawable"/><file name="verse_reference_background" path="C:\Muslim Core\app\src\main\res\drawable\verse_reference_background.xml" qualifiers="" type="drawable"/><file name="pdms_saleem_quran" path="C:\Muslim Core\app\src\main\res\font\pdms_saleem_quran.ttf" qualifiers="" type="font"/><file name="roboto_bold" path="C:\Muslim Core\app\src\main\res\font\roboto_bold.xml" qualifiers="" type="font"/><file name="roboto_medium" path="C:\Muslim Core\app\src\main\res\font\roboto_medium.xml" qualifiers="" type="font"/><file name="roboto_regular" path="C:\Muslim Core\app\src\main\res\font\roboto_regular.xml" qualifiers="" type="font"/><file name="uthmani" path="C:\Muslim Core\app\src\main\res\font\uthmani.xml" qualifiers="" type="font"/><file name="activity_main" path="C:\Muslim Core\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_prayer_alert" path="C:\Muslim Core\app\src\main\res\layout\activity_prayer_alert.xml" qualifiers="" type="layout"/><file name="dialog_font_settings" path="C:\Muslim Core\app\src\main\res\layout\dialog_font_settings.xml" qualifiers="" type="layout"/><file name="dialog_location_picker" path="C:\Muslim Core\app\src\main\res\layout\dialog_location_picker.xml" qualifiers="" type="layout"/><file name="fragment_adhkar" path="C:\Muslim Core\app\src\main\res\layout\fragment_adhkar.xml" qualifiers="" type="layout"/><file name="fragment_dua_category" path="C:\Muslim Core\app\src\main\res\layout\fragment_dua_category.xml" qualifiers="" type="layout"/><file name="fragment_location_picker" path="C:\Muslim Core\app\src\main\res\layout\fragment_location_picker.xml" qualifiers="" type="layout"/><file name="fragment_notification_settings" path="C:\Muslim Core\app\src\main\res\layout\fragment_notification_settings.xml" qualifiers="" type="layout"/><file name="fragment_prayer" path="C:\Muslim Core\app\src\main\res\layout\fragment_prayer.xml" qualifiers="" type="layout"/><file name="fragment_prayer_tracker" path="C:\Muslim Core\app\src\main\res\layout\fragment_prayer_tracker.xml" qualifiers="" type="layout"/><file name="fragment_quran" path="C:\Muslim Core\app\src\main\res\layout\fragment_quran.xml" qualifiers="" type="layout"/><file name="fragment_settings" path="C:\Muslim Core\app\src\main\res\layout\fragment_settings.xml" qualifiers="" type="layout"/><file name="fragment_surah_detail" path="C:\Muslim Core\app\src\main\res\layout\fragment_surah_detail.xml" qualifiers="" type="layout"/><file name="fragment_tajweed_rules" path="C:\Muslim Core\app\src\main\res\layout\fragment_tajweed_rules.xml" qualifiers="" type="layout"/><file name="fragment_tajweed_settings" path="C:\Muslim Core\app\src\main\res\layout\fragment_tajweed_settings.xml" qualifiers="" type="layout"/><file name="item_ayah" path="C:\Muslim Core\app\src\main\res\layout\item_ayah.xml" qualifiers="" type="layout"/><file name="item_calendar_day" path="C:\Muslim Core\app\src\main\res\layout\item_calendar_day.xml" qualifiers="" type="layout"/><file name="item_dua" path="C:\Muslim Core\app\src\main\res\layout\item_dua.xml" qualifiers="" type="layout"/><file name="item_location" path="C:\Muslim Core\app\src\main\res\layout\item_location.xml" qualifiers="" type="layout"/><file name="item_prayer_time" path="C:\Muslim Core\app\src\main\res\layout\item_prayer_time.xml" qualifiers="" type="layout"/><file name="item_prayer_tracker" path="C:\Muslim Core\app\src\main\res\layout\item_prayer_tracker.xml" qualifiers="" type="layout"/><file name="item_surah" path="C:\Muslim Core\app\src\main\res\layout\item_surah.xml" qualifiers="" type="layout"/><file name="item_tajweed_legend" path="C:\Muslim Core\app\src\main\res\layout\item_tajweed_legend.xml" qualifiers="" type="layout"/><file name="bottom_navigation_menu" path="C:\Muslim Core\app\src\main\res\menu\bottom_navigation_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Muslim Core\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Muslim Core\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file path="C:\Muslim Core\app\src\main\res\values\colors.xml" qualifiers=""><color name="islamic_green_primary">#0E7C4B</color><color name="islamic_green_dark">#0A5A36</color><color name="islamic_green_light">#4CAF50</color><color name="islamic_green_lighter">#81C784</color><color name="islamic_green_lightest">#C8E6C9</color><color name="islamic_gold">#D4AF37</color><color name="islamic_gold_dark">#B8941F</color><color name="islamic_gold_light">#F4E4A6</color><color name="background_primary">#FAFAFA</color><color name="background_secondary">#F5F5F5</color><color name="background_card">#FFFFFF</color><color name="background_gradient_start">#FFFFFF</color><color name="background_gradient_end">#F8F9FA</color><color name="background_primary_dark">#121212</color><color name="background_secondary_dark">#1E1E1E</color><color name="background_card_dark">#2D2D2D</color><color name="background_gradient_start_dark">#1A1A1A</color><color name="background_gradient_end_dark">#2C2C2C</color><color name="text_primary">#212121</color><color name="text_secondary">#757575</color><color name="text_tertiary">#9E9E9E</color><color name="text_disabled">#BDBDBD</color><color name="text_on_primary">#FFFFFF</color><color name="text_arabic">#1B5E20</color><color name="text_primary_dark">#FFFFFF</color><color name="text_secondary_dark">#B3B3B3</color><color name="text_tertiary_dark">#808080</color><color name="text_arabic_dark">#81C784</color><color name="prayer_fajr">#4A90E2</color><color name="prayer_dhuhr">#F5A623</color><color name="prayer_asr">#F39C12</color><color name="prayer_maghrib">#E74C3C</color><color name="prayer_isha">#8E44AD</color><color name="success">#4CAF50</color><color name="warning">#FF9800</color><color name="error">#F44336</color><color name="info">#2196F3</color><color name="accent_blue">#1976D2</color><color name="accent_purple">#7B1FA2</color><color name="accent_teal">#00796B</color><color name="white">#FFFFFF</color><color name="black">#000000</color><color name="transparent">#00000000</color><color name="semi_transparent">#80000000</color><color name="divider">#E0E0E0</color><color name="divider_dark">#424242</color><color name="shadow_light">#1A000000</color><color name="shadow_medium">#33000000</color><color name="shadow_dark">#4D000000</color><color name="qibla_needle">#D32F2F</color><color name="qibla_background">#E8F5E8</color><color name="qibla_circle">#0E7C4B</color><color name="progress_background">#E0E0E0</color><color name="progress_completed">#4CAF50</color><color name="progress_partial">#FF9800</color><color name="notification_background">#0E7C4B</color><color name="notification_text">#FFFFFF</color><color name="ripple_light">#20000000</color><color name="ripple_dark">#40FFFFFF</color><color name="dark_green_primary">#0E7C4B</color><color name="dark_green_background">#1A1A1A</color><color name="dark_green_card">#2A2A2A</color><color name="dark_green_text_primary">#FFFFFF</color><color name="dark_green_text_secondary">#CCFFFFFF</color><color name="dark_green_bottom_nav_bg">#1A1A1A</color><color name="dark_green_bottom_nav_selected">#0E7C4B</color><color name="dark_green_bottom_nav_unselected">#666666</color><color name="light_yellow_primary">#F9A825</color><color name="light_yellow_background">#FFFEF7</color><color name="light_yellow_card">#FFFFFF</color><color name="light_yellow_text_primary">#333333</color><color name="light_yellow_text_secondary">#666666</color><color name="light_yellow_bottom_nav_bg">#FFFFFF</color><color name="light_yellow_bottom_nav_selected">#F9A825</color><color name="light_yellow_bottom_nav_unselected">#999999</color><color name="quran_accent">#D4AF37</color><color name="quran_text_arabic">#FFFFFF</color><color name="quran_text_translation">#CCFFFFFF</color><color name="quran_text_transliteration">#99FFFFFF</color><color name="quran_background">#1A1A1A</color><color name="quran_card_background">#2A2A2A</color><color name="theme_current_dark_bg">#2A2A2A</color><color name="theme_current_dark_text">#FFFFFF</color><color name="theme_sepia_bg">#F4F1E8</color><color name="theme_sepia_text">#3A3A3A</color><color name="theme_light_bg">#FFFFFF</color><color name="theme_light_text">#2A2A2A</color><color name="theme_black_bg">#000000</color><color name="theme_black_text">#FFFFFF</color><color name="theme_dark_blue_bg">#1A1A2E</color><color name="theme_dark_blue_text">#FFFFFF</color></file><file path="C:\Muslim Core\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Muslim Core</string><string name="app_name_arabic">المسلم الأساسي</string><string name="nav_prayer">Prayer</string><string name="nav_prayer_arabic">الصلاة</string><string name="nav_quran">Quran</string><string name="nav_quran_arabic">القرآن</string><string name="nav_adhkar">Adhkar</string><string name="nav_adhkar_arabic">الأذكار</string><string name="nav_settings">Settings</string><string name="nav_settings_arabic">الإعدادات</string><string name="prayer_fajr">Fajr</string><string name="prayer_fajr_arabic">الفجر</string><string name="prayer_dhuhr">Dhuhr</string><string name="prayer_dhuhr_arabic">الظهر</string><string name="prayer_asr">Asr</string><string name="prayer_asr_arabic">العصر</string><string name="prayer_maghrib">Maghrib</string><string name="prayer_maghrib_arabic">المغرب</string><string name="prayer_isha">Isha</string><string name="prayer_isha_arabic">العشاء</string><string name="prayer_sunrise">Sunrise</string><string name="prayer_sunrise_arabic">الشروق</string><string name="prayer_completed">Prayer Completed</string><string name="prayer_missed">Prayer Missed</string><string name="prayer_pending">Prayer Pending</string><string name="track_prayers">Track My Prayers</string><string name="next_prayer">Next Prayer</string><string name="current_prayer">Current Prayer</string><string name="time_remaining">Time Remaining</string><string name="surah">Surah</string><string name="surah_arabic">سورة</string><string name="ayah">Ayah</string><string name="ayah_arabic">آية</string><string name="juz">Juz</string><string name="juz_arabic">جزء</string><string name="page">Page</string><string name="page_arabic">صفحة</string><string name="bookmark">Bookmark</string><string name="bookmarks">Bookmarks</string><string name="continue_reading">Continue Reading</string><string name="khatma_planner">Khatma Planner</string><string name="tajweed_rules">Tajweed Rules</string><string name="adhkar_morning">Morning Adhkar</string><string name="adhkar_morning_arabic">أذكار الصباح</string><string name="adhkar_evening">Evening Adhkar</string><string name="adhkar_evening_arabic">أذكار المساء</string><string name="adhkar_after_prayer">After Prayer</string><string name="adhkar_after_prayer_arabic">أذكار بعد الصلاة</string><string name="adhkar_sleep">Sleep Adhkar</string><string name="adhkar_sleep_arabic">أذكار النوم</string><string name="adhkar_protection">Protection</string><string name="adhkar_protection_arabic">أذكار الحماية</string><string name="tasbeeh">Tasbeeh</string><string name="tasbeeh_arabic">التسبيح</string><string name="tasbeeh_counter">Tasbeeh Counter</string><string name="count">Count</string><string name="reset">Reset</string><string name="target_reached">Target Reached!</string><string name="qibla">Qibla</string><string name="qibla_arabic">القبلة</string><string name="qibla_direction">Qibla Direction</string><string name="distance_to_kaaba">Distance to Kaaba</string><string name="calibrate_compass">Calibrate Compass</string><string name="language">Language</string><string name="theme">Theme</string><string name="notifications">Notifications</string><string name="location">Location</string><string name="calculation_method">Calculation Method</string><string name="madhab">Madhab</string><string name="privacy_policy">Privacy Policy</string><string name="terms_of_service">Terms of Service</string><string name="feedback">Send Feedback</string><string name="about">About</string><string name="theme_light">Light</string><string name="theme_dark">Dark</string><string name="theme_auto">Auto</string><string name="language_english">English</string><string name="language_arabic">العربية</string><string name="save">Save</string><string name="cancel">Cancel</string><string name="ok">OK</string><string name="yes">Yes</string><string name="no">No</string><string name="done">Done</string><string name="edit">Edit</string><string name="delete">Delete</string><string name="share">Share</string><string name="search">Search</string><string name="filter">Filter</string><string name="sort">Sort</string><string name="refresh">Refresh</string><string name="loading">Loading…</string><string name="retry">Retry</string><string name="error_network">Network error. Please check your connection.</string><string name="error_location">Unable to get location. Please enable location services.</string><string name="error_permission">Permission required to continue.</string><string name="error_general">Something went wrong. Please try again.</string><string name="success_prayer_logged">Prayer logged successfully</string><string name="success_bookmark_added">Bookmark added</string><string name="success_bookmark_removed">Bookmark removed</string><string name="success_settings_saved">Settings saved</string><string name="notification_prayer_time">It\'s time for %s prayer</string><string name="notification_adhkar_reminder">Time for your daily adhkar</string><string name="notification_quran_reminder">Don\'t forget to read Quran today</string><string name="permission_location_title">Location Permission</string><string name="permission_location_message">This app needs location access to provide accurate prayer times and Qibla direction.</string><string name="permission_notification_title">Notification Permission</string><string name="permission_notification_message">Allow notifications to receive prayer time reminders and adhkar alerts.</string><string name="empty_bookmarks">No bookmarks yet</string><string name="empty_prayer_logs">No prayer logs found</string><string name="empty_search_results">No results found</string><string name="time_format_12h">h:mm a</string><string name="time_format_24h">HH:mm</string><string name="date_format">MMM dd, yyyy</string><string name="hijri_date_format">dd MMM yyyy AH</string><string name="bismillah">بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيم</string><string name="alhamdulillah">الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِين</string><string name="subhanallah">سُبْحَانَ اللَّه</string><string name="allahu_akbar">اللَّهُ أَكْبَر</string><string name="la_ilaha_illa_allah">لَا إِلَهَ إِلَّا اللَّه</string><string name="cd_prayer_status">Prayer status</string><string name="cd_bookmark_button">Bookmark this ayah</string><string name="cd_play_audio">Play audio</string><string name="cd_qibla_compass">Qibla compass</string><string name="cd_tasbeeh_counter">Tasbeeh counter</string><string name="cd_navigation_menu">Navigation menu</string></file><file path="C:\Muslim Core\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.MuslimCore" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/islamic_green_primary</item>
        <item name="colorOnPrimary">@color/text_on_primary</item>
        <item name="colorPrimaryContainer">@color/islamic_green_lightest</item>
        <item name="colorOnPrimaryContainer">@color/islamic_green_dark</item>

        
        <item name="colorSecondary">@color/islamic_gold</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSecondaryContainer">@color/islamic_gold_light</item>
        <item name="colorOnSecondaryContainer">@color/islamic_gold_dark</item>

        
        <item name="colorTertiary">@color/accent_teal</item>
        <item name="colorOnTertiary">@color/white</item>
        <item name="colorTertiaryContainer">@color/islamic_green_lighter</item>
        <item name="colorOnTertiaryContainer">@color/islamic_green_dark</item>

        
        <item name="android:colorBackground">@color/background_primary</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorSurface">@color/background_card</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="colorSurfaceVariant">@color/background_secondary</item>
        <item name="colorOnSurfaceVariant">@color/text_secondary</item>

        
        <item name="colorError">@color/error</item>
        <item name="colorOnError">@color/white</item>
        <item name="colorErrorContainer">@color/error</item>
        <item name="colorOnErrorContainer">@color/white</item>

        
        <item name="android:statusBarColor">@color/islamic_green_primary</item>
        <item name="android:windowLightStatusBar">false</item>

        
        <item name="android:navigationBarColor">@color/background_primary</item>
        <item name="android:windowLightNavigationBar">true</item>

        
        <item name="android:windowBackground">@color/background_primary</item>

        
        <item name="textAppearanceHeadline1">@style/TextAppearance.MuslimCore.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MuslimCore.Headline2</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MuslimCore.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MuslimCore.Body2</item>

        
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.MuslimCore.SmallComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.MuslimCore.MediumComponent</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.MuslimCore.LargeComponent</item>
    </style><style name="Theme.MuslimCore.NoActionBar" parent="Theme.MuslimCore">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style><style name="Theme.MuslimCore.Splash" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/islamic_green_primary</item>
        <item name="windowSplashScreenAnimatedIcon">@drawable/ic_launcher_foreground</item>
        <item name="windowSplashScreenAnimationDuration">1000</item>
        <item name="postSplashScreenTheme">@style/Theme.MuslimCore.NoActionBar</item>
    </style><style name="TextAppearance.MuslimCore.Headline1" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
        <item name="android:textSize">32sp</item>
    </style><style name="TextAppearance.MuslimCore.Headline2" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="android:textSize">24sp</item>
    </style><style name="TextAppearance.MuslimCore.Body1" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
        <item name="android:textSize">16sp</item>
    </style><style name="TextAppearance.MuslimCore.Body2" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
        <item name="android:textSize">14sp</item>
    </style><style name="TextAppearance.MuslimCore.Arabic" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/text_arabic</item>
        <item name="android:fontFamily">@font/uthmani</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textDirection">rtl</item>
        <item name="android:gravity">center</item>
        <item name="android:lineSpacingExtra">8dp</item>
    </style><style name="TextAppearance.MuslimCore.Arabic.Large" parent="TextAppearance.MuslimCore.Arabic">
        <item name="android:textSize">24sp</item>
        <item name="android:lineSpacingExtra">12dp</item>
    </style><style name="ShapeAppearance.MuslimCore.SmallComponent" parent="ShapeAppearance.Material3.Corner.Small">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style><style name="ShapeAppearance.MuslimCore.MediumComponent" parent="ShapeAppearance.Material3.Corner.Medium">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">12dp</item>
    </style><style name="ShapeAppearance.MuslimCore.LargeComponent" parent="ShapeAppearance.Material3.Corner.Large">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style><style name="Widget.MuslimCore.Button" parent="Widget.Material3.Button">
        <item name="android:textColor">@color/text_on_primary</item>
        <item name="backgroundTint">@color/islamic_green_primary</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
    </style><style name="Widget.MuslimCore.Button.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textColor">@color/islamic_green_primary</item>
        <item name="strokeColor">@color/islamic_green_primary</item>
        <item name="cornerRadius">12dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
    </style><style name="Widget.MuslimCore.Card" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/background_card</item>
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style><style name="Widget.MuslimCore.Card.Prayer" parent="Widget.MuslimCore.Card">
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">6dp</item>
        <item name="android:layout_margin">12dp</item>
    </style><style name="Widget.MuslimCore.BottomNavigationView" parent="Widget.Material3.BottomNavigationView">
        <item name="android:background">@color/background_card</item>
        <item name="itemIconTint">@color/bottom_nav_color</item>
        <item name="itemTextColor">@color/bottom_nav_color</item>
        <item name="elevation">8dp</item>
        <item name="itemActiveIndicatorStyle">@null</item>
    </style><style name="TextAppearance.MuslimCore.BottomNav.Active" parent="TextAppearance.Material3.LabelMedium">
        <item name="android:textColor">@color/islamic_green_primary</item>
        <item name="android:textSize">12sp</item>
    </style><style name="TextAppearance.MuslimCore.BottomNav.Inactive" parent="TextAppearance.Material3.LabelMedium">
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:textSize">12sp</item>
    </style><style name="Widget.MuslimCore.Toolbar" parent="Widget.Material3.Toolbar">
        <item name="android:background">@color/islamic_green_primary</item>
        <item name="titleTextColor">@color/text_on_primary</item>
        <item name="subtitleTextColor">@color/text_on_primary</item>
        <item name="android:elevation">4dp</item>
    </style></file><file name="backup_rules" path="C:\Muslim Core\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Muslim Core\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="hands_icon" path="C:\Muslim Core\app\src\main\res\drawable\hands_icon.xml" qualifiers="" type="drawable"/><file name="quran_icon" path="C:\Muslim Core\app\src\main\res\drawable\quran_icon.xml" qualifiers="" type="drawable"/><file name="hands_icon_filled" path="C:\Muslim Core\app\src\main\res\drawable\hands_icon_filled.xml" qualifiers="" type="drawable"/><file name="hands_icon_selector" path="C:\Muslim Core\app\src\main\res\drawable\hands_icon_selector.xml" qualifiers="" type="drawable"/><file name="quran_icon_filled" path="C:\Muslim Core\app\src\main\res\drawable\quran_icon_filled.xml" qualifiers="" type="drawable"/><file name="quran_icon_selector" path="C:\Muslim Core\app\src\main\res\drawable\quran_icon_selector.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Muslim Core\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Muslim Core\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Muslim Core\app\build\generated\res\resValues\debug"/><source path="C:\Muslim Core\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Muslim Core\app\build\generated\res\resValues\debug"/><source path="C:\Muslim Core\app\build\generated\res\processDebugGoogleServices"><file path="C:\Muslim Core\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">835725373985</string><string name="google_api_key" translatable="false">AIzaSyAk64ViXdhp4xwnqMpGBYxXO5cABH570GE</string><string name="google_app_id" translatable="false">1:835725373985:android:9d03bfc077e932fc472960</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyAk64ViXdhp4xwnqMpGBYxXO5cABH570GE</string><string name="google_storage_bucket" translatable="false">rey-lingo.firebasestorage.app</string><string name="project_id" translatable="false">rey-lingo</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>