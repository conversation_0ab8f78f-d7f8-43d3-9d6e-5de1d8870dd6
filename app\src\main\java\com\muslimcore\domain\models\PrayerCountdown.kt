package com.muslimcore.domain.models

/**
 * Data class representing prayer countdown information
 */
data class PrayerCountdown(
    val currentPrayerName: String,
    val currentPrayerTime: String,
    val nextPrayerName: String,
    val nextPrayerTime: String,
    val timeUntilNext: String, // Format: "03:06:05"
    val timeUntilNextMillis: Long,
    val isLastPrayerOfDay: Boolean = false
) {
    
    /**
     * Check if countdown is within the last hour
     */
    fun isWithinLastHour(): Boolean {
        return timeUntilNextMillis <= 3600000L // 1 hour in milliseconds
    }
    
    /**
     * Check if countdown is within the last 10 minutes
     */
    fun isWithinLastTenMinutes(): <PERSON><PERSON><PERSON> {
        return timeUntilNextMillis <= 600000L // 10 minutes in milliseconds
    }
    
    /**
     * Get formatted time for notification
     */
    fun getNotificationText(): String {
        return "Next Prayer: $nextPrayerName $nextPrayerTime in $timeUntilNext"
    }
    
    /**
     * Get short format for UI
     */
    fun getShortFormat(): String {
        return "Next prayer in $timeUntilNext"
    }
}
