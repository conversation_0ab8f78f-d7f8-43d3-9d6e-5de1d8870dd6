package com.muslimcore.domain.usecases.quran;

import com.muslimcore.domain.repositories.QuranRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetSurahUseCase_Factory implements Factory<GetSurahUseCase> {
  private final Provider<QuranRepository> quranRepositoryProvider;

  public GetSurahUseCase_Factory(Provider<QuranRepository> quranRepositoryProvider) {
    this.quranRepositoryProvider = quranRepositoryProvider;
  }

  @Override
  public GetSurahUseCase get() {
    return newInstance(quranRepositoryProvider.get());
  }

  public static GetSurahUseCase_Factory create(Provider<QuranRepository> quranRepositoryProvider) {
    return new GetSurahUseCase_Factory(quranRepositoryProvider);
  }

  public static GetSurahUseCase newInstance(QuranRepository quranRepository) {
    return new GetSurahUseCase(quranRepository);
  }
}
