package com.muslimcore.presentation.viewmodels;

import com.muslimcore.domain.repositories.PrayerRepository;
import com.muslimcore.domain.repositories.UserRepository;
import com.muslimcore.domain.usecases.prayer.GetPrayerTimesUseCase;
import com.muslimcore.domain.usecases.prayer.LogPrayerUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PrayerViewModel_Factory implements Factory<PrayerViewModel> {
  private final Provider<GetPrayerTimesUseCase> getPrayerTimesUseCaseProvider;

  private final Provider<LogPrayerUseCase> logPrayerUseCaseProvider;

  private final Provider<PrayerRepository> prayerRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  public PrayerViewModel_Factory(Provider<GetPrayerTimesUseCase> getPrayerTimesUseCaseProvider,
      Provider<LogPrayerUseCase> logPrayerUseCaseProvider,
      Provider<PrayerRepository> prayerRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    this.getPrayerTimesUseCaseProvider = getPrayerTimesUseCaseProvider;
    this.logPrayerUseCaseProvider = logPrayerUseCaseProvider;
    this.prayerRepositoryProvider = prayerRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public PrayerViewModel get() {
    return newInstance(getPrayerTimesUseCaseProvider.get(), logPrayerUseCaseProvider.get(), prayerRepositoryProvider.get(), userRepositoryProvider.get());
  }

  public static PrayerViewModel_Factory create(
      Provider<GetPrayerTimesUseCase> getPrayerTimesUseCaseProvider,
      Provider<LogPrayerUseCase> logPrayerUseCaseProvider,
      Provider<PrayerRepository> prayerRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    return new PrayerViewModel_Factory(getPrayerTimesUseCaseProvider, logPrayerUseCaseProvider, prayerRepositoryProvider, userRepositoryProvider);
  }

  public static PrayerViewModel newInstance(GetPrayerTimesUseCase getPrayerTimesUseCase,
      LogPrayerUseCase logPrayerUseCase, PrayerRepository prayerRepository,
      UserRepository userRepository) {
    return new PrayerViewModel(getPrayerTimesUseCase, logPrayerUseCase, prayerRepository, userRepository);
  }
}
