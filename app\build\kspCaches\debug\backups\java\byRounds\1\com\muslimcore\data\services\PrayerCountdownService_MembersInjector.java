package com.muslimcore.data.services;

import com.muslimcore.data.local.managers.PrayerTimeManager;
import com.muslimcore.domain.repositories.LocationRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PrayerCountdownService_MembersInjector implements MembersInjector<PrayerCountdownService> {
  private final Provider<PrayerTimeManager> prayerTimeManagerProvider;

  private final Provider<LocationRepository> locationRepositoryProvider;

  public PrayerCountdownService_MembersInjector(
      Provider<PrayerTimeManager> prayerTimeManagerProvider,
      Provider<LocationRepository> locationRepositoryProvider) {
    this.prayerTimeManagerProvider = prayerTimeManagerProvider;
    this.locationRepositoryProvider = locationRepositoryProvider;
  }

  public static MembersInjector<PrayerCountdownService> create(
      Provider<PrayerTimeManager> prayerTimeManagerProvider,
      Provider<LocationRepository> locationRepositoryProvider) {
    return new PrayerCountdownService_MembersInjector(prayerTimeManagerProvider, locationRepositoryProvider);
  }

  @Override
  public void injectMembers(PrayerCountdownService instance) {
    injectPrayerTimeManager(instance, prayerTimeManagerProvider.get());
    injectLocationRepository(instance, locationRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.muslimcore.data.services.PrayerCountdownService.prayerTimeManager")
  public static void injectPrayerTimeManager(PrayerCountdownService instance,
      PrayerTimeManager prayerTimeManager) {
    instance.prayerTimeManager = prayerTimeManager;
  }

  @InjectedFieldSignature("com.muslimcore.data.services.PrayerCountdownService.locationRepository")
  public static void injectLocationRepository(PrayerCountdownService instance,
      LocationRepository locationRepository) {
    instance.locationRepository = locationRepository;
  }
}
