package com.muslimcore.presentation.services

import android.app.*
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import androidx.lifecycle.lifecycleScope
import com.muslimcore.R
import com.muslimcore.data.local.managers.LocationManager
import com.muslimcore.data.local.managers.PrayerTimeManager
import com.muslimcore.presentation.activities.PrayerAlertActivity
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject

@AndroidEntryPoint
class PrayerService : Service() {

    @Inject
    lateinit var locationManager: LocationManager
    
    @Inject
    lateinit var prayerTimeManager: PrayerTimeManager
    
    private lateinit var notificationPrefs: SharedPreferences
    private var serviceJob: Job? = null
    private var countdownJob: Job? = null
    
    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "prayer_service_channel"
        private const val COUNTDOWN_NOTIFICATION_ID = 1002
        private const val COUNTDOWN_CHANNEL_ID = "prayer_countdown_channel"
        
        fun startService(context: Context) {
            val intent = Intent(context, PrayerService::class.java)
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        
        fun stopService(context: Context) {
            val intent = Intent(context, PrayerService::class.java)
            context.stopService(intent)
        }
    }

    override fun onCreate() {
        super.onCreate()
        notificationPrefs = getSharedPreferences("notification_settings", Context.MODE_PRIVATE)
        createNotificationChannels()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        startForegroundService()
        startPrayerMonitoring()
        return START_STICKY // Restart if killed
    }

    override fun onBind(intent: Intent?): IBinder? = null

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            
            // Service channel
            val serviceChannel = NotificationChannel(
                CHANNEL_ID,
                "Prayer Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Background service for prayer notifications"
                setShowBadge(false)
            }
            
            // Countdown channel
            val countdownChannel = NotificationChannel(
                COUNTDOWN_CHANNEL_ID,
                "Prayer Countdown",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Prayer countdown notifications"
                setShowBadge(false)
            }
            
            notificationManager.createNotificationChannel(serviceChannel)
            notificationManager.createNotificationChannel(countdownChannel)
        }
    }

    private fun startForegroundService() {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Prayer Service")
            .setContentText("Monitoring prayer times")
            .setSmallIcon(R.drawable.prayer_icon)
            .setOngoing(true)
            .setShowWhen(false)
            .build()

        startForeground(NOTIFICATION_ID, notification)
    }

    private fun startPrayerMonitoring() {
        serviceJob = CoroutineScope(Dispatchers.Default).launch {
            while (isActive) {
                try {
                    val location = locationManager.getCurrentLocation()
                    if (location != null) {
                        val prayerTimes = prayerTimeManager.calculatePrayerTimes(
                            location.latitude,
                            location.longitude
                        )
                        
                        // Check for prayer time alerts
                        checkPrayerTimeAlerts(prayerTimes)
                        
                        // Update countdown notification if enabled
                        updateCountdownNotification(prayerTimes)
                    }
                    
                    // Wait 30 seconds before next check
                    delay(30000)
                    
                } catch (e: Exception) {
                    // Handle errors gracefully
                    delay(60000) // Wait longer on error
                }
            }
        }
    }

    private fun checkPrayerTimeAlerts(prayerTimes: PrayerTimeManager.PrayerTimes) {
        val prayerAlertsEnabled = notificationPrefs.getBoolean("prayer_alerts_enabled", true)
        if (!prayerAlertsEnabled) return
        
        val currentPrayer = prayerTimeManager.isPrayerTime(prayerTimes)
        if (currentPrayer != null) {
            // Show prayer alert
            showPrayerAlert(currentPrayer)
        }
    }

    private fun updateCountdownNotification(prayerTimes: PrayerTimeManager.PrayerTimes) {
        val countdownEnabled = notificationPrefs.getBoolean("countdown_enabled", false)
        if (!countdownEnabled) {
            // Remove countdown notification if disabled
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancel(COUNTDOWN_NOTIFICATION_ID)
            return
        }
        
        // Start countdown updates
        startCountdownUpdates(prayerTimes)
    }

    private fun startCountdownUpdates(prayerTimes: PrayerTimeManager.PrayerTimes) {
        countdownJob?.cancel()
        countdownJob = CoroutineScope(Dispatchers.Default).launch {
            while (isActive) {
                try {
                    val nextPrayer = prayerTimeManager.getNextPrayer(prayerTimes)
                    val timeUntil = prayerTimeManager.formatTimeUntil(nextPrayer.timeUntil)
                    
                    showCountdownNotification(nextPrayer.name, timeUntil)
                    
                    // Update every second
                    delay(1000)
                    
                } catch (e: Exception) {
                    delay(5000) // Wait on error
                }
            }
        }
    }

    private fun showCountdownNotification(prayerName: String, timeUntil: String) {
        val notification = NotificationCompat.Builder(this, COUNTDOWN_CHANNEL_ID)
            .setContentTitle("Next Prayer: $prayerName")
            .setContentText("in $timeUntil")
            .setSmallIcon(R.drawable.prayer_icon)
            .setOngoing(true)
            .setShowWhen(false)
            .setOnlyAlertOnce(true)
            .build()

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(COUNTDOWN_NOTIFICATION_ID, notification)
    }

    private fun showPrayerAlert(prayer: PrayerTimeManager.Prayer) {
        val fullscreenEnabled = notificationPrefs.getBoolean("fullscreen_alert_enabled", true)

        if (fullscreenEnabled) {
            // Show full-screen alert
            val prayerName = prayer.name
            val timeFormat = SimpleDateFormat("h:mm a", Locale.getDefault())
            val currentTime = timeFormat.format(Date())

            val intent = PrayerAlertActivity.createIntent(this, prayerName, currentTime)
            startActivity(intent)
        } else {
            // Show regular notification
            showPrayerNotification(prayer)
        }
    }

    private fun showPrayerNotification(prayer: PrayerTimeManager.Prayer) {
        val notification = NotificationCompat.Builder(this, COUNTDOWN_CHANNEL_ID)
            .setContentTitle("Prayer Time: ${prayer.name}")
            .setContentText("It's time for ${prayer.name} prayer")
            .setSmallIcon(R.drawable.prayer_icon)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .build()

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(prayer.hashCode(), notification)
    }

    override fun onDestroy() {
        super.onDestroy()
        serviceJob?.cancel()
        countdownJob?.cancel()
    }
}
