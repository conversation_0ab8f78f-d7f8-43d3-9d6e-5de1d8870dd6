// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.muslimcore;

import android.app.Activity;
import android.app.Service;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.auth.FirebaseAuth;
import com.google.gson.Gson;
import com.muslimcore.data.local.PreferencesManager;
import com.muslimcore.data.local.dao.PrayerDao;
import com.muslimcore.data.local.database.MuslimCoreDatabase;
import com.muslimcore.data.local.datasource.QuranAssetsDataSource;
import com.muslimcore.data.local.managers.LocationManager;
import com.muslimcore.data.local.managers.PrayerTimeManager;
import com.muslimcore.data.remote.api.PrayerTimesApi;
import com.muslimcore.data.repositories.LocationRepositoryImpl;
import com.muslimcore.data.repositories.PrayerRepositoryImpl;
import com.muslimcore.data.repositories.QuranAssetsRepository;
import com.muslimcore.data.repositories.UserRepositoryImpl;
import com.muslimcore.di.DatabaseModule;
import com.muslimcore.di.DatabaseModule_ProvideMuslimCoreDatabaseFactory;
import com.muslimcore.di.DatabaseModule_ProvidePrayerDaoFactory;
import com.muslimcore.di.FirebaseModule;
import com.muslimcore.di.FirebaseModule_ProvideFirebaseAnalyticsFactory;
import com.muslimcore.di.FirebaseModule_ProvideFirebaseAuthFactory;
import com.muslimcore.di.NetworkModule;
import com.muslimcore.di.NetworkModule_ProvideGsonFactory;
import com.muslimcore.di.NetworkModule_ProvideOkHttpClientFactory;
import com.muslimcore.di.NetworkModule_ProvidePrayerTimesApiFactory;
import com.muslimcore.di.NetworkModule_ProvideRetrofitFactory;
import com.muslimcore.domain.usecases.GetCurrentLocationUseCase;
import com.muslimcore.domain.usecases.prayer.GetPrayerTimesUseCase;
import com.muslimcore.domain.usecases.prayer.LogPrayerUseCase;
import com.muslimcore.presentation.MainActivity;
import com.muslimcore.presentation.MainActivity_MembersInjector;
import com.muslimcore.presentation.fragments.AdhkarFragment;
import com.muslimcore.presentation.fragments.LocationPickerFragment;
import com.muslimcore.presentation.fragments.LocationPickerFragment_MembersInjector;
import com.muslimcore.presentation.fragments.NotificationSettingsFragment;
import com.muslimcore.presentation.fragments.PrayerFragment;
import com.muslimcore.presentation.fragments.PrayerFragment_MembersInjector;
import com.muslimcore.presentation.fragments.PrayerTrackerFragment;
import com.muslimcore.presentation.fragments.QuranFragment;
import com.muslimcore.presentation.fragments.SettingsFragment;
import com.muslimcore.presentation.fragments.SettingsFragment_MembersInjector;
import com.muslimcore.presentation.fragments.SurahDetailFragment;
import com.muslimcore.presentation.services.PrayerService;
import com.muslimcore.presentation.services.PrayerService_MembersInjector;
import com.muslimcore.presentation.utils.ThemeManager;
import com.muslimcore.presentation.viewmodels.MainViewModel;
import com.muslimcore.presentation.viewmodels.MainViewModel_HiltModules_KeyModule_ProvideFactory;
import com.muslimcore.presentation.viewmodels.PrayerLocationViewModel;
import com.muslimcore.presentation.viewmodels.PrayerLocationViewModel_HiltModules_KeyModule_ProvideFactory;
import com.muslimcore.presentation.viewmodels.PrayerViewModel;
import com.muslimcore.presentation.viewmodels.PrayerViewModel_HiltModules_KeyModule_ProvideFactory;
import com.muslimcore.presentation.viewmodels.QuranViewModel;
import com.muslimcore.presentation.viewmodels.QuranViewModel_HiltModules_KeyModule_ProvideFactory;
import com.muslimcore.presentation.viewmodels.SurahDetailViewModel;
import com.muslimcore.presentation.viewmodels.SurahDetailViewModel_HiltModules_KeyModule_ProvideFactory;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.flags.HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.Preconditions;
import java.util.Map;
import java.util.Set;
import javax.inject.Provider;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;

@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaggerMuslimCoreApplication_HiltComponents_SingletonC {
  private DaggerMuslimCoreApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private ApplicationContextModule applicationContextModule;

    private Builder() {
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder databaseModule(DatabaseModule databaseModule) {
      Preconditions.checkNotNull(databaseModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder firebaseModule(FirebaseModule firebaseModule) {
      Preconditions.checkNotNull(firebaseModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule(
        HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule) {
      Preconditions.checkNotNull(hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder networkModule(NetworkModule networkModule) {
      Preconditions.checkNotNull(networkModule);
      return this;
    }

    public MuslimCoreApplication_HiltComponents.SingletonC build() {
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      return new SingletonCImpl(applicationContextModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements MuslimCoreApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public MuslimCoreApplication_HiltComponents.ActivityRetainedC build() {
      return new ActivityRetainedCImpl(singletonCImpl);
    }
  }

  private static final class ActivityCBuilder implements MuslimCoreApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public MuslimCoreApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements MuslimCoreApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public MuslimCoreApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements MuslimCoreApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public MuslimCoreApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements MuslimCoreApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public MuslimCoreApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements MuslimCoreApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public MuslimCoreApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements MuslimCoreApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public MuslimCoreApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends MuslimCoreApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends MuslimCoreApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public void injectAdhkarFragment(AdhkarFragment adhkarFragment) {
    }

    @Override
    public void injectLocationPickerFragment(LocationPickerFragment locationPickerFragment) {
      injectLocationPickerFragment2(locationPickerFragment);
    }

    @Override
    public void injectNotificationSettingsFragment(
        NotificationSettingsFragment notificationSettingsFragment) {
    }

    @Override
    public void injectPrayerFragment(PrayerFragment prayerFragment) {
      injectPrayerFragment2(prayerFragment);
    }

    @Override
    public void injectPrayerTrackerFragment(PrayerTrackerFragment prayerTrackerFragment) {
    }

    @Override
    public void injectQuranFragment(QuranFragment quranFragment) {
    }

    @Override
    public void injectSettingsFragment(SettingsFragment settingsFragment) {
      injectSettingsFragment2(settingsFragment);
    }

    @Override
    public void injectSurahDetailFragment(SurahDetailFragment surahDetailFragment) {
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }

    @CanIgnoreReturnValue
    private LocationPickerFragment injectLocationPickerFragment2(LocationPickerFragment instance) {
      LocationPickerFragment_MembersInjector.injectLocationManager(instance, singletonCImpl.locationManagerProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private PrayerFragment injectPrayerFragment2(PrayerFragment instance) {
      PrayerFragment_MembersInjector.injectPrayerTimeManager(instance, singletonCImpl.prayerTimeManagerProvider.get());
      PrayerFragment_MembersInjector.injectThemeManager(instance, singletonCImpl.themeManagerProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private SettingsFragment injectSettingsFragment2(SettingsFragment instance) {
      SettingsFragment_MembersInjector.injectPreferencesManager(instance, singletonCImpl.preferencesManagerProvider.get());
      return instance;
    }
  }

  private static final class ViewCImpl extends MuslimCoreApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends MuslimCoreApplication_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectMainActivity(MainActivity mainActivity) {
      injectMainActivity2(mainActivity);
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(getViewModelKeys(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Set<String> getViewModelKeys() {
      return ImmutableSet.<String>of(MainViewModel_HiltModules_KeyModule_ProvideFactory.provide(), PrayerLocationViewModel_HiltModules_KeyModule_ProvideFactory.provide(), PrayerViewModel_HiltModules_KeyModule_ProvideFactory.provide(), QuranViewModel_HiltModules_KeyModule_ProvideFactory.provide(), SurahDetailViewModel_HiltModules_KeyModule_ProvideFactory.provide());
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @CanIgnoreReturnValue
    private MainActivity injectMainActivity2(MainActivity instance) {
      MainActivity_MembersInjector.injectThemeManager(instance, singletonCImpl.themeManagerProvider.get());
      return instance;
    }
  }

  private static final class ViewModelCImpl extends MuslimCoreApplication_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private Provider<MainViewModel> mainViewModelProvider;

    private Provider<PrayerLocationViewModel> prayerLocationViewModelProvider;

    private Provider<PrayerViewModel> prayerViewModelProvider;

    private Provider<QuranViewModel> quranViewModelProvider;

    private Provider<SurahDetailViewModel> surahDetailViewModelProvider;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;

      initialize(savedStateHandleParam, viewModelLifecycleParam);

    }

    private GetPrayerTimesUseCase getPrayerTimesUseCase() {
      return new GetPrayerTimesUseCase(singletonCImpl.prayerRepositoryImplProvider.get(), singletonCImpl.userRepositoryImplProvider.get());
    }

    private LogPrayerUseCase logPrayerUseCase() {
      return new LogPrayerUseCase(singletonCImpl.prayerRepositoryImplProvider.get(), singletonCImpl.userRepositoryImplProvider.get());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final SavedStateHandle savedStateHandleParam,
        final ViewModelLifecycle viewModelLifecycleParam) {
      this.mainViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 0);
      this.prayerLocationViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 1);
      this.prayerViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 2);
      this.quranViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 3);
      this.surahDetailViewModelProvider = new SwitchingProvider<>(singletonCImpl, activityRetainedCImpl, viewModelCImpl, 4);
    }

    @Override
    public Map<String, Provider<ViewModel>> getHiltViewModelMap() {
      return ImmutableMap.<String, Provider<ViewModel>>of("com.muslimcore.presentation.viewmodels.MainViewModel", ((Provider) mainViewModelProvider), "com.muslimcore.presentation.viewmodels.PrayerLocationViewModel", ((Provider) prayerLocationViewModelProvider), "com.muslimcore.presentation.viewmodels.PrayerViewModel", ((Provider) prayerViewModelProvider), "com.muslimcore.presentation.viewmodels.QuranViewModel", ((Provider) quranViewModelProvider), "com.muslimcore.presentation.viewmodels.SurahDetailViewModel", ((Provider) surahDetailViewModelProvider));
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final ViewModelCImpl viewModelCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          ViewModelCImpl viewModelCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.viewModelCImpl = viewModelCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.muslimcore.presentation.viewmodels.MainViewModel 
          return (T) new MainViewModel(singletonCImpl.userRepositoryImplProvider.get(), singletonCImpl.locationRepositoryImplProvider.get(), singletonCImpl.getCurrentLocationUseCaseProvider.get(), singletonCImpl.preferencesManagerProvider.get());

          case 1: // com.muslimcore.presentation.viewmodels.PrayerLocationViewModel 
          return (T) new PrayerLocationViewModel(singletonCImpl.locationRepositoryImplProvider.get(), singletonCImpl.getCurrentLocationUseCaseProvider.get());

          case 2: // com.muslimcore.presentation.viewmodels.PrayerViewModel 
          return (T) new PrayerViewModel(viewModelCImpl.getPrayerTimesUseCase(), viewModelCImpl.logPrayerUseCase(), singletonCImpl.prayerRepositoryImplProvider.get(), singletonCImpl.userRepositoryImplProvider.get());

          case 3: // com.muslimcore.presentation.viewmodels.QuranViewModel 
          return (T) new QuranViewModel(singletonCImpl.quranAssetsRepositoryProvider.get());

          case 4: // com.muslimcore.presentation.viewmodels.SurahDetailViewModel 
          return (T) new SurahDetailViewModel(singletonCImpl.quranAssetsRepositoryProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ActivityRetainedCImpl extends MuslimCoreApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;

      initialize();

    }

    @SuppressWarnings("unchecked")
    private void initialize() {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends MuslimCoreApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }

    @Override
    public void injectPrayerService(PrayerService prayerService) {
      injectPrayerService2(prayerService);
    }

    @CanIgnoreReturnValue
    private PrayerService injectPrayerService2(PrayerService instance) {
      PrayerService_MembersInjector.injectLocationManager(instance, singletonCImpl.locationManagerProvider.get());
      PrayerService_MembersInjector.injectPrayerTimeManager(instance, singletonCImpl.prayerTimeManagerProvider.get());
      return instance;
    }
  }

  private static final class SingletonCImpl extends MuslimCoreApplication_HiltComponents.SingletonC {
    private final ApplicationContextModule applicationContextModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<PreferencesManager> preferencesManagerProvider;

    private Provider<ThemeManager> themeManagerProvider;

    private Provider<LocationManager> locationManagerProvider;

    private Provider<PrayerTimeManager> prayerTimeManagerProvider;

    private Provider<FirebaseAuth> provideFirebaseAuthProvider;

    private Provider<FirebaseAnalytics> provideFirebaseAnalyticsProvider;

    private Provider<UserRepositoryImpl> userRepositoryImplProvider;

    private Provider<LocationRepositoryImpl> locationRepositoryImplProvider;

    private Provider<GetCurrentLocationUseCase> getCurrentLocationUseCaseProvider;

    private Provider<MuslimCoreDatabase> provideMuslimCoreDatabaseProvider;

    private Provider<OkHttpClient> provideOkHttpClientProvider;

    private Provider<Retrofit> provideRetrofitProvider;

    private Provider<PrayerTimesApi> providePrayerTimesApiProvider;

    private Provider<PrayerRepositoryImpl> prayerRepositoryImplProvider;

    private Provider<Gson> provideGsonProvider;

    private Provider<QuranAssetsDataSource> quranAssetsDataSourceProvider;

    private Provider<QuranAssetsRepository> quranAssetsRepositoryProvider;

    private SingletonCImpl(ApplicationContextModule applicationContextModuleParam) {
      this.applicationContextModule = applicationContextModuleParam;
      initialize(applicationContextModuleParam);

    }

    private PrayerDao prayerDao() {
      return DatabaseModule_ProvidePrayerDaoFactory.providePrayerDao(provideMuslimCoreDatabaseProvider.get());
    }

    @SuppressWarnings("unchecked")
    private void initialize(final ApplicationContextModule applicationContextModuleParam) {
      this.preferencesManagerProvider = DoubleCheck.provider(new SwitchingProvider<PreferencesManager>(singletonCImpl, 1));
      this.themeManagerProvider = DoubleCheck.provider(new SwitchingProvider<ThemeManager>(singletonCImpl, 0));
      this.locationManagerProvider = DoubleCheck.provider(new SwitchingProvider<LocationManager>(singletonCImpl, 2));
      this.prayerTimeManagerProvider = DoubleCheck.provider(new SwitchingProvider<PrayerTimeManager>(singletonCImpl, 3));
      this.provideFirebaseAuthProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseAuth>(singletonCImpl, 5));
      this.provideFirebaseAnalyticsProvider = DoubleCheck.provider(new SwitchingProvider<FirebaseAnalytics>(singletonCImpl, 6));
      this.userRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<UserRepositoryImpl>(singletonCImpl, 4));
      this.locationRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<LocationRepositoryImpl>(singletonCImpl, 7));
      this.getCurrentLocationUseCaseProvider = DoubleCheck.provider(new SwitchingProvider<GetCurrentLocationUseCase>(singletonCImpl, 8));
      this.provideMuslimCoreDatabaseProvider = DoubleCheck.provider(new SwitchingProvider<MuslimCoreDatabase>(singletonCImpl, 10));
      this.provideOkHttpClientProvider = DoubleCheck.provider(new SwitchingProvider<OkHttpClient>(singletonCImpl, 13));
      this.provideRetrofitProvider = DoubleCheck.provider(new SwitchingProvider<Retrofit>(singletonCImpl, 12));
      this.providePrayerTimesApiProvider = DoubleCheck.provider(new SwitchingProvider<PrayerTimesApi>(singletonCImpl, 11));
      this.prayerRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<PrayerRepositoryImpl>(singletonCImpl, 9));
      this.provideGsonProvider = DoubleCheck.provider(new SwitchingProvider<Gson>(singletonCImpl, 16));
      this.quranAssetsDataSourceProvider = DoubleCheck.provider(new SwitchingProvider<QuranAssetsDataSource>(singletonCImpl, 15));
      this.quranAssetsRepositoryProvider = DoubleCheck.provider(new SwitchingProvider<QuranAssetsRepository>(singletonCImpl, 14));
    }

    @Override
    public void injectMuslimCoreApplication(MuslimCoreApplication muslimCoreApplication) {
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return ImmutableSet.<Boolean>of();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.muslimcore.presentation.utils.ThemeManager 
          return (T) new ThemeManager(singletonCImpl.preferencesManagerProvider.get());

          case 1: // com.muslimcore.data.local.PreferencesManager 
          return (T) new PreferencesManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 2: // com.muslimcore.data.local.managers.LocationManager 
          return (T) new LocationManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 3: // com.muslimcore.data.local.managers.PrayerTimeManager 
          return (T) new PrayerTimeManager(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 4: // com.muslimcore.data.repositories.UserRepositoryImpl 
          return (T) new UserRepositoryImpl(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideFirebaseAuthProvider.get(), singletonCImpl.provideFirebaseAnalyticsProvider.get());

          case 5: // com.google.firebase.auth.FirebaseAuth 
          return (T) FirebaseModule_ProvideFirebaseAuthFactory.provideFirebaseAuth();

          case 6: // com.google.firebase.analytics.FirebaseAnalytics 
          return (T) FirebaseModule_ProvideFirebaseAnalyticsFactory.provideFirebaseAnalytics();

          case 7: // com.muslimcore.data.repositories.LocationRepositoryImpl 
          return (T) new LocationRepositoryImpl(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.preferencesManagerProvider.get());

          case 8: // com.muslimcore.domain.usecases.GetCurrentLocationUseCase 
          return (T) new GetCurrentLocationUseCase(singletonCImpl.locationRepositoryImplProvider.get());

          case 9: // com.muslimcore.data.repositories.PrayerRepositoryImpl 
          return (T) new PrayerRepositoryImpl(singletonCImpl.prayerDao(), singletonCImpl.providePrayerTimesApiProvider.get());

          case 10: // com.muslimcore.data.local.database.MuslimCoreDatabase 
          return (T) DatabaseModule_ProvideMuslimCoreDatabaseFactory.provideMuslimCoreDatabase(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 11: // com.muslimcore.data.remote.api.PrayerTimesApi 
          return (T) NetworkModule_ProvidePrayerTimesApiFactory.providePrayerTimesApi(singletonCImpl.provideRetrofitProvider.get());

          case 12: // retrofit2.Retrofit 
          return (T) NetworkModule_ProvideRetrofitFactory.provideRetrofit(singletonCImpl.provideOkHttpClientProvider.get());

          case 13: // okhttp3.OkHttpClient 
          return (T) NetworkModule_ProvideOkHttpClientFactory.provideOkHttpClient();

          case 14: // com.muslimcore.data.repositories.QuranAssetsRepository 
          return (T) new QuranAssetsRepository(singletonCImpl.quranAssetsDataSourceProvider.get());

          case 15: // com.muslimcore.data.local.datasource.QuranAssetsDataSource 
          return (T) new QuranAssetsDataSource(ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideGsonProvider.get());

          case 16: // com.google.gson.Gson 
          return (T) NetworkModule_ProvideGsonFactory.provideGson();

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
