package com.muslimcore.presentation.utils;

import com.muslimcore.data.local.PreferencesManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ThemeManager_Factory implements Factory<ThemeManager> {
  private final Provider<PreferencesManager> preferencesManagerProvider;

  public ThemeManager_Factory(Provider<PreferencesManager> preferencesManagerProvider) {
    this.preferencesManagerProvider = preferencesManagerProvider;
  }

  @Override
  public ThemeManager get() {
    return newInstance(preferencesManagerProvider.get());
  }

  public static ThemeManager_Factory create(
      Provider<PreferencesManager> preferencesManagerProvider) {
    return new ThemeManager_Factory(preferencesManagerProvider);
  }

  public static ThemeManager newInstance(PreferencesManager preferencesManager) {
    return new ThemeManager(preferencesManager);
  }
}
