package com.muslimcore.presentation.receivers

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.muslimcore.presentation.services.PrayerService

class BootReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                // Check if user has enabled notifications
                val notificationPrefs = context.getSharedPreferences("notification_settings", Context.MODE_PRIVATE)
                val countdownEnabled = notificationPrefs.getBoolean("countdown_enabled", false)
                val prayerAlertsEnabled = notificationPrefs.getBoolean("prayer_alerts_enabled", true)
                
                // Only restart service if user has enabled notifications
                if (countdownEnabled || prayerAlertsEnabled) {
                    PrayerService.startService(context)
                }
            }
        }
    }
}
