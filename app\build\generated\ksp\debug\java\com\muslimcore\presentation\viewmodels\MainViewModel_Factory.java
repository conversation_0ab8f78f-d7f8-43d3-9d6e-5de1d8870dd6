package com.muslimcore.presentation.viewmodels;

import com.muslimcore.data.local.PreferencesManager;
import com.muslimcore.domain.repositories.LocationRepository;
import com.muslimcore.domain.repositories.UserRepository;
import com.muslimcore.domain.usecases.GetCurrentLocationUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainViewModel_Factory implements Factory<MainViewModel> {
  private final Provider<UserRepository> userRepositoryProvider;

  private final Provider<LocationRepository> locationRepositoryProvider;

  private final Provider<GetCurrentLocationUseCase> getCurrentLocationUseCaseProvider;

  private final Provider<PreferencesManager> preferencesManagerProvider;

  public MainViewModel_Factory(Provider<UserRepository> userRepositoryProvider,
      Provider<LocationRepository> locationRepositoryProvider,
      Provider<GetCurrentLocationUseCase> getCurrentLocationUseCaseProvider,
      Provider<PreferencesManager> preferencesManagerProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
    this.locationRepositoryProvider = locationRepositoryProvider;
    this.getCurrentLocationUseCaseProvider = getCurrentLocationUseCaseProvider;
    this.preferencesManagerProvider = preferencesManagerProvider;
  }

  @Override
  public MainViewModel get() {
    return newInstance(userRepositoryProvider.get(), locationRepositoryProvider.get(), getCurrentLocationUseCaseProvider.get(), preferencesManagerProvider.get());
  }

  public static MainViewModel_Factory create(Provider<UserRepository> userRepositoryProvider,
      Provider<LocationRepository> locationRepositoryProvider,
      Provider<GetCurrentLocationUseCase> getCurrentLocationUseCaseProvider,
      Provider<PreferencesManager> preferencesManagerProvider) {
    return new MainViewModel_Factory(userRepositoryProvider, locationRepositoryProvider, getCurrentLocationUseCaseProvider, preferencesManagerProvider);
  }

  public static MainViewModel newInstance(UserRepository userRepository,
      LocationRepository locationRepository, GetCurrentLocationUseCase getCurrentLocationUseCase,
      PreferencesManager preferencesManager) {
    return new MainViewModel(userRepository, locationRepository, getCurrentLocationUseCase, preferencesManager);
  }
}
