package com.muslimcore.domain.usecases.prayer;

import com.muslimcore.domain.repositories.PrayerRepository;
import com.muslimcore.domain.repositories.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GetPrayerTimesUseCase_Factory implements Factory<GetPrayerTimesUseCase> {
  private final Provider<PrayerRepository> prayerRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  public GetPrayerTimesUseCase_Factory(Provider<PrayerRepository> prayerRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    this.prayerRepositoryProvider = prayerRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public GetPrayerTimesUseCase get() {
    return newInstance(prayerRepositoryProvider.get(), userRepositoryProvider.get());
  }

  public static GetPrayerTimesUseCase_Factory create(
      Provider<PrayerRepository> prayerRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    return new GetPrayerTimesUseCase_Factory(prayerRepositoryProvider, userRepositoryProvider);
  }

  public static GetPrayerTimesUseCase newInstance(PrayerRepository prayerRepository,
      UserRepository userRepository) {
    return new GetPrayerTimesUseCase(prayerRepository, userRepository);
  }
}
