package com.muslimcore.data.local.managers

import android.Manifest
import android.content.Context
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.location.Address
import android.location.Geocoder
import android.location.Location
import androidx.core.app.ActivityCompat
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.Priority
import com.google.android.gms.tasks.CancellationTokenSource
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

@Singleton
class LocationManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences("location_prefs", Context.MODE_PRIVATE)
    private val geocoder = Geocoder(context, Locale.getDefault())
    private val fusedLocationClient: FusedLocationProviderClient = LocationServices.getFusedLocationProviderClient(context)
    
    companion object {
        private const val PREF_LATITUDE = "latitude"
        private const val PREF_LONGITUDE = "longitude"
        private const val PREF_CITY_NAME = "city_name"
        private const val PREF_COUNTRY_NAME = "country_name"
        private const val PREF_LAST_UPDATE = "last_update"
        private const val LOCATION_UPDATE_INTERVAL = 24 * 60 * 60 * 1000L // 24 hours
    }
    
    data class LocationData(
        val latitude: Double,
        val longitude: Double,
        val cityName: String,
        val countryName: String
    )
    
    /**
     * Get current location with automatic detection using FusedLocationProviderClient
     */
    suspend fun getCurrentLocation(): LocationData? {
        return withContext(Dispatchers.IO) {
            // First try to get cached location if recent
            getCachedLocation()?.let { cached ->
                val lastUpdate = prefs.getLong(PREF_LAST_UPDATE, 0)
                if (System.currentTimeMillis() - lastUpdate < LOCATION_UPDATE_INTERVAL) {
                    return@withContext cached
                }
            }

            // Try to get fresh location using FusedLocationProviderClient
            getFreshLocationWithFused() ?: getCachedLocation()
        }
    }
    
    /**
     * Get cached location from preferences
     */
    private fun getCachedLocation(): LocationData? {
        val lat = prefs.getFloat(PREF_LATITUDE, 0f).toDouble()
        val lng = prefs.getFloat(PREF_LONGITUDE, 0f).toDouble()
        val city = prefs.getString(PREF_CITY_NAME, "") ?: ""
        val country = prefs.getString(PREF_COUNTRY_NAME, "") ?: ""
        
        return if (lat != 0.0 && lng != 0.0 && city.isNotEmpty()) {
            LocationData(lat, lng, city, country)
        } else null
    }
    
    /**
     * Get fresh location using FusedLocationProviderClient with timeout
     */
    private suspend fun getFreshLocationWithFused(): LocationData? {
        if (!hasLocationPermission()) {
            return null
        }

        return try {
            // Use withTimeoutOrNull to prevent hanging indefinitely
            withTimeoutOrNull(30000) { // 30 second timeout
                suspendCancellableCoroutine<Location?> { continuation ->
                    val cancellationTokenSource = CancellationTokenSource()

                    // Set up cancellation
                    continuation.invokeOnCancellation {
                        cancellationTokenSource.cancel()
                    }

                    try {
                        fusedLocationClient.getCurrentLocation(
                            Priority.PRIORITY_HIGH_ACCURACY,
                            cancellationTokenSource.token
                        ).addOnSuccessListener { location ->
                            continuation.resume(location)
                        }.addOnFailureListener { exception ->
                            continuation.resume(null)
                        }
                    } catch (e: SecurityException) {
                        continuation.resume(null)
                    }
                }
            }?.let { location ->
                // Convert location to LocationData with reverse geocoding
                reverseGeocode(location.latitude, location.longitude)?.also { locationData ->
                    saveLocation(locationData)
                }
            }
        } catch (e: Exception) {
            null
        }
    }
    

    
    /**
     * Convert coordinates to city name using reverse geocoding
     */
    private suspend fun reverseGeocode(latitude: Double, longitude: Double): LocationData? {
        return withContext(Dispatchers.IO) {
            try {
                val addresses = geocoder.getFromLocation(latitude, longitude, 1)
                if (!addresses.isNullOrEmpty()) {
                    val address = addresses[0]
                    val city = address.locality ?: address.adminArea ?: address.subAdminArea ?: "Unknown City"
                    val country = address.countryName ?: "Unknown Country"
                    
                    LocationData(latitude, longitude, city, country)
                } else {
                    null
                }
            } catch (e: Exception) {
                null
            }
        }
    }
    
    /**
     * Search for locations by city name
     */
    suspend fun searchLocations(query: String): List<LocationData> {
        return withContext(Dispatchers.IO) {
            try {
                val addresses = geocoder.getFromLocationName(query, 5)
                addresses?.mapNotNull { address ->
                    val city = address.locality ?: address.adminArea ?: address.subAdminArea
                    val country = address.countryName
                    
                    if (city != null && country != null) {
                        LocationData(address.latitude, address.longitude, city, country)
                    } else null
                } ?: emptyList()
            } catch (e: Exception) {
                emptyList()
            }
        }
    }
    
    /**
     * Save location to preferences
     */
    private fun saveLocation(locationData: LocationData) {
        prefs.edit()
            .putFloat(PREF_LATITUDE, locationData.latitude.toFloat())
            .putFloat(PREF_LONGITUDE, locationData.longitude.toFloat())
            .putString(PREF_CITY_NAME, locationData.cityName)
            .putString(PREF_COUNTRY_NAME, locationData.countryName)
            .putLong(PREF_LAST_UPDATE, System.currentTimeMillis())
            .apply()
    }
    
    /**
     * Set location manually
     */
    fun setLocation(locationData: LocationData) {
        saveLocation(locationData)
    }
    
    /**
     * Check if location permissions are granted
     */
    fun hasLocationPermission(): Boolean {
        return ActivityCompat.checkSelfPermission(
            context, Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED ||
        ActivityCompat.checkSelfPermission(
            context, Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    /**
     * Get popular Islamic cities
     */
    fun getPopularIslamicCities(): List<LocationData> {
        return listOf(
            LocationData(21.4225, 39.8262, "Mecca", "Saudi Arabia"),
            LocationData(24.4686, 39.6142, "Medina", "Saudi Arabia"),
            LocationData(31.7683, 35.2137, "Jerusalem", "Palestine"),
            LocationData(30.0444, 31.2357, "Cairo", "Egypt"),
            LocationData(33.5138, 36.2765, "Damascus", "Syria"),
            LocationData(33.3152, 44.3661, "Baghdad", "Iraq"),
            LocationData(35.6892, 51.3890, "Tehran", "Iran"),
            LocationData(41.0082, 28.9784, "Istanbul", "Turkey"),
            LocationData(24.7136, 46.6753, "Riyadh", "Saudi Arabia"),
            LocationData(25.2048, 55.2708, "Dubai", "UAE")
        )
    }
}
