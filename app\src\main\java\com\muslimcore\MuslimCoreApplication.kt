package com.muslimcore

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import androidx.work.Configuration
import androidx.work.WorkManager
import dagger.hilt.android.HiltAndroidApp

@HiltAndroidApp
class MuslimCoreApplication : Application(), Configuration.Provider {

    companion object {
        const val PRAYER_NOTIFICATION_CHANNEL_ID = "prayer_notifications"
        const val ADHKAR_NOTIFICATION_CHANNEL_ID = "adhkar_notifications"
        const val GENERAL_NOTIFICATION_CHANNEL_ID = "general_notifications"
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannels()
        initializeWorkManager()
    }

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // Prayer Times Notification Channel
            val prayerChannel = NotificationChannel(
                PRAYER_NOTIFICATION_CHANNEL_ID,
                "Prayer Times",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for prayer times"
                enableVibration(true)
                setShowBadge(true)
            }

            // Adhkar Notification Channel
            val adhkarChannel = NotificationChannel(
                ADHKAR_NOTIFICATION_CHANNEL_ID,
                "Adhkar Reminders",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Reminders for morning and evening adhkar"
                enableVibration(true)
                setShowBadge(true)
            }

            // General Notification Channel
            val generalChannel = NotificationChannel(
                GENERAL_NOTIFICATION_CHANNEL_ID,
                "General",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "General app notifications"
                enableVibration(false)
                setShowBadge(false)
            }

            notificationManager.createNotificationChannels(
                listOf(prayerChannel, adhkarChannel, generalChannel)
            )
        }
    }

    private fun initializeWorkManager() {
        try {
            // Check if WorkManager is already initialized
            WorkManager.initialize(this, workManagerConfiguration)
            android.util.Log.d("MuslimCoreApp", "WorkManager initialized successfully")
        } catch (e: IllegalStateException) {
            // WorkManager already initialized
            android.util.Log.d("MuslimCoreApp", "WorkManager already initialized")
        } catch (e: Exception) {
            android.util.Log.e("MuslimCoreApp", "Error initializing WorkManager", e)
        }
    }



    override fun getWorkManagerConfiguration(): Configuration {
        return Configuration.Builder()
            .setMinimumLoggingLevel(android.util.Log.INFO)
            .setWorkerFactory(androidx.work.DelegatingWorkerFactory())
            .build()
    }
}
