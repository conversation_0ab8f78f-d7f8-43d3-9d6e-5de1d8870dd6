package com.muslimcore.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.muslimcore.data.local.entities.AyahEntity;
import com.muslimcore.data.local.entities.BookmarkEntity;
import com.muslimcore.data.local.entities.KhatmaProgressEntity;
import com.muslimcore.data.local.entities.ReadingProgressEntity;
import com.muslimcore.data.local.entities.SurahEntity;
import java.lang.Boolean;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class QuranDao_Impl implements QuranDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<SurahEntity> __insertionAdapterOfSurahEntity;

  private final EntityInsertionAdapter<AyahEntity> __insertionAdapterOfAyahEntity;

  private final EntityInsertionAdapter<BookmarkEntity> __insertionAdapterOfBookmarkEntity;

  private final EntityInsertionAdapter<ReadingProgressEntity> __insertionAdapterOfReadingProgressEntity;

  private final EntityInsertionAdapter<KhatmaProgressEntity> __insertionAdapterOfKhatmaProgressEntity;

  private final EntityDeletionOrUpdateAdapter<BookmarkEntity> __deletionAdapterOfBookmarkEntity;

  private final EntityDeletionOrUpdateAdapter<KhatmaProgressEntity> __deletionAdapterOfKhatmaProgressEntity;

  private final EntityDeletionOrUpdateAdapter<KhatmaProgressEntity> __updateAdapterOfKhatmaProgressEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteBookmarkByAyah;

  private final SharedSQLiteStatement __preparedStmtOfInsertFavoriteSurah;

  private final SharedSQLiteStatement __preparedStmtOfDeleteFavoriteSurah;

  public QuranDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfSurahEntity = new EntityInsertionAdapter<SurahEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `surahs` (`number`,`name`,`englishName`,`englishNameTranslation`,`numberOfAyahs`,`revelationType`) VALUES (?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SurahEntity entity) {
        statement.bindLong(1, entity.getNumber());
        statement.bindString(2, entity.getName());
        statement.bindString(3, entity.getEnglishName());
        statement.bindString(4, entity.getEnglishNameTranslation());
        statement.bindLong(5, entity.getNumberOfAyahs());
        statement.bindString(6, entity.getRevelationType());
      }
    };
    this.__insertionAdapterOfAyahEntity = new EntityInsertionAdapter<AyahEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `ayahs` (`number`,`text`,`numberInSurah`,`surahNumber`,`juz`,`manzil`,`page`,`ruku`,`hizbQuarter`,`sajda`,`translation`,`audio`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AyahEntity entity) {
        statement.bindLong(1, entity.getNumber());
        statement.bindString(2, entity.getText());
        statement.bindLong(3, entity.getNumberInSurah());
        statement.bindLong(4, entity.getSurahNumber());
        statement.bindLong(5, entity.getJuz());
        statement.bindLong(6, entity.getManzil());
        statement.bindLong(7, entity.getPage());
        statement.bindLong(8, entity.getRuku());
        statement.bindLong(9, entity.getHizbQuarter());
        final int _tmp = entity.getSajda() ? 1 : 0;
        statement.bindLong(10, _tmp);
        if (entity.getTranslation() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getTranslation());
        }
        if (entity.getAudio() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getAudio());
        }
      }
    };
    this.__insertionAdapterOfBookmarkEntity = new EntityInsertionAdapter<BookmarkEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `bookmarks` (`id`,`surahNumber`,`ayahNumber`,`surahName`,`ayahText`,`note`,`createdAt`,`userId`) VALUES (nullif(?, 0),?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final BookmarkEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getSurahNumber());
        statement.bindLong(3, entity.getAyahNumber());
        statement.bindString(4, entity.getSurahName());
        statement.bindString(5, entity.getAyahText());
        if (entity.getNote() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getNote());
        }
        statement.bindLong(7, entity.getCreatedAt());
        if (entity.getUserId() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getUserId());
        }
      }
    };
    this.__insertionAdapterOfReadingProgressEntity = new EntityInsertionAdapter<ReadingProgressEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `reading_progress` (`id`,`surahNumber`,`ayahNumber`,`lastReadAt`,`userId`) VALUES (?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final ReadingProgressEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getSurahNumber());
        statement.bindLong(3, entity.getAyahNumber());
        statement.bindLong(4, entity.getLastReadAt());
        if (entity.getUserId() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getUserId());
        }
      }
    };
    this.__insertionAdapterOfKhatmaProgressEntity = new EntityInsertionAdapter<KhatmaProgressEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `khatma_progress` (`id`,`name`,`targetDays`,`startDate`,`endDate`,`currentSurah`,`currentAyah`,`isCompleted`,`dailyTarget`,`userId`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final KhatmaProgressEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getName());
        statement.bindLong(3, entity.getTargetDays());
        statement.bindLong(4, entity.getStartDate());
        statement.bindLong(5, entity.getEndDate());
        statement.bindLong(6, entity.getCurrentSurah());
        statement.bindLong(7, entity.getCurrentAyah());
        final int _tmp = entity.isCompleted() ? 1 : 0;
        statement.bindLong(8, _tmp);
        statement.bindLong(9, entity.getDailyTarget());
        if (entity.getUserId() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getUserId());
        }
      }
    };
    this.__deletionAdapterOfBookmarkEntity = new EntityDeletionOrUpdateAdapter<BookmarkEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `bookmarks` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final BookmarkEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__deletionAdapterOfKhatmaProgressEntity = new EntityDeletionOrUpdateAdapter<KhatmaProgressEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `khatma_progress` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final KhatmaProgressEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfKhatmaProgressEntity = new EntityDeletionOrUpdateAdapter<KhatmaProgressEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `khatma_progress` SET `id` = ?,`name` = ?,`targetDays` = ?,`startDate` = ?,`endDate` = ?,`currentSurah` = ?,`currentAyah` = ?,`isCompleted` = ?,`dailyTarget` = ?,`userId` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final KhatmaProgressEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindString(2, entity.getName());
        statement.bindLong(3, entity.getTargetDays());
        statement.bindLong(4, entity.getStartDate());
        statement.bindLong(5, entity.getEndDate());
        statement.bindLong(6, entity.getCurrentSurah());
        statement.bindLong(7, entity.getCurrentAyah());
        final int _tmp = entity.isCompleted() ? 1 : 0;
        statement.bindLong(8, _tmp);
        statement.bindLong(9, entity.getDailyTarget());
        if (entity.getUserId() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getUserId());
        }
        statement.bindLong(11, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteBookmarkByAyah = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM bookmarks WHERE surahNumber = ? AND ayahNumber = ?";
        return _query;
      }
    };
    this.__preparedStmtOfInsertFavoriteSurah = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "INSERT INTO favorite_surahs (surahNumber) VALUES (?)";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteFavoriteSurah = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM favorite_surahs WHERE surahNumber = ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertSurahs(final List<SurahEntity> surahs,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfSurahEntity.insert(surahs);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertAyahs(final List<AyahEntity> ayahs,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfAyahEntity.insert(ayahs);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertBookmark(final BookmarkEntity bookmark,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfBookmarkEntity.insert(bookmark);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertReadingProgress(final ReadingProgressEntity progress,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfReadingProgressEntity.insert(progress);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertKhatma(final KhatmaProgressEntity khatma,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfKhatmaProgressEntity.insertAndReturnId(khatma);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteBookmark(final BookmarkEntity bookmark,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfBookmarkEntity.handle(bookmark);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteKhatma(final KhatmaProgressEntity khatma,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfKhatmaProgressEntity.handle(khatma);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateKhatma(final KhatmaProgressEntity khatma,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfKhatmaProgressEntity.handle(khatma);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteBookmarkByAyah(final int surahNumber, final int ayahNumber,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteBookmarkByAyah.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, surahNumber);
        _argIndex = 2;
        _stmt.bindLong(_argIndex, ayahNumber);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteBookmarkByAyah.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object insertFavoriteSurah(final int surahNumber,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfInsertFavoriteSurah.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, surahNumber);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeInsert();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfInsertFavoriteSurah.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteFavoriteSurah(final int surahNumber,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteFavoriteSurah.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, surahNumber);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteFavoriteSurah.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<SurahEntity>> getAllSurahs() {
    final String _sql = "SELECT * FROM surahs ORDER BY number ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"surahs"}, new Callable<List<SurahEntity>>() {
      @Override
      @NonNull
      public List<SurahEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "number");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfEnglishName = CursorUtil.getColumnIndexOrThrow(_cursor, "englishName");
          final int _cursorIndexOfEnglishNameTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "englishNameTranslation");
          final int _cursorIndexOfNumberOfAyahs = CursorUtil.getColumnIndexOrThrow(_cursor, "numberOfAyahs");
          final int _cursorIndexOfRevelationType = CursorUtil.getColumnIndexOrThrow(_cursor, "revelationType");
          final List<SurahEntity> _result = new ArrayList<SurahEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SurahEntity _item;
            final int _tmpNumber;
            _tmpNumber = _cursor.getInt(_cursorIndexOfNumber);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpEnglishName;
            _tmpEnglishName = _cursor.getString(_cursorIndexOfEnglishName);
            final String _tmpEnglishNameTranslation;
            _tmpEnglishNameTranslation = _cursor.getString(_cursorIndexOfEnglishNameTranslation);
            final int _tmpNumberOfAyahs;
            _tmpNumberOfAyahs = _cursor.getInt(_cursorIndexOfNumberOfAyahs);
            final String _tmpRevelationType;
            _tmpRevelationType = _cursor.getString(_cursorIndexOfRevelationType);
            _item = new SurahEntity(_tmpNumber,_tmpName,_tmpEnglishName,_tmpEnglishNameTranslation,_tmpNumberOfAyahs,_tmpRevelationType);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getSurah(final int surahNumber,
      final Continuation<? super SurahEntity> $completion) {
    final String _sql = "SELECT * FROM surahs WHERE number = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, surahNumber);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<SurahEntity>() {
      @Override
      @Nullable
      public SurahEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "number");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfEnglishName = CursorUtil.getColumnIndexOrThrow(_cursor, "englishName");
          final int _cursorIndexOfEnglishNameTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "englishNameTranslation");
          final int _cursorIndexOfNumberOfAyahs = CursorUtil.getColumnIndexOrThrow(_cursor, "numberOfAyahs");
          final int _cursorIndexOfRevelationType = CursorUtil.getColumnIndexOrThrow(_cursor, "revelationType");
          final SurahEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpNumber;
            _tmpNumber = _cursor.getInt(_cursorIndexOfNumber);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpEnglishName;
            _tmpEnglishName = _cursor.getString(_cursorIndexOfEnglishName);
            final String _tmpEnglishNameTranslation;
            _tmpEnglishNameTranslation = _cursor.getString(_cursorIndexOfEnglishNameTranslation);
            final int _tmpNumberOfAyahs;
            _tmpNumberOfAyahs = _cursor.getInt(_cursorIndexOfNumberOfAyahs);
            final String _tmpRevelationType;
            _tmpRevelationType = _cursor.getString(_cursorIndexOfRevelationType);
            _result = new SurahEntity(_tmpNumber,_tmpName,_tmpEnglishName,_tmpEnglishNameTranslation,_tmpNumberOfAyahs,_tmpRevelationType);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<SurahEntity>> searchSurahs(final String query) {
    final String _sql = "SELECT * FROM surahs WHERE name LIKE '%' || ? || '%' OR englishName LIKE '%' || ? || '%'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, query);
    _argIndex = 2;
    _statement.bindString(_argIndex, query);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"surahs"}, new Callable<List<SurahEntity>>() {
      @Override
      @NonNull
      public List<SurahEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "number");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfEnglishName = CursorUtil.getColumnIndexOrThrow(_cursor, "englishName");
          final int _cursorIndexOfEnglishNameTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "englishNameTranslation");
          final int _cursorIndexOfNumberOfAyahs = CursorUtil.getColumnIndexOrThrow(_cursor, "numberOfAyahs");
          final int _cursorIndexOfRevelationType = CursorUtil.getColumnIndexOrThrow(_cursor, "revelationType");
          final List<SurahEntity> _result = new ArrayList<SurahEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SurahEntity _item;
            final int _tmpNumber;
            _tmpNumber = _cursor.getInt(_cursorIndexOfNumber);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpEnglishName;
            _tmpEnglishName = _cursor.getString(_cursorIndexOfEnglishName);
            final String _tmpEnglishNameTranslation;
            _tmpEnglishNameTranslation = _cursor.getString(_cursorIndexOfEnglishNameTranslation);
            final int _tmpNumberOfAyahs;
            _tmpNumberOfAyahs = _cursor.getInt(_cursorIndexOfNumberOfAyahs);
            final String _tmpRevelationType;
            _tmpRevelationType = _cursor.getString(_cursorIndexOfRevelationType);
            _item = new SurahEntity(_tmpNumber,_tmpName,_tmpEnglishName,_tmpEnglishNameTranslation,_tmpNumberOfAyahs,_tmpRevelationType);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAyahsBySurah(final int surahNumber,
      final Continuation<? super List<AyahEntity>> $completion) {
    final String _sql = "SELECT * FROM ayahs WHERE surahNumber = ? ORDER BY numberInSurah ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, surahNumber);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<AyahEntity>>() {
      @Override
      @NonNull
      public List<AyahEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "number");
          final int _cursorIndexOfText = CursorUtil.getColumnIndexOrThrow(_cursor, "text");
          final int _cursorIndexOfNumberInSurah = CursorUtil.getColumnIndexOrThrow(_cursor, "numberInSurah");
          final int _cursorIndexOfSurahNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "surahNumber");
          final int _cursorIndexOfJuz = CursorUtil.getColumnIndexOrThrow(_cursor, "juz");
          final int _cursorIndexOfManzil = CursorUtil.getColumnIndexOrThrow(_cursor, "manzil");
          final int _cursorIndexOfPage = CursorUtil.getColumnIndexOrThrow(_cursor, "page");
          final int _cursorIndexOfRuku = CursorUtil.getColumnIndexOrThrow(_cursor, "ruku");
          final int _cursorIndexOfHizbQuarter = CursorUtil.getColumnIndexOrThrow(_cursor, "hizbQuarter");
          final int _cursorIndexOfSajda = CursorUtil.getColumnIndexOrThrow(_cursor, "sajda");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfAudio = CursorUtil.getColumnIndexOrThrow(_cursor, "audio");
          final List<AyahEntity> _result = new ArrayList<AyahEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AyahEntity _item;
            final int _tmpNumber;
            _tmpNumber = _cursor.getInt(_cursorIndexOfNumber);
            final String _tmpText;
            _tmpText = _cursor.getString(_cursorIndexOfText);
            final int _tmpNumberInSurah;
            _tmpNumberInSurah = _cursor.getInt(_cursorIndexOfNumberInSurah);
            final int _tmpSurahNumber;
            _tmpSurahNumber = _cursor.getInt(_cursorIndexOfSurahNumber);
            final int _tmpJuz;
            _tmpJuz = _cursor.getInt(_cursorIndexOfJuz);
            final int _tmpManzil;
            _tmpManzil = _cursor.getInt(_cursorIndexOfManzil);
            final int _tmpPage;
            _tmpPage = _cursor.getInt(_cursorIndexOfPage);
            final int _tmpRuku;
            _tmpRuku = _cursor.getInt(_cursorIndexOfRuku);
            final int _tmpHizbQuarter;
            _tmpHizbQuarter = _cursor.getInt(_cursorIndexOfHizbQuarter);
            final boolean _tmpSajda;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfSajda);
            _tmpSajda = _tmp != 0;
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpAudio;
            if (_cursor.isNull(_cursorIndexOfAudio)) {
              _tmpAudio = null;
            } else {
              _tmpAudio = _cursor.getString(_cursorIndexOfAudio);
            }
            _item = new AyahEntity(_tmpNumber,_tmpText,_tmpNumberInSurah,_tmpSurahNumber,_tmpJuz,_tmpManzil,_tmpPage,_tmpRuku,_tmpHizbQuarter,_tmpSajda,_tmpTranslation,_tmpAudio);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAyahsByJuz(final int juzNumber,
      final Continuation<? super List<AyahEntity>> $completion) {
    final String _sql = "SELECT * FROM ayahs WHERE juz = ? ORDER BY number ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, juzNumber);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<AyahEntity>>() {
      @Override
      @NonNull
      public List<AyahEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "number");
          final int _cursorIndexOfText = CursorUtil.getColumnIndexOrThrow(_cursor, "text");
          final int _cursorIndexOfNumberInSurah = CursorUtil.getColumnIndexOrThrow(_cursor, "numberInSurah");
          final int _cursorIndexOfSurahNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "surahNumber");
          final int _cursorIndexOfJuz = CursorUtil.getColumnIndexOrThrow(_cursor, "juz");
          final int _cursorIndexOfManzil = CursorUtil.getColumnIndexOrThrow(_cursor, "manzil");
          final int _cursorIndexOfPage = CursorUtil.getColumnIndexOrThrow(_cursor, "page");
          final int _cursorIndexOfRuku = CursorUtil.getColumnIndexOrThrow(_cursor, "ruku");
          final int _cursorIndexOfHizbQuarter = CursorUtil.getColumnIndexOrThrow(_cursor, "hizbQuarter");
          final int _cursorIndexOfSajda = CursorUtil.getColumnIndexOrThrow(_cursor, "sajda");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfAudio = CursorUtil.getColumnIndexOrThrow(_cursor, "audio");
          final List<AyahEntity> _result = new ArrayList<AyahEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AyahEntity _item;
            final int _tmpNumber;
            _tmpNumber = _cursor.getInt(_cursorIndexOfNumber);
            final String _tmpText;
            _tmpText = _cursor.getString(_cursorIndexOfText);
            final int _tmpNumberInSurah;
            _tmpNumberInSurah = _cursor.getInt(_cursorIndexOfNumberInSurah);
            final int _tmpSurahNumber;
            _tmpSurahNumber = _cursor.getInt(_cursorIndexOfSurahNumber);
            final int _tmpJuz;
            _tmpJuz = _cursor.getInt(_cursorIndexOfJuz);
            final int _tmpManzil;
            _tmpManzil = _cursor.getInt(_cursorIndexOfManzil);
            final int _tmpPage;
            _tmpPage = _cursor.getInt(_cursorIndexOfPage);
            final int _tmpRuku;
            _tmpRuku = _cursor.getInt(_cursorIndexOfRuku);
            final int _tmpHizbQuarter;
            _tmpHizbQuarter = _cursor.getInt(_cursorIndexOfHizbQuarter);
            final boolean _tmpSajda;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfSajda);
            _tmpSajda = _tmp != 0;
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpAudio;
            if (_cursor.isNull(_cursorIndexOfAudio)) {
              _tmpAudio = null;
            } else {
              _tmpAudio = _cursor.getString(_cursorIndexOfAudio);
            }
            _item = new AyahEntity(_tmpNumber,_tmpText,_tmpNumberInSurah,_tmpSurahNumber,_tmpJuz,_tmpManzil,_tmpPage,_tmpRuku,_tmpHizbQuarter,_tmpSajda,_tmpTranslation,_tmpAudio);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAyahsByPage(final int pageNumber,
      final Continuation<? super List<AyahEntity>> $completion) {
    final String _sql = "SELECT * FROM ayahs WHERE page = ? ORDER BY number ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, pageNumber);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<AyahEntity>>() {
      @Override
      @NonNull
      public List<AyahEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "number");
          final int _cursorIndexOfText = CursorUtil.getColumnIndexOrThrow(_cursor, "text");
          final int _cursorIndexOfNumberInSurah = CursorUtil.getColumnIndexOrThrow(_cursor, "numberInSurah");
          final int _cursorIndexOfSurahNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "surahNumber");
          final int _cursorIndexOfJuz = CursorUtil.getColumnIndexOrThrow(_cursor, "juz");
          final int _cursorIndexOfManzil = CursorUtil.getColumnIndexOrThrow(_cursor, "manzil");
          final int _cursorIndexOfPage = CursorUtil.getColumnIndexOrThrow(_cursor, "page");
          final int _cursorIndexOfRuku = CursorUtil.getColumnIndexOrThrow(_cursor, "ruku");
          final int _cursorIndexOfHizbQuarter = CursorUtil.getColumnIndexOrThrow(_cursor, "hizbQuarter");
          final int _cursorIndexOfSajda = CursorUtil.getColumnIndexOrThrow(_cursor, "sajda");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfAudio = CursorUtil.getColumnIndexOrThrow(_cursor, "audio");
          final List<AyahEntity> _result = new ArrayList<AyahEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AyahEntity _item;
            final int _tmpNumber;
            _tmpNumber = _cursor.getInt(_cursorIndexOfNumber);
            final String _tmpText;
            _tmpText = _cursor.getString(_cursorIndexOfText);
            final int _tmpNumberInSurah;
            _tmpNumberInSurah = _cursor.getInt(_cursorIndexOfNumberInSurah);
            final int _tmpSurahNumber;
            _tmpSurahNumber = _cursor.getInt(_cursorIndexOfSurahNumber);
            final int _tmpJuz;
            _tmpJuz = _cursor.getInt(_cursorIndexOfJuz);
            final int _tmpManzil;
            _tmpManzil = _cursor.getInt(_cursorIndexOfManzil);
            final int _tmpPage;
            _tmpPage = _cursor.getInt(_cursorIndexOfPage);
            final int _tmpRuku;
            _tmpRuku = _cursor.getInt(_cursorIndexOfRuku);
            final int _tmpHizbQuarter;
            _tmpHizbQuarter = _cursor.getInt(_cursorIndexOfHizbQuarter);
            final boolean _tmpSajda;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfSajda);
            _tmpSajda = _tmp != 0;
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpAudio;
            if (_cursor.isNull(_cursorIndexOfAudio)) {
              _tmpAudio = null;
            } else {
              _tmpAudio = _cursor.getString(_cursorIndexOfAudio);
            }
            _item = new AyahEntity(_tmpNumber,_tmpText,_tmpNumberInSurah,_tmpSurahNumber,_tmpJuz,_tmpManzil,_tmpPage,_tmpRuku,_tmpHizbQuarter,_tmpSajda,_tmpTranslation,_tmpAudio);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAyah(final int surahNumber, final int ayahNumber,
      final Continuation<? super AyahEntity> $completion) {
    final String _sql = "SELECT * FROM ayahs WHERE surahNumber = ? AND numberInSurah = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, surahNumber);
    _argIndex = 2;
    _statement.bindLong(_argIndex, ayahNumber);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<AyahEntity>() {
      @Override
      @Nullable
      public AyahEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "number");
          final int _cursorIndexOfText = CursorUtil.getColumnIndexOrThrow(_cursor, "text");
          final int _cursorIndexOfNumberInSurah = CursorUtil.getColumnIndexOrThrow(_cursor, "numberInSurah");
          final int _cursorIndexOfSurahNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "surahNumber");
          final int _cursorIndexOfJuz = CursorUtil.getColumnIndexOrThrow(_cursor, "juz");
          final int _cursorIndexOfManzil = CursorUtil.getColumnIndexOrThrow(_cursor, "manzil");
          final int _cursorIndexOfPage = CursorUtil.getColumnIndexOrThrow(_cursor, "page");
          final int _cursorIndexOfRuku = CursorUtil.getColumnIndexOrThrow(_cursor, "ruku");
          final int _cursorIndexOfHizbQuarter = CursorUtil.getColumnIndexOrThrow(_cursor, "hizbQuarter");
          final int _cursorIndexOfSajda = CursorUtil.getColumnIndexOrThrow(_cursor, "sajda");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfAudio = CursorUtil.getColumnIndexOrThrow(_cursor, "audio");
          final AyahEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpNumber;
            _tmpNumber = _cursor.getInt(_cursorIndexOfNumber);
            final String _tmpText;
            _tmpText = _cursor.getString(_cursorIndexOfText);
            final int _tmpNumberInSurah;
            _tmpNumberInSurah = _cursor.getInt(_cursorIndexOfNumberInSurah);
            final int _tmpSurahNumber;
            _tmpSurahNumber = _cursor.getInt(_cursorIndexOfSurahNumber);
            final int _tmpJuz;
            _tmpJuz = _cursor.getInt(_cursorIndexOfJuz);
            final int _tmpManzil;
            _tmpManzil = _cursor.getInt(_cursorIndexOfManzil);
            final int _tmpPage;
            _tmpPage = _cursor.getInt(_cursorIndexOfPage);
            final int _tmpRuku;
            _tmpRuku = _cursor.getInt(_cursorIndexOfRuku);
            final int _tmpHizbQuarter;
            _tmpHizbQuarter = _cursor.getInt(_cursorIndexOfHizbQuarter);
            final boolean _tmpSajda;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfSajda);
            _tmpSajda = _tmp != 0;
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpAudio;
            if (_cursor.isNull(_cursorIndexOfAudio)) {
              _tmpAudio = null;
            } else {
              _tmpAudio = _cursor.getString(_cursorIndexOfAudio);
            }
            _result = new AyahEntity(_tmpNumber,_tmpText,_tmpNumberInSurah,_tmpSurahNumber,_tmpJuz,_tmpManzil,_tmpPage,_tmpRuku,_tmpHizbQuarter,_tmpSajda,_tmpTranslation,_tmpAudio);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<AyahEntity>> searchAyahs(final String query) {
    final String _sql = "SELECT * FROM ayahs WHERE text LIKE '%' || ? || '%' OR translation LIKE '%' || ? || '%'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, query);
    _argIndex = 2;
    _statement.bindString(_argIndex, query);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"ayahs"}, new Callable<List<AyahEntity>>() {
      @Override
      @NonNull
      public List<AyahEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "number");
          final int _cursorIndexOfText = CursorUtil.getColumnIndexOrThrow(_cursor, "text");
          final int _cursorIndexOfNumberInSurah = CursorUtil.getColumnIndexOrThrow(_cursor, "numberInSurah");
          final int _cursorIndexOfSurahNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "surahNumber");
          final int _cursorIndexOfJuz = CursorUtil.getColumnIndexOrThrow(_cursor, "juz");
          final int _cursorIndexOfManzil = CursorUtil.getColumnIndexOrThrow(_cursor, "manzil");
          final int _cursorIndexOfPage = CursorUtil.getColumnIndexOrThrow(_cursor, "page");
          final int _cursorIndexOfRuku = CursorUtil.getColumnIndexOrThrow(_cursor, "ruku");
          final int _cursorIndexOfHizbQuarter = CursorUtil.getColumnIndexOrThrow(_cursor, "hizbQuarter");
          final int _cursorIndexOfSajda = CursorUtil.getColumnIndexOrThrow(_cursor, "sajda");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfAudio = CursorUtil.getColumnIndexOrThrow(_cursor, "audio");
          final List<AyahEntity> _result = new ArrayList<AyahEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final AyahEntity _item;
            final int _tmpNumber;
            _tmpNumber = _cursor.getInt(_cursorIndexOfNumber);
            final String _tmpText;
            _tmpText = _cursor.getString(_cursorIndexOfText);
            final int _tmpNumberInSurah;
            _tmpNumberInSurah = _cursor.getInt(_cursorIndexOfNumberInSurah);
            final int _tmpSurahNumber;
            _tmpSurahNumber = _cursor.getInt(_cursorIndexOfSurahNumber);
            final int _tmpJuz;
            _tmpJuz = _cursor.getInt(_cursorIndexOfJuz);
            final int _tmpManzil;
            _tmpManzil = _cursor.getInt(_cursorIndexOfManzil);
            final int _tmpPage;
            _tmpPage = _cursor.getInt(_cursorIndexOfPage);
            final int _tmpRuku;
            _tmpRuku = _cursor.getInt(_cursorIndexOfRuku);
            final int _tmpHizbQuarter;
            _tmpHizbQuarter = _cursor.getInt(_cursorIndexOfHizbQuarter);
            final boolean _tmpSajda;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfSajda);
            _tmpSajda = _tmp != 0;
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpAudio;
            if (_cursor.isNull(_cursorIndexOfAudio)) {
              _tmpAudio = null;
            } else {
              _tmpAudio = _cursor.getString(_cursorIndexOfAudio);
            }
            _item = new AyahEntity(_tmpNumber,_tmpText,_tmpNumberInSurah,_tmpSurahNumber,_tmpJuz,_tmpManzil,_tmpPage,_tmpRuku,_tmpHizbQuarter,_tmpSajda,_tmpTranslation,_tmpAudio);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<BookmarkEntity>> getAllBookmarks() {
    final String _sql = "SELECT * FROM bookmarks ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"bookmarks"}, new Callable<List<BookmarkEntity>>() {
      @Override
      @NonNull
      public List<BookmarkEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfSurahNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "surahNumber");
          final int _cursorIndexOfAyahNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "ayahNumber");
          final int _cursorIndexOfSurahName = CursorUtil.getColumnIndexOrThrow(_cursor, "surahName");
          final int _cursorIndexOfAyahText = CursorUtil.getColumnIndexOrThrow(_cursor, "ayahText");
          final int _cursorIndexOfNote = CursorUtil.getColumnIndexOrThrow(_cursor, "note");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final List<BookmarkEntity> _result = new ArrayList<BookmarkEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final BookmarkEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpSurahNumber;
            _tmpSurahNumber = _cursor.getInt(_cursorIndexOfSurahNumber);
            final int _tmpAyahNumber;
            _tmpAyahNumber = _cursor.getInt(_cursorIndexOfAyahNumber);
            final String _tmpSurahName;
            _tmpSurahName = _cursor.getString(_cursorIndexOfSurahName);
            final String _tmpAyahText;
            _tmpAyahText = _cursor.getString(_cursorIndexOfAyahText);
            final String _tmpNote;
            if (_cursor.isNull(_cursorIndexOfNote)) {
              _tmpNote = null;
            } else {
              _tmpNote = _cursor.getString(_cursorIndexOfNote);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            _item = new BookmarkEntity(_tmpId,_tmpSurahNumber,_tmpAyahNumber,_tmpSurahName,_tmpAyahText,_tmpNote,_tmpCreatedAt,_tmpUserId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Boolean> isBookmarked(final int surahNumber, final int ayahNumber) {
    final String _sql = "SELECT COUNT(*) > 0 FROM bookmarks WHERE surahNumber = ? AND ayahNumber = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, surahNumber);
    _argIndex = 2;
    _statement.bindLong(_argIndex, ayahNumber);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"bookmarks"}, new Callable<Boolean>() {
      @Override
      @NonNull
      public Boolean call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Boolean _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp != 0;
          } else {
            _result = false;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<ReadingProgressEntity> getReadingProgress() {
    final String _sql = "SELECT * FROM reading_progress WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"reading_progress"}, new Callable<ReadingProgressEntity>() {
      @Override
      @Nullable
      public ReadingProgressEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfSurahNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "surahNumber");
          final int _cursorIndexOfAyahNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "ayahNumber");
          final int _cursorIndexOfLastReadAt = CursorUtil.getColumnIndexOrThrow(_cursor, "lastReadAt");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final ReadingProgressEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpSurahNumber;
            _tmpSurahNumber = _cursor.getInt(_cursorIndexOfSurahNumber);
            final int _tmpAyahNumber;
            _tmpAyahNumber = _cursor.getInt(_cursorIndexOfAyahNumber);
            final long _tmpLastReadAt;
            _tmpLastReadAt = _cursor.getLong(_cursorIndexOfLastReadAt);
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            _result = new ReadingProgressEntity(_tmpId,_tmpSurahNumber,_tmpAyahNumber,_tmpLastReadAt,_tmpUserId);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<AyahEntity> getLastReadAyah() {
    final String _sql = "SELECT a.* FROM ayahs a INNER JOIN reading_progress rp ON a.surahNumber = rp.surahNumber AND a.numberInSurah = rp.ayahNumber WHERE rp.id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"ayahs",
        "reading_progress"}, new Callable<AyahEntity>() {
      @Override
      @Nullable
      public AyahEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "number");
          final int _cursorIndexOfText = CursorUtil.getColumnIndexOrThrow(_cursor, "text");
          final int _cursorIndexOfNumberInSurah = CursorUtil.getColumnIndexOrThrow(_cursor, "numberInSurah");
          final int _cursorIndexOfSurahNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "surahNumber");
          final int _cursorIndexOfJuz = CursorUtil.getColumnIndexOrThrow(_cursor, "juz");
          final int _cursorIndexOfManzil = CursorUtil.getColumnIndexOrThrow(_cursor, "manzil");
          final int _cursorIndexOfPage = CursorUtil.getColumnIndexOrThrow(_cursor, "page");
          final int _cursorIndexOfRuku = CursorUtil.getColumnIndexOrThrow(_cursor, "ruku");
          final int _cursorIndexOfHizbQuarter = CursorUtil.getColumnIndexOrThrow(_cursor, "hizbQuarter");
          final int _cursorIndexOfSajda = CursorUtil.getColumnIndexOrThrow(_cursor, "sajda");
          final int _cursorIndexOfTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "translation");
          final int _cursorIndexOfAudio = CursorUtil.getColumnIndexOrThrow(_cursor, "audio");
          final AyahEntity _result;
          if (_cursor.moveToFirst()) {
            final int _tmpNumber;
            _tmpNumber = _cursor.getInt(_cursorIndexOfNumber);
            final String _tmpText;
            _tmpText = _cursor.getString(_cursorIndexOfText);
            final int _tmpNumberInSurah;
            _tmpNumberInSurah = _cursor.getInt(_cursorIndexOfNumberInSurah);
            final int _tmpSurahNumber;
            _tmpSurahNumber = _cursor.getInt(_cursorIndexOfSurahNumber);
            final int _tmpJuz;
            _tmpJuz = _cursor.getInt(_cursorIndexOfJuz);
            final int _tmpManzil;
            _tmpManzil = _cursor.getInt(_cursorIndexOfManzil);
            final int _tmpPage;
            _tmpPage = _cursor.getInt(_cursorIndexOfPage);
            final int _tmpRuku;
            _tmpRuku = _cursor.getInt(_cursorIndexOfRuku);
            final int _tmpHizbQuarter;
            _tmpHizbQuarter = _cursor.getInt(_cursorIndexOfHizbQuarter);
            final boolean _tmpSajda;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfSajda);
            _tmpSajda = _tmp != 0;
            final String _tmpTranslation;
            if (_cursor.isNull(_cursorIndexOfTranslation)) {
              _tmpTranslation = null;
            } else {
              _tmpTranslation = _cursor.getString(_cursorIndexOfTranslation);
            }
            final String _tmpAudio;
            if (_cursor.isNull(_cursorIndexOfAudio)) {
              _tmpAudio = null;
            } else {
              _tmpAudio = _cursor.getString(_cursorIndexOfAudio);
            }
            _result = new AyahEntity(_tmpNumber,_tmpText,_tmpNumberInSurah,_tmpSurahNumber,_tmpJuz,_tmpManzil,_tmpPage,_tmpRuku,_tmpHizbQuarter,_tmpSajda,_tmpTranslation,_tmpAudio);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<KhatmaProgressEntity> getActiveKhatma() {
    final String _sql = "SELECT * FROM khatma_progress WHERE isCompleted = 0 ORDER BY startDate DESC LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"khatma_progress"}, new Callable<KhatmaProgressEntity>() {
      @Override
      @Nullable
      public KhatmaProgressEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfTargetDays = CursorUtil.getColumnIndexOrThrow(_cursor, "targetDays");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfEndDate = CursorUtil.getColumnIndexOrThrow(_cursor, "endDate");
          final int _cursorIndexOfCurrentSurah = CursorUtil.getColumnIndexOrThrow(_cursor, "currentSurah");
          final int _cursorIndexOfCurrentAyah = CursorUtil.getColumnIndexOrThrow(_cursor, "currentAyah");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfDailyTarget = CursorUtil.getColumnIndexOrThrow(_cursor, "dailyTarget");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final KhatmaProgressEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final int _tmpTargetDays;
            _tmpTargetDays = _cursor.getInt(_cursorIndexOfTargetDays);
            final long _tmpStartDate;
            _tmpStartDate = _cursor.getLong(_cursorIndexOfStartDate);
            final long _tmpEndDate;
            _tmpEndDate = _cursor.getLong(_cursorIndexOfEndDate);
            final int _tmpCurrentSurah;
            _tmpCurrentSurah = _cursor.getInt(_cursorIndexOfCurrentSurah);
            final int _tmpCurrentAyah;
            _tmpCurrentAyah = _cursor.getInt(_cursorIndexOfCurrentAyah);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpDailyTarget;
            _tmpDailyTarget = _cursor.getInt(_cursorIndexOfDailyTarget);
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            _result = new KhatmaProgressEntity(_tmpId,_tmpName,_tmpTargetDays,_tmpStartDate,_tmpEndDate,_tmpCurrentSurah,_tmpCurrentAyah,_tmpIsCompleted,_tmpDailyTarget,_tmpUserId);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<KhatmaProgressEntity>> getAllKhatmas() {
    final String _sql = "SELECT * FROM khatma_progress ORDER BY startDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"khatma_progress"}, new Callable<List<KhatmaProgressEntity>>() {
      @Override
      @NonNull
      public List<KhatmaProgressEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfTargetDays = CursorUtil.getColumnIndexOrThrow(_cursor, "targetDays");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfEndDate = CursorUtil.getColumnIndexOrThrow(_cursor, "endDate");
          final int _cursorIndexOfCurrentSurah = CursorUtil.getColumnIndexOrThrow(_cursor, "currentSurah");
          final int _cursorIndexOfCurrentAyah = CursorUtil.getColumnIndexOrThrow(_cursor, "currentAyah");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfDailyTarget = CursorUtil.getColumnIndexOrThrow(_cursor, "dailyTarget");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final List<KhatmaProgressEntity> _result = new ArrayList<KhatmaProgressEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final KhatmaProgressEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final int _tmpTargetDays;
            _tmpTargetDays = _cursor.getInt(_cursorIndexOfTargetDays);
            final long _tmpStartDate;
            _tmpStartDate = _cursor.getLong(_cursorIndexOfStartDate);
            final long _tmpEndDate;
            _tmpEndDate = _cursor.getLong(_cursorIndexOfEndDate);
            final int _tmpCurrentSurah;
            _tmpCurrentSurah = _cursor.getInt(_cursorIndexOfCurrentSurah);
            final int _tmpCurrentAyah;
            _tmpCurrentAyah = _cursor.getInt(_cursorIndexOfCurrentAyah);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpDailyTarget;
            _tmpDailyTarget = _cursor.getInt(_cursorIndexOfDailyTarget);
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            _item = new KhatmaProgressEntity(_tmpId,_tmpName,_tmpTargetDays,_tmpStartDate,_tmpEndDate,_tmpCurrentSurah,_tmpCurrentAyah,_tmpIsCompleted,_tmpDailyTarget,_tmpUserId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getKhatmaById(final long id,
      final Continuation<? super KhatmaProgressEntity> $completion) {
    final String _sql = "SELECT * FROM khatma_progress WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<KhatmaProgressEntity>() {
      @Override
      @Nullable
      public KhatmaProgressEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfTargetDays = CursorUtil.getColumnIndexOrThrow(_cursor, "targetDays");
          final int _cursorIndexOfStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "startDate");
          final int _cursorIndexOfEndDate = CursorUtil.getColumnIndexOrThrow(_cursor, "endDate");
          final int _cursorIndexOfCurrentSurah = CursorUtil.getColumnIndexOrThrow(_cursor, "currentSurah");
          final int _cursorIndexOfCurrentAyah = CursorUtil.getColumnIndexOrThrow(_cursor, "currentAyah");
          final int _cursorIndexOfIsCompleted = CursorUtil.getColumnIndexOrThrow(_cursor, "isCompleted");
          final int _cursorIndexOfDailyTarget = CursorUtil.getColumnIndexOrThrow(_cursor, "dailyTarget");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "userId");
          final KhatmaProgressEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final int _tmpTargetDays;
            _tmpTargetDays = _cursor.getInt(_cursorIndexOfTargetDays);
            final long _tmpStartDate;
            _tmpStartDate = _cursor.getLong(_cursorIndexOfStartDate);
            final long _tmpEndDate;
            _tmpEndDate = _cursor.getLong(_cursorIndexOfEndDate);
            final int _tmpCurrentSurah;
            _tmpCurrentSurah = _cursor.getInt(_cursorIndexOfCurrentSurah);
            final int _tmpCurrentAyah;
            _tmpCurrentAyah = _cursor.getInt(_cursorIndexOfCurrentAyah);
            final boolean _tmpIsCompleted;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsCompleted);
            _tmpIsCompleted = _tmp != 0;
            final int _tmpDailyTarget;
            _tmpDailyTarget = _cursor.getInt(_cursorIndexOfDailyTarget);
            final String _tmpUserId;
            if (_cursor.isNull(_cursorIndexOfUserId)) {
              _tmpUserId = null;
            } else {
              _tmpUserId = _cursor.getString(_cursorIndexOfUserId);
            }
            _result = new KhatmaProgressEntity(_tmpId,_tmpName,_tmpTargetDays,_tmpStartDate,_tmpEndDate,_tmpCurrentSurah,_tmpCurrentAyah,_tmpIsCompleted,_tmpDailyTarget,_tmpUserId);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<SurahEntity>> getFavoriteSurahs() {
    final String _sql = "SELECT s.* FROM surahs s INNER JOIN favorite_surahs f ON s.number = f.surahNumber ORDER BY f.addedAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"surahs",
        "favorite_surahs"}, new Callable<List<SurahEntity>>() {
      @Override
      @NonNull
      public List<SurahEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "number");
          final int _cursorIndexOfName = CursorUtil.getColumnIndexOrThrow(_cursor, "name");
          final int _cursorIndexOfEnglishName = CursorUtil.getColumnIndexOrThrow(_cursor, "englishName");
          final int _cursorIndexOfEnglishNameTranslation = CursorUtil.getColumnIndexOrThrow(_cursor, "englishNameTranslation");
          final int _cursorIndexOfNumberOfAyahs = CursorUtil.getColumnIndexOrThrow(_cursor, "numberOfAyahs");
          final int _cursorIndexOfRevelationType = CursorUtil.getColumnIndexOrThrow(_cursor, "revelationType");
          final List<SurahEntity> _result = new ArrayList<SurahEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SurahEntity _item;
            final int _tmpNumber;
            _tmpNumber = _cursor.getInt(_cursorIndexOfNumber);
            final String _tmpName;
            _tmpName = _cursor.getString(_cursorIndexOfName);
            final String _tmpEnglishName;
            _tmpEnglishName = _cursor.getString(_cursorIndexOfEnglishName);
            final String _tmpEnglishNameTranslation;
            _tmpEnglishNameTranslation = _cursor.getString(_cursorIndexOfEnglishNameTranslation);
            final int _tmpNumberOfAyahs;
            _tmpNumberOfAyahs = _cursor.getInt(_cursorIndexOfNumberOfAyahs);
            final String _tmpRevelationType;
            _tmpRevelationType = _cursor.getString(_cursorIndexOfRevelationType);
            _item = new SurahEntity(_tmpNumber,_tmpName,_tmpEnglishName,_tmpEnglishNameTranslation,_tmpNumberOfAyahs,_tmpRevelationType);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<Boolean> isSurahFavorite(final int surahNumber) {
    final String _sql = "SELECT COUNT(*) > 0 FROM favorite_surahs WHERE surahNumber = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, surahNumber);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"favorite_surahs"}, new Callable<Boolean>() {
      @Override
      @NonNull
      public Boolean call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Boolean _result;
          if (_cursor.moveToFirst()) {
            final int _tmp;
            _tmp = _cursor.getInt(0);
            _result = _tmp != 0;
          } else {
            _result = false;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
