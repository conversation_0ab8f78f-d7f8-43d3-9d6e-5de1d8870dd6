package com.muslimcore.di;

import android.content.Context;
import com.muslimcore.data.local.database.MuslimCoreDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideMuslimCoreDatabaseFactory implements Factory<MuslimCoreDatabase> {
  private final Provider<Context> contextProvider;

  public DatabaseModule_ProvideMuslimCoreDatabaseFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public MuslimCoreDatabase get() {
    return provideMuslimCoreDatabase(contextProvider.get());
  }

  public static DatabaseModule_ProvideMuslimCoreDatabaseFactory create(
      Provider<Context> contextProvider) {
    return new DatabaseModule_ProvideMuslimCoreDatabaseFactory(contextProvider);
  }

  public static MuslimCoreDatabase provideMuslimCoreDatabase(Context context) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideMuslimCoreDatabase(context));
  }
}
