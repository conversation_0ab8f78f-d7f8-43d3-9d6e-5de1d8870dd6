1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.muslimcore"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Internet permission for API calls -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Muslim Core\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Muslim Core\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Muslim Core\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Muslim Core\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
14-->C:\Muslim Core\app\src\main\AndroidManifest.xml:8:5-79
14-->C:\Muslim Core\app\src\main\AndroidManifest.xml:8:22-76
15    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
15-->C:\Muslim Core\app\src\main\AndroidManifest.xml:9:5-81
15-->C:\Muslim Core\app\src\main\AndroidManifest.xml:9:22-78
16    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
16-->C:\Muslim Core\app\src\main\AndroidManifest.xml:10:5-77
16-->C:\Muslim Core\app\src\main\AndroidManifest.xml:10:22-74
17    <uses-permission android:name="android.permission.WAKE_LOCK" />
17-->C:\Muslim Core\app\src\main\AndroidManifest.xml:11:5-68
17-->C:\Muslim Core\app\src\main\AndroidManifest.xml:11:22-65
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
18-->C:\Muslim Core\app\src\main\AndroidManifest.xml:12:5-77
18-->C:\Muslim Core\app\src\main\AndroidManifest.xml:12:22-74
19    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
19-->C:\Muslim Core\app\src\main\AndroidManifest.xml:13:5-81
19-->C:\Muslim Core\app\src\main\AndroidManifest.xml:13:22-78
20    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
20-->C:\Muslim Core\app\src\main\AndroidManifest.xml:14:5-78
20-->C:\Muslim Core\app\src\main\AndroidManifest.xml:14:22-75
21
22    <!-- Additional permissions -->
23    <uses-permission android:name="android.permission.VIBRATE" />
23-->C:\Muslim Core\app\src\main\AndroidManifest.xml:17:5-66
23-->C:\Muslim Core\app\src\main\AndroidManifest.xml:17:22-63
24
25    <uses-feature
25-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
26        android:glEsVersion="0x00020000"
26-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
27        android:required="true" />
27-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
28
29    <queries>
29-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
30
31        <!-- Needs to be explicitly declared on Android R+ -->
32        <package android:name="com.google.android.apps.maps" />
32-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
32-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
33    </queries> <!-- Required by older versions of Google Play services to create IID tokens -->
34    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
34-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:26:5-82
34-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:26:22-79
35    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
35-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:5-79
35-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:25:22-76
36    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
36-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:5-88
36-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:26:22-85
37    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
37-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:5-82
37-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:27:22-79
38    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
38-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:5-110
38-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:26:22-107
39
40    <permission
40-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
41        android:name="com.muslimcore.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
41-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
42        android:protectionLevel="signature" />
42-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
43
44    <uses-permission android:name="com.muslimcore.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
44-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
44-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
45
46    <application
46-->C:\Muslim Core\app\src\main\AndroidManifest.xml:19:5-106:19
47        android:name="com.muslimcore.MuslimCoreApplication"
47-->C:\Muslim Core\app\src\main\AndroidManifest.xml:20:9-46
48        android:allowBackup="true"
48-->C:\Muslim Core\app\src\main\AndroidManifest.xml:21:9-35
49        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
49-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\29aa2070c5f46de89120ba6e20f2606d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
50        android:dataExtractionRules="@xml/data_extraction_rules"
50-->C:\Muslim Core\app\src\main\AndroidManifest.xml:22:9-65
51        android:debuggable="true"
52        android:extractNativeLibs="false"
53        android:fullBackupContent="@xml/backup_rules"
53-->C:\Muslim Core\app\src\main\AndroidManifest.xml:23:9-54
54        android:icon="@mipmap/ic_launcher"
54-->C:\Muslim Core\app\src\main\AndroidManifest.xml:24:9-43
55        android:label="@string/app_name"
55-->C:\Muslim Core\app\src\main\AndroidManifest.xml:25:9-41
56        android:roundIcon="@mipmap/ic_launcher_round"
56-->C:\Muslim Core\app\src\main\AndroidManifest.xml:26:9-54
57        android:supportsRtl="true"
57-->C:\Muslim Core\app\src\main\AndroidManifest.xml:27:9-35
58        android:testOnly="true"
59        android:theme="@style/Theme.MuslimCore" >
59-->C:\Muslim Core\app\src\main\AndroidManifest.xml:28:9-48
60
61        <!-- Main Activity -->
62        <activity
62-->C:\Muslim Core\app\src\main\AndroidManifest.xml:32:9-41:20
63            android:name="com.muslimcore.presentation.MainActivity"
63-->C:\Muslim Core\app\src\main\AndroidManifest.xml:33:13-54
64            android:exported="true"
64-->C:\Muslim Core\app\src\main\AndroidManifest.xml:34:13-36
65            android:screenOrientation="portrait"
65-->C:\Muslim Core\app\src\main\AndroidManifest.xml:35:13-49
66            android:theme="@style/Theme.MuslimCore.NoActionBar" >
66-->C:\Muslim Core\app\src\main\AndroidManifest.xml:36:13-64
67            <intent-filter>
67-->C:\Muslim Core\app\src\main\AndroidManifest.xml:37:13-40:29
68                <action android:name="android.intent.action.MAIN" />
68-->C:\Muslim Core\app\src\main\AndroidManifest.xml:38:17-69
68-->C:\Muslim Core\app\src\main\AndroidManifest.xml:38:25-66
69
70                <category android:name="android.intent.category.LAUNCHER" />
70-->C:\Muslim Core\app\src\main\AndroidManifest.xml:39:17-77
70-->C:\Muslim Core\app\src\main\AndroidManifest.xml:39:27-74
71            </intent-filter>
72        </activity>
73
74        <!-- Firebase Messaging Service -->
75        <service
75-->C:\Muslim Core\app\src\main\AndroidManifest.xml:46:9-52:19
76            android:name="com.muslimcore.data.services.MuslimCoreMessagingService"
76-->C:\Muslim Core\app\src\main\AndroidManifest.xml:47:13-69
77            android:exported="false" >
77-->C:\Muslim Core\app\src\main\AndroidManifest.xml:48:13-37
78            <intent-filter>
78-->C:\Muslim Core\app\src\main\AndroidManifest.xml:49:13-51:29
79                <action android:name="com.google.firebase.MESSAGING_EVENT" />
79-->C:\Muslim Core\app\src\main\AndroidManifest.xml:50:17-78
79-->C:\Muslim Core\app\src\main\AndroidManifest.xml:50:25-75
80            </intent-filter>
81        </service>
82
83        <!-- Prayer Alert Activity -->
84        <activity
84-->C:\Muslim Core\app\src\main\AndroidManifest.xml:55:9-60:67
85            android:name="com.muslimcore.presentation.activities.PrayerAlertActivity"
85-->C:\Muslim Core\app\src\main\AndroidManifest.xml:56:13-72
86            android:exported="false"
86-->C:\Muslim Core\app\src\main\AndroidManifest.xml:57:13-37
87            android:launchMode="singleTop"
87-->C:\Muslim Core\app\src\main\AndroidManifest.xml:58:13-43
88            android:screenOrientation="portrait"
88-->C:\Muslim Core\app\src\main\AndroidManifest.xml:59:13-49
89            android:theme="@style/Theme.MuslimCore.NoActionBar" />
89-->C:\Muslim Core\app\src\main\AndroidManifest.xml:60:13-64
90
91        <!-- Prayer Background Service -->
92        <service
92-->C:\Muslim Core\app\src\main\AndroidManifest.xml:63:9-67:58
93            android:name="com.muslimcore.presentation.services.PrayerService"
93-->C:\Muslim Core\app\src\main\AndroidManifest.xml:64:13-64
94            android:enabled="true"
94-->C:\Muslim Core\app\src\main\AndroidManifest.xml:65:13-35
95            android:exported="false"
95-->C:\Muslim Core\app\src\main\AndroidManifest.xml:66:13-37
96            android:foregroundServiceType="specialUse" />
96-->C:\Muslim Core\app\src\main\AndroidManifest.xml:67:13-55
97
98        <!-- Prayer Notification Service -->
99        <service
99-->C:\Muslim Core\app\src\main\AndroidManifest.xml:70:9-73:40
100            android:name="com.muslimcore.data.services.PrayerNotificationService"
100-->C:\Muslim Core\app\src\main\AndroidManifest.xml:71:13-68
101            android:enabled="true"
101-->C:\Muslim Core\app\src\main\AndroidManifest.xml:72:13-35
102            android:exported="false" />
102-->C:\Muslim Core\app\src\main\AndroidManifest.xml:73:13-37
103
104        <!-- Boot Receiver -->
105        <receiver
105-->C:\Muslim Core\app\src\main\AndroidManifest.xml:76:9-86:20
106            android:name="com.muslimcore.presentation.receivers.BootReceiver"
106-->C:\Muslim Core\app\src\main\AndroidManifest.xml:77:13-64
107            android:enabled="true"
107-->C:\Muslim Core\app\src\main\AndroidManifest.xml:78:13-35
108            android:exported="true" >
108-->C:\Muslim Core\app\src\main\AndroidManifest.xml:79:13-36
109            <intent-filter android:priority="1000" >
109-->C:\Muslim Core\app\src\main\AndroidManifest.xml:80:13-85:29
109-->C:\Muslim Core\app\src\main\AndroidManifest.xml:80:28-51
110                <action android:name="android.intent.action.BOOT_COMPLETED" />
110-->C:\Muslim Core\app\src\main\AndroidManifest.xml:81:17-79
110-->C:\Muslim Core\app\src\main\AndroidManifest.xml:81:25-76
111                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
111-->C:\Muslim Core\app\src\main\AndroidManifest.xml:82:17-84
111-->C:\Muslim Core\app\src\main\AndroidManifest.xml:82:25-81
112                <action android:name="android.intent.action.PACKAGE_REPLACED" />
112-->C:\Muslim Core\app\src\main\AndroidManifest.xml:83:17-81
112-->C:\Muslim Core\app\src\main\AndroidManifest.xml:83:25-78
113
114                <data android:scheme="package" />
114-->C:\Muslim Core\app\src\main\AndroidManifest.xml:84:17-50
114-->C:\Muslim Core\app\src\main\AndroidManifest.xml:84:23-47
115            </intent-filter>
116        </receiver>
117
118        <!-- Prayer Alarm Receiver -->
119        <receiver
119-->C:\Muslim Core\app\src\main\AndroidManifest.xml:89:9-92:40
120            android:name="com.muslimcore.data.receivers.PrayerAlarmReceiver"
120-->C:\Muslim Core\app\src\main\AndroidManifest.xml:90:13-63
121            android:enabled="true"
121-->C:\Muslim Core\app\src\main\AndroidManifest.xml:91:13-35
122            android:exported="false" />
122-->C:\Muslim Core\app\src\main\AndroidManifest.xml:92:13-37
123
124        <!-- Disable automatic WorkManager initialization -->
125        <provider
126            android:name="androidx.startup.InitializationProvider"
126-->C:\Muslim Core\app\src\main\AndroidManifest.xml:96:13-67
127            android:authorities="com.muslimcore.androidx-startup"
127-->C:\Muslim Core\app\src\main\AndroidManifest.xml:97:13-68
128            android:exported="false" >
128-->C:\Muslim Core\app\src\main\AndroidManifest.xml:98:13-37
129            <meta-data
129-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39a8a2aca2bad19af9fb513e99abd016\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.emoji2.text.EmojiCompatInitializer"
130-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39a8a2aca2bad19af9fb513e99abd016\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
131                android:value="androidx.startup" />
131-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\39a8a2aca2bad19af9fb513e99abd016\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
132            <meta-data
132-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\683da4f7ef5696e7dcd83bd164cc9897\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
133                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
133-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\683da4f7ef5696e7dcd83bd164cc9897\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
134                android:value="androidx.startup" />
134-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\683da4f7ef5696e7dcd83bd164cc9897\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
135            <meta-data
135-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
136                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
136-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
137                android:value="androidx.startup" />
137-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
138        </provider>
139
140        <!-- Needs to be explicitly declared on P+ -->
141        <uses-library
141-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
142            android:name="org.apache.http.legacy"
142-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
143            android:required="false" />
143-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ce2d8fddf65a1d1c885d98f45b4a48f\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
144
145        <service
145-->[com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:9:9-15:19
146            android:name="com.google.firebase.components.ComponentDiscoveryService"
146-->[com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:10:13-84
147            android:directBootAware="true"
147-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
148            android:exported="false" >
148-->[com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:11:13-37
149            <meta-data
149-->[com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:12:13-14:85
150                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
150-->[com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:13:17-129
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[com.google.firebase:firebase-firestore-ktx:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6cb3da3a0e28fbb3430bbd354b98bd68\transformed\firebase-firestore-ktx-24.9.1\AndroidManifest.xml:14:17-82
152            <meta-data
152-->[com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:17:13-19:85
153                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
153-->[com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:18:17-122
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:19:17-82
155            <meta-data
155-->[com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:20:13-22:85
156                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
156-->[com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:21:17-111
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[com.google.firebase:firebase-firestore:24.9.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bc763f29c7783602ce54f376238f5573\transformed\firebase-firestore-24.9.1\AndroidManifest.xml:22:17-82
158            <meta-data
158-->[com.google.firebase:firebase-messaging-ktx:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f02b071850434d6569dac072ad31c2fd\transformed\firebase-messaging-ktx-23.3.1\AndroidManifest.xml:26:13-28:85
159                android:name="com.google.firebase.components:com.google.firebase.messaging.ktx.FirebaseMessagingLegacyRegistrar"
159-->[com.google.firebase:firebase-messaging-ktx:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f02b071850434d6569dac072ad31c2fd\transformed\firebase-messaging-ktx-23.3.1\AndroidManifest.xml:27:17-129
160                android:value="com.google.firebase.components.ComponentRegistrar" />
160-->[com.google.firebase:firebase-messaging-ktx:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f02b071850434d6569dac072ad31c2fd\transformed\firebase-messaging-ktx-23.3.1\AndroidManifest.xml:28:17-82
161            <meta-data
161-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:57:13-59:85
162                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
162-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:58:17-122
163                android:value="com.google.firebase.components.ComponentRegistrar" />
163-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:59:17-82
164            <meta-data
164-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:60:13-62:85
165                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
165-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:61:17-119
166                android:value="com.google.firebase.components.ComponentRegistrar" />
166-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:62:17-82
167            <meta-data
167-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a121f920b99ddcf8e2e1aac0ac05b0f9\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:11:13-13:85
168                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
168-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a121f920b99ddcf8e2e1aac0ac05b0f9\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:12:17-119
169                android:value="com.google.firebase.components.ComponentRegistrar" />
169-->[com.google.firebase:firebase-auth-ktx:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a121f920b99ddcf8e2e1aac0ac05b0f9\transformed\firebase-auth-ktx-22.3.0\AndroidManifest.xml:13:17-82
170            <meta-data
170-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:69:13-71:85
171                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
171-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:70:17-109
172                android:value="com.google.firebase.components.ComponentRegistrar" />
172-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:71:17-82
173            <meta-data
173-->[com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a29fdd56c9ef7f7a8d2faae48bfc03d\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:12:13-14:85
174                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.ktx.FirebaseConfigLegacyRegistrar"
174-->[com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a29fdd56c9ef7f7a8d2faae48bfc03d\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:13:17-129
175                android:value="com.google.firebase.components.ComponentRegistrar" />
175-->[com.google.firebase:firebase-config-ktx:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1a29fdd56c9ef7f7a8d2faae48bfc03d\transformed\firebase-config-ktx-21.6.0\AndroidManifest.xml:14:17-82
176            <meta-data
176-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\462349714f38d054982a20853c15eade\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:11:13-13:85
177                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
177-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\462349714f38d054982a20853c15eade\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:12:17-129
178                android:value="com.google.firebase.components.ComponentRegistrar" />
178-->[com.google.firebase:firebase-analytics-ktx:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\462349714f38d054982a20853c15eade\transformed\firebase-analytics-ktx-21.5.0\AndroidManifest.xml:13:17-82
179            <meta-data
179-->[com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:29:13-31:85
180                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.FirebaseRemoteConfigKtxRegistrar"
180-->[com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:30:17-128
181                android:value="com.google.firebase.components.ComponentRegistrar" />
181-->[com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:31:17-82
182            <meta-data
182-->[com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:32:13-34:85
183                android:name="com.google.firebase.components:com.google.firebase.remoteconfig.RemoteConfigRegistrar"
183-->[com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:33:17-117
184                android:value="com.google.firebase.components.ComponentRegistrar" />
184-->[com.google.firebase:firebase-config:21.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d7bd6579bd35503adb2fe395d71ea3e\transformed\firebase-config-21.6.0\AndroidManifest.xml:34:17-82
185            <meta-data
185-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:37:13-39:85
186                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
186-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:38:17-139
187                android:value="com.google.firebase.components.ComponentRegistrar" />
187-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:39:17-82
188            <meta-data
188-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
189                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
189-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
190                android:value="com.google.firebase.components.ComponentRegistrar" />
190-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
191            <meta-data
191-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
192                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
192-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
193                android:value="com.google.firebase.components.ComponentRegistrar" />
193-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4550ebb1f57cdacda2347951a8b355e7\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
194            <meta-data
194-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce87eaf2450b4f0f51a49634d42a1887\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
195                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
195-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce87eaf2450b4f0f51a49634d42a1887\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
196                android:value="com.google.firebase.components.ComponentRegistrar" />
196-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce87eaf2450b4f0f51a49634d42a1887\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
197            <meta-data
197-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
198                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
198-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
199                android:value="com.google.firebase.components.ComponentRegistrar" />
199-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
200            <meta-data
200-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5565bd1e3d8bedbb48c3d9ebd009d2d6\transformed\firebase-abt-21.1.1\AndroidManifest.xml:12:13-14:85
201                android:name="com.google.firebase.components:com.google.firebase.abt.component.AbtRegistrar"
201-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5565bd1e3d8bedbb48c3d9ebd009d2d6\transformed\firebase-abt-21.1.1\AndroidManifest.xml:13:17-109
202                android:value="com.google.firebase.components.ComponentRegistrar" />
202-->[com.google.firebase:firebase-abt:21.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5565bd1e3d8bedbb48c3d9ebd009d2d6\transformed\firebase-abt-21.1.1\AndroidManifest.xml:14:17-82
203            <meta-data
203-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ca3bb862412792bd96083ec00cacda6\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
204                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
204-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ca3bb862412792bd96083ec00cacda6\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
205                android:value="com.google.firebase.components.ComponentRegistrar" />
205-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.12\transforms\3ca3bb862412792bd96083ec00cacda6\transformed\firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
206        </service>
207
208        <receiver
208-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:29:9-40:20
209            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
209-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:30:13-78
210            android:exported="true"
210-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:31:13-36
211            android:permission="com.google.android.c2dm.permission.SEND" >
211-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:32:13-73
212            <intent-filter>
212-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:33:13-35:29
213                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
213-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:34:17-81
213-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:34:25-78
214            </intent-filter>
215
216            <meta-data
216-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:37:13-39:40
217                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
217-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:38:17-92
218                android:value="true" />
218-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:39:17-37
219        </receiver>
220        <!--
221             FirebaseMessagingService performs security checks at runtime,
222             but set to not exported to explicitly avoid allowing another app to call it.
223        -->
224        <service
224-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:46:9-53:19
225            android:name="com.google.firebase.messaging.FirebaseMessagingService"
225-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:47:13-82
226            android:directBootAware="true"
226-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:48:13-43
227            android:exported="false" >
227-->[com.google.firebase:firebase-messaging:23.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\657d4b970ca786a19c67b9546b583e6a\transformed\firebase-messaging-23.3.1\AndroidManifest.xml:49:13-37
228            <intent-filter android:priority="-500" >
228-->C:\Muslim Core\app\src\main\AndroidManifest.xml:49:13-51:29
229                <action android:name="com.google.firebase.MESSAGING_EVENT" />
229-->C:\Muslim Core\app\src\main\AndroidManifest.xml:50:17-78
229-->C:\Muslim Core\app\src\main\AndroidManifest.xml:50:25-75
230            </intent-filter>
231        </service>
232
233        <activity
233-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:29:9-46:20
234            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
234-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:30:13-80
235            android:excludeFromRecents="true"
235-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:31:13-46
236            android:exported="true"
236-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:32:13-36
237            android:launchMode="singleTask"
237-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:33:13-44
238            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
238-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:34:13-72
239            <intent-filter>
239-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:35:13-45:29
240                <action android:name="android.intent.action.VIEW" />
240-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
240-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
241
242                <category android:name="android.intent.category.DEFAULT" />
242-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
242-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
243                <category android:name="android.intent.category.BROWSABLE" />
243-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
243-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
244
245                <data
245-->C:\Muslim Core\app\src\main\AndroidManifest.xml:84:17-50
246                    android:host="firebase.auth"
247                    android:path="/"
248                    android:scheme="genericidp" />
248-->C:\Muslim Core\app\src\main\AndroidManifest.xml:84:23-47
249            </intent-filter>
250        </activity>
251        <activity
251-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:47:9-64:20
252            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
252-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:48:13-79
253            android:excludeFromRecents="true"
253-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:49:13-46
254            android:exported="true"
254-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:50:13-36
255            android:launchMode="singleTask"
255-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:51:13-44
256            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
256-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:52:13-72
257            <intent-filter>
257-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:53:13-63:29
258                <action android:name="android.intent.action.VIEW" />
258-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
258-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
259
260                <category android:name="android.intent.category.DEFAULT" />
260-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
260-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
261                <category android:name="android.intent.category.BROWSABLE" />
261-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
261-->[com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4f96e904e00d58c3ce6afc4255acc8\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
262
263                <data
263-->C:\Muslim Core\app\src\main\AndroidManifest.xml:84:17-50
264                    android:host="firebase.auth"
265                    android:path="/"
266                    android:scheme="recaptcha" />
266-->C:\Muslim Core\app\src\main\AndroidManifest.xml:84:23-47
267            </intent-filter>
268        </activity>
269        <activity
269-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd229cba7731028eaebcf7d452c4e6dc\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
270            android:name="com.google.android.gms.common.api.GoogleApiActivity"
270-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd229cba7731028eaebcf7d452c4e6dc\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
271            android:exported="false"
271-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd229cba7731028eaebcf7d452c4e6dc\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
272            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
272-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\bd229cba7731028eaebcf7d452c4e6dc\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
273
274        <property
274-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:30:9-32:61
275            android:name="android.adservices.AD_SERVICES_CONFIG"
275-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:31:13-65
276            android:resource="@xml/ga_ad_services_config" />
276-->[com.google.android.gms:play-services-measurement-api:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c42e3f53f251f72190ffef6945138b19\transformed\play-services-measurement-api-21.5.0\AndroidManifest.xml:32:13-58
277
278        <provider
278-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
279            android:name="com.google.firebase.provider.FirebaseInitProvider"
279-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
280            android:authorities="com.muslimcore.firebaseinitprovider"
280-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
281            android:directBootAware="true"
281-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
282            android:exported="false"
282-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
283            android:initOrder="100" />
283-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\29a9afe92f618d0e0b754a9bfb76a586\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
284
285        <service
285-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:39:9-45:35
286            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
286-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:40:13-88
287            android:directBootAware="false"
287-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:41:13-44
288            android:enabled="@bool/enable_system_alarm_service_default"
288-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:42:13-72
289            android:exported="false" />
289-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:43:13-37
290        <service
290-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:46:9-52:35
291            android:name="androidx.work.impl.background.systemjob.SystemJobService"
291-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:47:13-84
292            android:directBootAware="false"
292-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:48:13-44
293            android:enabled="@bool/enable_system_job_service_default"
293-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:49:13-70
294            android:exported="true"
294-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:50:13-36
295            android:permission="android.permission.BIND_JOB_SERVICE" />
295-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:51:13-69
296        <service
296-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:53:9-59:35
297            android:name="androidx.work.impl.foreground.SystemForegroundService"
297-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:54:13-81
298            android:directBootAware="false"
298-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:55:13-44
299            android:enabled="@bool/enable_system_foreground_service_default"
299-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:56:13-77
300            android:exported="false" />
300-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:57:13-37
301
302        <receiver
302-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:61:9-66:35
303            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
303-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:62:13-88
304            android:directBootAware="false"
304-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:63:13-44
305            android:enabled="true"
305-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:64:13-35
306            android:exported="false" />
306-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:65:13-37
307        <receiver
307-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:67:9-77:20
308            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
308-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:68:13-106
309            android:directBootAware="false"
309-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:69:13-44
310            android:enabled="false"
310-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:70:13-36
311            android:exported="false" >
311-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:71:13-37
312            <intent-filter>
312-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:73:13-76:29
313                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
313-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:17-87
313-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:74:25-84
314                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
314-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:17-90
314-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:75:25-87
315            </intent-filter>
316        </receiver>
317        <receiver
317-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:78:9-88:20
318            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
318-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:79:13-104
319            android:directBootAware="false"
319-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:80:13-44
320            android:enabled="false"
320-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:81:13-36
321            android:exported="false" >
321-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:82:13-37
322            <intent-filter>
322-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:84:13-87:29
323                <action android:name="android.intent.action.BATTERY_OKAY" />
323-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:17-77
323-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:85:25-74
324                <action android:name="android.intent.action.BATTERY_LOW" />
324-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:17-76
324-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:86:25-73
325            </intent-filter>
326        </receiver>
327        <receiver
327-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:89:9-99:20
328            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
328-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:90:13-104
329            android:directBootAware="false"
329-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:91:13-44
330            android:enabled="false"
330-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:92:13-36
331            android:exported="false" >
331-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:93:13-37
332            <intent-filter>
332-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:95:13-98:29
333                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
333-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:17-83
333-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:96:25-80
334                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
334-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:17-82
334-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:97:25-79
335            </intent-filter>
336        </receiver>
337        <receiver
337-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:100:9-109:20
338            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
338-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:101:13-103
339            android:directBootAware="false"
339-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:102:13-44
340            android:enabled="false"
340-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:103:13-36
341            android:exported="false" >
341-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:104:13-37
342            <intent-filter>
342-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:106:13-108:29
343                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
343-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:17-79
343-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:107:25-76
344            </intent-filter>
345        </receiver>
346        <receiver
346-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:110:9-121:20
347            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
347-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:111:13-88
348            android:directBootAware="false"
348-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:112:13-44
349            android:enabled="false"
349-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:113:13-36
350            android:exported="false" >
350-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:114:13-37
351            <intent-filter>
351-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:116:13-120:29
352                <action android:name="android.intent.action.BOOT_COMPLETED" />
352-->C:\Muslim Core\app\src\main\AndroidManifest.xml:81:17-79
352-->C:\Muslim Core\app\src\main\AndroidManifest.xml:81:25-76
353                <action android:name="android.intent.action.TIME_SET" />
353-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:17-73
353-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:118:25-70
354                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
354-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:17-81
354-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:119:25-78
355            </intent-filter>
356        </receiver>
357        <receiver
357-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:122:9-131:20
358            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
358-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:123:13-99
359            android:directBootAware="false"
359-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:124:13-44
360            android:enabled="@bool/enable_system_alarm_service_default"
360-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:125:13-72
361            android:exported="false" >
361-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:126:13-37
362            <intent-filter>
362-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:128:13-130:29
363                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
363-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:17-98
363-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:129:25-95
364            </intent-filter>
365        </receiver>
366        <receiver
366-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:132:9-142:20
367            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
367-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:133:13-78
368            android:directBootAware="false"
368-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:134:13-44
369            android:enabled="true"
369-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:135:13-35
370            android:exported="true"
370-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:136:13-36
371            android:permission="android.permission.DUMP" >
371-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:137:13-57
372            <intent-filter>
372-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:139:13-141:29
373                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
373-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:17-88
373-->[androidx.work:work-runtime:2.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\59f4b2c5fb29ed0d6dfb40492e70e152\transformed\work-runtime-2.8.1\AndroidManifest.xml:140:25-85
374            </intent-filter>
375        </receiver>
376        <receiver
376-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:29:9-33:20
377            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
377-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:30:13-85
378            android:enabled="true"
378-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:31:13-35
379            android:exported="false" >
379-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:32:13-37
380        </receiver>
381
382        <service
382-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:35:9-38:40
383            android:name="com.google.android.gms.measurement.AppMeasurementService"
383-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:36:13-84
384            android:enabled="true"
384-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:37:13-35
385            android:exported="false" />
385-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:38:13-37
386        <service
386-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:39:9-43:72
387            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
387-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:40:13-87
388            android:enabled="true"
388-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:41:13-35
389            android:exported="false"
389-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:42:13-37
390            android:permission="android.permission.BIND_JOB_SERVICE" />
390-->[com.google.android.gms:play-services-measurement:21.5.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cce030851b64fce14a30f35636cafff\transformed\play-services-measurement-21.5.0\AndroidManifest.xml:43:13-69
391
392        <uses-library
392-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\135d01c71a961225103e48b7dd206008\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
393            android:name="android.ext.adservices"
393-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\135d01c71a961225103e48b7dd206008\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
394            android:required="false" />
394-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\135d01c71a961225103e48b7dd206008\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
395        <uses-library
395-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
396            android:name="androidx.window.extensions"
396-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
397            android:required="false" />
397-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
398        <uses-library
398-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
399            android:name="androidx.window.sidecar"
399-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
400            android:required="false" />
400-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e661d63d9c04259b2495b443a4caf917\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
401
402        <meta-data
402-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\812136ae31eafce15fafbc5dfed6f6ac\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
403            android:name="com.google.android.gms.version"
403-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\812136ae31eafce15fafbc5dfed6f6ac\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
404            android:value="@integer/google_play_services_version" />
404-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\812136ae31eafce15fafbc5dfed6f6ac\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
405
406        <service
406-->[androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa7f398d5d0d1385b12ae8bfc214adc\transformed\room-runtime-2.6.0\AndroidManifest.xml:24:9-28:63
407            android:name="androidx.room.MultiInstanceInvalidationService"
407-->[androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa7f398d5d0d1385b12ae8bfc214adc\transformed\room-runtime-2.6.0\AndroidManifest.xml:25:13-74
408            android:directBootAware="true"
408-->[androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa7f398d5d0d1385b12ae8bfc214adc\transformed\room-runtime-2.6.0\AndroidManifest.xml:26:13-43
409            android:exported="false" />
409-->[androidx.room:room-runtime:2.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\baa7f398d5d0d1385b12ae8bfc214adc\transformed\room-runtime-2.6.0\AndroidManifest.xml:27:13-37
410        <service
410-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
411            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
411-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
412            android:exported="false" >
412-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
413            <meta-data
413-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
414                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
414-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
415                android:value="cct" />
415-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\683f793d9b7aa5372f5619ebe6359687\transformed\transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
416        </service>
417        <service
417-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
418            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
418-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
419            android:exported="false"
419-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
420            android:permission="android.permission.BIND_JOB_SERVICE" >
420-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
421        </service>
422
423        <receiver
423-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
424            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
424-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
425            android:exported="false" />
425-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\4d198d6f53404638e45dff19d597f6a7\transformed\transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
426        <receiver
426-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
427            android:name="androidx.profileinstaller.ProfileInstallReceiver"
427-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
428            android:directBootAware="false"
428-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
429            android:enabled="true"
429-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
430            android:exported="true"
430-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
431            android:permission="android.permission.DUMP" >
431-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
432            <intent-filter>
432-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
433                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
433-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
433-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
434            </intent-filter>
435            <intent-filter>
435-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
436                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
436-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
436-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
437            </intent-filter>
438            <intent-filter>
438-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
439                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
439-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
439-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
440            </intent-filter>
441            <intent-filter>
441-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
442                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
442-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
442-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ce2fbb511b1dd1207b7023c44f05bb81\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
443            </intent-filter>
444        </receiver>
445    </application>
446
447</manifest>
