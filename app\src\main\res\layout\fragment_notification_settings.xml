<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/dark_green_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="24dp">

            <ImageView
                android:id="@+id/button_back"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_back"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:padding="4dp"
                app:tint="@android:color/white" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Notification Settings"
                android:textColor="@android:color/white"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center" />

            <View
                android:layout_width="24dp"
                android:layout_height="24dp" />

        </LinearLayout>

        <!-- Countdown Notification Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/dark_green_card"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Countdown Notification"
                    android:textColor="@android:color/white"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Show persistent countdown in notification bar"
                    android:textColor="#B3FFFFFF"
                    android:textSize="14sp"
                    android:layout_marginBottom="12dp" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_countdown_notification"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Enable Countdown Notification"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    app:thumbTint="@color/dark_green_primary"
                    app:trackTint="#40FFFFFF" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Prayer Alert Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/dark_green_card"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Prayer Time Alerts"
                    android:textColor="@android:color/white"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Get notified when prayer time arrives"
                    android:textColor="#B3FFFFFF"
                    android:textSize="14sp"
                    android:layout_marginBottom="12dp" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_prayer_alerts"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Enable Prayer Alerts"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    android:layout_marginBottom="16dp"
                    app:thumbTint="@color/dark_green_primary"
                    app:trackTint="#40FFFFFF" />

                <!-- Alert Type Options -->
                <LinearLayout
                    android:id="@+id/layout_alert_options"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Alert Type"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <RadioGroup
                        android:id="@+id/radio_group_alert_type"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <RadioButton
                            android:id="@+id/radio_vibration_only"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Vibration Only"
                            android:textColor="@android:color/white"
                            android:textSize="14sp"
                            app:buttonTint="@color/dark_green_primary" />

                        <RadioButton
                            android:id="@+id/radio_athan_sound"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Athan Sound (App)"
                            android:textColor="@android:color/white"
                            android:textSize="14sp"
                            app:buttonTint="@color/dark_green_primary" />

                        <RadioButton
                            android:id="@+id/radio_phone_sound"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Phone Notification Sound"
                            android:textColor="@android:color/white"
                            android:textSize="14sp"
                            app:buttonTint="@color/dark_green_primary" />

                        <RadioButton
                            android:id="@+id/radio_vibration_and_sound"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Vibration + Sound"
                            android:textColor="@android:color/white"
                            android:textSize="14sp"
                            app:buttonTint="@color/dark_green_primary" />

                    </RadioGroup>

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Full Screen Alert Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/dark_green_card"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Full Screen Alert"
                    android:textColor="@android:color/white"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Show prayer alert even when phone is locked"
                    android:textColor="#B3FFFFFF"
                    android:textSize="14sp"
                    android:layout_marginBottom="12dp" />

                <androidx.appcompat.widget.SwitchCompat
                    android:id="@+id/switch_fullscreen_alert"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Enable Full Screen Alert"
                    android:textColor="@android:color/white"
                    android:textSize="16sp"
                    app:thumbTint="@color/dark_green_primary"
                    app:trackTint="#40FFFFFF" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Save Button -->
        <Button
            android:id="@+id/button_save_settings"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginTop="24dp"
            android:background="@drawable/button_rounded"
            android:text="Save Settings"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

</ScrollView>
