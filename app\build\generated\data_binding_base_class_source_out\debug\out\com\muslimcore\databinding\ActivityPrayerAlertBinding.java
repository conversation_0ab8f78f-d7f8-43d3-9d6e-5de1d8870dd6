// Generated by view binder compiler. Do not edit!
package com.muslimcore.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.muslimcore.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPrayerAlertBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button buttonDismiss;

  @NonNull
  public final Button buttonSnooze;

  @NonNull
  public final Button buttonSnooze10;

  @NonNull
  public final Button buttonSnooze20;

  @NonNull
  public final Button buttonSnooze30;

  @NonNull
  public final Button buttonSnooze5;

  @NonNull
  public final LinearLayout layoutSnoozeOptions;

  @NonNull
  public final TextView textPrayerName;

  @NonNull
  public final TextView textPrayerTime;

  private ActivityPrayerAlertBinding(@NonNull LinearLayout rootView, @NonNull Button buttonDismiss,
      @NonNull Button buttonSnooze, @NonNull Button buttonSnooze10, @NonNull Button buttonSnooze20,
      @NonNull Button buttonSnooze30, @NonNull Button buttonSnooze5,
      @NonNull LinearLayout layoutSnoozeOptions, @NonNull TextView textPrayerName,
      @NonNull TextView textPrayerTime) {
    this.rootView = rootView;
    this.buttonDismiss = buttonDismiss;
    this.buttonSnooze = buttonSnooze;
    this.buttonSnooze10 = buttonSnooze10;
    this.buttonSnooze20 = buttonSnooze20;
    this.buttonSnooze30 = buttonSnooze30;
    this.buttonSnooze5 = buttonSnooze5;
    this.layoutSnoozeOptions = layoutSnoozeOptions;
    this.textPrayerName = textPrayerName;
    this.textPrayerTime = textPrayerTime;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPrayerAlertBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPrayerAlertBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_prayer_alert, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPrayerAlertBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_dismiss;
      Button buttonDismiss = ViewBindings.findChildViewById(rootView, id);
      if (buttonDismiss == null) {
        break missingId;
      }

      id = R.id.button_snooze;
      Button buttonSnooze = ViewBindings.findChildViewById(rootView, id);
      if (buttonSnooze == null) {
        break missingId;
      }

      id = R.id.button_snooze_10;
      Button buttonSnooze10 = ViewBindings.findChildViewById(rootView, id);
      if (buttonSnooze10 == null) {
        break missingId;
      }

      id = R.id.button_snooze_20;
      Button buttonSnooze20 = ViewBindings.findChildViewById(rootView, id);
      if (buttonSnooze20 == null) {
        break missingId;
      }

      id = R.id.button_snooze_30;
      Button buttonSnooze30 = ViewBindings.findChildViewById(rootView, id);
      if (buttonSnooze30 == null) {
        break missingId;
      }

      id = R.id.button_snooze_5;
      Button buttonSnooze5 = ViewBindings.findChildViewById(rootView, id);
      if (buttonSnooze5 == null) {
        break missingId;
      }

      id = R.id.layout_snooze_options;
      LinearLayout layoutSnoozeOptions = ViewBindings.findChildViewById(rootView, id);
      if (layoutSnoozeOptions == null) {
        break missingId;
      }

      id = R.id.text_prayer_name;
      TextView textPrayerName = ViewBindings.findChildViewById(rootView, id);
      if (textPrayerName == null) {
        break missingId;
      }

      id = R.id.text_prayer_time;
      TextView textPrayerTime = ViewBindings.findChildViewById(rootView, id);
      if (textPrayerTime == null) {
        break missingId;
      }

      return new ActivityPrayerAlertBinding((LinearLayout) rootView, buttonDismiss, buttonSnooze,
          buttonSnooze10, buttonSnooze20, buttonSnooze30, buttonSnooze5, layoutSnoozeOptions,
          textPrayerName, textPrayerTime);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
