package com.muslimcore.domain.repositories

import com.muslimcore.domain.models.PrayerCountdown
import kotlinx.coroutines.flow.StateFlow

/**
 * Repository interface for managing prayer countdown
 */
interface PrayerCountdownRepository {
    
    /**
     * StateFlow of current prayer countdown
     */
    val prayerCountdown: StateFlow<PrayerCountdown?>
    
    /**
     * Start the prayer countdown service
     */
    fun startCountdownService()
    
    /**
     * Stop the prayer countdown service
     */
    fun stopCountdownService()
    
    /**
     * Check if countdown service is running
     */
    fun isServiceRunning(): Bo<PERSON>an
    
    /**
     * Get current countdown data
     */
    suspend fun getCurrentCountdown(): PrayerCountdown?
}
