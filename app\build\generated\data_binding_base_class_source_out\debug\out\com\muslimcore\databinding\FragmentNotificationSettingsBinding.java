// Generated by view binder compiler. Do not edit!
package com.muslimcore.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.muslimcore.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentNotificationSettingsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final ImageView buttonBack;

  @NonNull
  public final Button buttonSaveSettings;

  @NonNull
  public final LinearLayout layoutAlertOptions;

  @NonNull
  public final RadioButton radioAthanSound;

  @NonNull
  public final RadioGroup radioGroupAlertType;

  @NonNull
  public final RadioButton radioPhoneSound;

  @NonNull
  public final RadioButton radioVibrationAndSound;

  @NonNull
  public final RadioButton radioVibrationOnly;

  @NonNull
  public final SwitchCompat switchCountdownNotification;

  @NonNull
  public final SwitchCompat switchFullscreenAlert;

  @NonNull
  public final SwitchCompat switchPrayerAlerts;

  private FragmentNotificationSettingsBinding(@NonNull ScrollView rootView,
      @NonNull ImageView buttonBack, @NonNull Button buttonSaveSettings,
      @NonNull LinearLayout layoutAlertOptions, @NonNull RadioButton radioAthanSound,
      @NonNull RadioGroup radioGroupAlertType, @NonNull RadioButton radioPhoneSound,
      @NonNull RadioButton radioVibrationAndSound, @NonNull RadioButton radioVibrationOnly,
      @NonNull SwitchCompat switchCountdownNotification,
      @NonNull SwitchCompat switchFullscreenAlert, @NonNull SwitchCompat switchPrayerAlerts) {
    this.rootView = rootView;
    this.buttonBack = buttonBack;
    this.buttonSaveSettings = buttonSaveSettings;
    this.layoutAlertOptions = layoutAlertOptions;
    this.radioAthanSound = radioAthanSound;
    this.radioGroupAlertType = radioGroupAlertType;
    this.radioPhoneSound = radioPhoneSound;
    this.radioVibrationAndSound = radioVibrationAndSound;
    this.radioVibrationOnly = radioVibrationOnly;
    this.switchCountdownNotification = switchCountdownNotification;
    this.switchFullscreenAlert = switchFullscreenAlert;
    this.switchPrayerAlerts = switchPrayerAlerts;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentNotificationSettingsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentNotificationSettingsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_notification_settings, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentNotificationSettingsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_back;
      ImageView buttonBack = ViewBindings.findChildViewById(rootView, id);
      if (buttonBack == null) {
        break missingId;
      }

      id = R.id.button_save_settings;
      Button buttonSaveSettings = ViewBindings.findChildViewById(rootView, id);
      if (buttonSaveSettings == null) {
        break missingId;
      }

      id = R.id.layout_alert_options;
      LinearLayout layoutAlertOptions = ViewBindings.findChildViewById(rootView, id);
      if (layoutAlertOptions == null) {
        break missingId;
      }

      id = R.id.radio_athan_sound;
      RadioButton radioAthanSound = ViewBindings.findChildViewById(rootView, id);
      if (radioAthanSound == null) {
        break missingId;
      }

      id = R.id.radio_group_alert_type;
      RadioGroup radioGroupAlertType = ViewBindings.findChildViewById(rootView, id);
      if (radioGroupAlertType == null) {
        break missingId;
      }

      id = R.id.radio_phone_sound;
      RadioButton radioPhoneSound = ViewBindings.findChildViewById(rootView, id);
      if (radioPhoneSound == null) {
        break missingId;
      }

      id = R.id.radio_vibration_and_sound;
      RadioButton radioVibrationAndSound = ViewBindings.findChildViewById(rootView, id);
      if (radioVibrationAndSound == null) {
        break missingId;
      }

      id = R.id.radio_vibration_only;
      RadioButton radioVibrationOnly = ViewBindings.findChildViewById(rootView, id);
      if (radioVibrationOnly == null) {
        break missingId;
      }

      id = R.id.switch_countdown_notification;
      SwitchCompat switchCountdownNotification = ViewBindings.findChildViewById(rootView, id);
      if (switchCountdownNotification == null) {
        break missingId;
      }

      id = R.id.switch_fullscreen_alert;
      SwitchCompat switchFullscreenAlert = ViewBindings.findChildViewById(rootView, id);
      if (switchFullscreenAlert == null) {
        break missingId;
      }

      id = R.id.switch_prayer_alerts;
      SwitchCompat switchPrayerAlerts = ViewBindings.findChildViewById(rootView, id);
      if (switchPrayerAlerts == null) {
        break missingId;
      }

      return new FragmentNotificationSettingsBinding((ScrollView) rootView, buttonBack,
          buttonSaveSettings, layoutAlertOptions, radioAthanSound, radioGroupAlertType,
          radioPhoneSound, radioVibrationAndSound, radioVibrationOnly, switchCountdownNotification,
          switchFullscreenAlert, switchPrayerAlerts);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
