<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Selected state - filled with green -->
    <item android:state_selected="true">
        <layer-list>
            <!-- Hands icon filled with green -->
            <item android:drawable="@drawable/hands_icon_filled" 
                  android:gravity="center" />
        </layer-list>
    </item>
    
    <!-- Normal state - grey fill -->
    <item>
        <layer-list>
            <!-- Hands icon grey -->
            <item android:drawable="@drawable/hands_icon" 
                  android:gravity="center" />
        </layer-list>
    </item>
</selector>
