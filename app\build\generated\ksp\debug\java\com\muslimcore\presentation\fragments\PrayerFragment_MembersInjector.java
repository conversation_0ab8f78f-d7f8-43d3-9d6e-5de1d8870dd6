package com.muslimcore.presentation.fragments;

import com.muslimcore.data.local.managers.PrayerTimeManager;
import com.muslimcore.presentation.utils.ThemeManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PrayerFragment_MembersInjector implements MembersInjector<PrayerFragment> {
  private final Provider<PrayerTimeManager> prayerTimeManagerProvider;

  private final Provider<ThemeManager> themeManagerProvider;

  public PrayerFragment_MembersInjector(Provider<PrayerTimeManager> prayerTimeManagerProvider,
      Provider<ThemeManager> themeManagerProvider) {
    this.prayerTimeManagerProvider = prayerTimeManagerProvider;
    this.themeManagerProvider = themeManagerProvider;
  }

  public static MembersInjector<PrayerFragment> create(
      Provider<PrayerTimeManager> prayerTimeManagerProvider,
      Provider<ThemeManager> themeManagerProvider) {
    return new PrayerFragment_MembersInjector(prayerTimeManagerProvider, themeManagerProvider);
  }

  @Override
  public void injectMembers(PrayerFragment instance) {
    injectPrayerTimeManager(instance, prayerTimeManagerProvider.get());
    injectThemeManager(instance, themeManagerProvider.get());
  }

  @InjectedFieldSignature("com.muslimcore.presentation.fragments.PrayerFragment.prayerTimeManager")
  public static void injectPrayerTimeManager(PrayerFragment instance,
      PrayerTimeManager prayerTimeManager) {
    instance.prayerTimeManager = prayerTimeManager;
  }

  @InjectedFieldSignature("com.muslimcore.presentation.fragments.PrayerFragment.themeManager")
  public static void injectThemeManager(PrayerFragment instance, ThemeManager themeManager) {
    instance.themeManager = themeManager;
  }
}
