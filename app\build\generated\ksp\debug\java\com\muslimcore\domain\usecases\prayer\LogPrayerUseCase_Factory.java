package com.muslimcore.domain.usecases.prayer;

import com.muslimcore.domain.repositories.PrayerRepository;
import com.muslimcore.domain.repositories.UserRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class LogPrayerUseCase_Factory implements Factory<LogPrayerUseCase> {
  private final Provider<PrayerRepository> prayerRepositoryProvider;

  private final Provider<UserRepository> userRepositoryProvider;

  public LogPrayerUseCase_Factory(Provider<PrayerRepository> prayerRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    this.prayerRepositoryProvider = prayerRepositoryProvider;
    this.userRepositoryProvider = userRepositoryProvider;
  }

  @Override
  public LogPrayerUseCase get() {
    return newInstance(prayerRepositoryProvider.get(), userRepositoryProvider.get());
  }

  public static LogPrayerUseCase_Factory create(Provider<PrayerRepository> prayerRepositoryProvider,
      Provider<UserRepository> userRepositoryProvider) {
    return new LogPrayerUseCase_Factory(prayerRepositoryProvider, userRepositoryProvider);
  }

  public static LogPrayerUseCase newInstance(PrayerRepository prayerRepository,
      UserRepository userRepository) {
    return new LogPrayerUseCase(prayerRepository, userRepository);
  }
}
