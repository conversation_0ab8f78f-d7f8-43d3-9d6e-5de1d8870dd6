package com.muslimcore.data.repositories;

import com.muslimcore.data.local.dao.PrayerDao;
import com.muslimcore.data.remote.api.PrayerTimesApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PrayerRepositoryImpl_Factory implements Factory<PrayerRepositoryImpl> {
  private final Provider<PrayerDao> prayerDaoProvider;

  private final Provider<PrayerTimesApi> prayerTimesApiProvider;

  public PrayerRepositoryImpl_Factory(Provider<PrayerDao> prayerDaoProvider,
      Provider<PrayerTimesApi> prayerTimesApiProvider) {
    this.prayerDaoProvider = prayerDaoProvider;
    this.prayerTimesApiProvider = prayerTimesApiProvider;
  }

  @Override
  public PrayerRepositoryImpl get() {
    return newInstance(prayerDaoProvider.get(), prayerTimesApiProvider.get());
  }

  public static PrayerRepositoryImpl_Factory create(Provider<PrayerDao> prayerDaoProvider,
      Provider<PrayerTimesApi> prayerTimesApiProvider) {
    return new PrayerRepositoryImpl_Factory(prayerDaoProvider, prayerTimesApiProvider);
  }

  public static PrayerRepositoryImpl newInstance(PrayerDao prayerDao,
      PrayerTimesApi prayerTimesApi) {
    return new PrayerRepositoryImpl(prayerDao, prayerTimesApi);
  }
}
