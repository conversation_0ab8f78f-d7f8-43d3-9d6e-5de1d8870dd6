// Generated by view binder compiler. Do not edit!
package com.muslimcore.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.muslimcore.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentLocationPickerBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView buttonBack;

  @NonNull
  public final Button buttonUseCurrent;

  @NonNull
  public final EditText editSearch;

  @NonNull
  public final RecyclerView recyclerCities;

  @NonNull
  public final TextView textCurrentLocation;

  private FragmentLocationPickerBinding(@NonNull LinearLayout rootView,
      @NonNull ImageView buttonBack, @NonNull Button buttonUseCurrent, @NonNull EditText editSearch,
      @NonNull RecyclerView recyclerCities, @NonNull TextView textCurrentLocation) {
    this.rootView = rootView;
    this.buttonBack = buttonBack;
    this.buttonUseCurrent = buttonUseCurrent;
    this.editSearch = editSearch;
    this.recyclerCities = recyclerCities;
    this.textCurrentLocation = textCurrentLocation;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentLocationPickerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentLocationPickerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_location_picker, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentLocationPickerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.button_back;
      ImageView buttonBack = ViewBindings.findChildViewById(rootView, id);
      if (buttonBack == null) {
        break missingId;
      }

      id = R.id.button_use_current;
      Button buttonUseCurrent = ViewBindings.findChildViewById(rootView, id);
      if (buttonUseCurrent == null) {
        break missingId;
      }

      id = R.id.edit_search;
      EditText editSearch = ViewBindings.findChildViewById(rootView, id);
      if (editSearch == null) {
        break missingId;
      }

      id = R.id.recycler_cities;
      RecyclerView recyclerCities = ViewBindings.findChildViewById(rootView, id);
      if (recyclerCities == null) {
        break missingId;
      }

      id = R.id.text_current_location;
      TextView textCurrentLocation = ViewBindings.findChildViewById(rootView, id);
      if (textCurrentLocation == null) {
        break missingId;
      }

      return new FragmentLocationPickerBinding((LinearLayout) rootView, buttonBack,
          buttonUseCurrent, editSearch, recyclerCities, textCurrentLocation);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
