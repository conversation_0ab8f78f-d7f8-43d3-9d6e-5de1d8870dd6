package com.muslimcore.presentation.activities

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.media.AudioManager
import android.media.MediaPlayer
import android.media.RingtoneManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.VibrationEffect
import android.os.Vibrator
import android.view.View
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import com.muslimcore.R
import com.muslimcore.data.local.managers.PrayerTimeManager
import com.muslimcore.databinding.ActivityPrayerAlertBinding
import com.muslimcore.presentation.fragments.NotificationSettingsFragment
import java.text.SimpleDateFormat
import java.util.*

class PrayerAlertActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPrayerAlertBinding
    private lateinit var notificationPrefs: SharedPreferences
    private var mediaPlayer: MediaPlayer? = null
    private var vibrator: Vibrator? = null
    private var prayerName: String = ""
    private var hasBeenSnoozed = false

    companion object {
        private const val EXTRA_PRAYER_NAME = "prayer_name"
        private const val EXTRA_PRAYER_TIME = "prayer_time"
        
        fun createIntent(context: Context, prayerName: String, prayerTime: String): Intent {
            return Intent(context, PrayerAlertActivity::class.java).apply {
                putExtra(EXTRA_PRAYER_NAME, prayerName)
                putExtra(EXTRA_PRAYER_TIME, prayerTime)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Show over lock screen
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setShowWhenLocked(true)
            setTurnScreenOn(true)
        } else {
            window.addFlags(
                WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON
            )
        }
        
        binding = ActivityPrayerAlertBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        notificationPrefs = getSharedPreferences("notification_settings", Context.MODE_PRIVATE)
        
        setupUI()
        setupClickListeners()
        startAlert()
    }

    private fun setupUI() {
        prayerName = intent.getStringExtra(EXTRA_PRAYER_NAME) ?: "Prayer"
        val prayerTime = intent.getStringExtra(EXTRA_PRAYER_TIME) ?: ""
        
        binding.textPrayerName.text = prayerName
        binding.textPrayerTime.text = prayerTime
        
        // Check if this prayer has already been snoozed
        val snoozeKey = "snoozed_${prayerName}_${getCurrentDateKey()}"
        hasBeenSnoozed = notificationPrefs.getBoolean(snoozeKey, false)
        
        // Hide snooze button if already snoozed
        if (hasBeenSnoozed) {
            binding.buttonSnooze.visibility = View.GONE
        }
    }

    private fun setupClickListeners() {
        binding.buttonDismiss.setOnClickListener {
            dismissAlert()
        }
        
        binding.buttonSnooze.setOnClickListener {
            if (binding.layoutSnoozeOptions.visibility == View.VISIBLE) {
                binding.layoutSnoozeOptions.visibility = View.GONE
            } else {
                binding.layoutSnoozeOptions.visibility = View.VISIBLE
            }
        }
        
        binding.buttonSnooze5.setOnClickListener { snoozeAlert(5) }
        binding.buttonSnooze10.setOnClickListener { snoozeAlert(10) }
        binding.buttonSnooze20.setOnClickListener { snoozeAlert(20) }
        binding.buttonSnooze30.setOnClickListener { snoozeAlert(30) }
    }

    private fun startAlert() {
        val alertType = notificationPrefs.getString("alert_type", NotificationSettingsFragment.ALERT_TYPE_VIBRATION_AND_SOUND)
        
        when (alertType) {
            NotificationSettingsFragment.ALERT_TYPE_VIBRATION -> {
                startVibration()
            }
            NotificationSettingsFragment.ALERT_TYPE_ATHAN -> {
                startAthanSound()
            }
            NotificationSettingsFragment.ALERT_TYPE_PHONE_SOUND -> {
                startPhoneSound()
            }
            NotificationSettingsFragment.ALERT_TYPE_VIBRATION_AND_SOUND -> {
                startVibration()
                startAthanSound()
            }
        }
    }

    private fun startVibration() {
        vibrator = getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            // Vibrate for 1 minute with pattern
            val pattern = longArrayOf(0, 1000, 500, 1000, 500)
            val effect = VibrationEffect.createWaveform(pattern, 0) // Repeat
            vibrator?.vibrate(effect)
        } else {
            // Legacy vibration
            val pattern = longArrayOf(0, 1000, 500, 1000, 500)
            vibrator?.vibrate(pattern, 0)
        }
        
        // Stop vibration after 1 minute
        binding.root.postDelayed({
            stopVibration()
        }, 60000)
    }

    private fun startAthanSound() {
        try {
            // For now, use phone notification sound as athan
            // TODO: Add athan.mp3 file to res/raw folder
            startPhoneSound()
        } catch (e: Exception) {
            // Fallback to phone sound if athan not available
            startPhoneSound()
        }
    }

    private fun startPhoneSound() {
        try {
            val notificationUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
            mediaPlayer = MediaPlayer.create(this, notificationUri)
            mediaPlayer?.apply {
                setAudioStreamType(AudioManager.STREAM_ALARM)
                isLooping = false
                start()
            }
        } catch (e: Exception) {
            // Handle error
        }
    }

    private fun snoozeAlert(minutes: Int) {
        // Mark this prayer as snoozed
        val snoozeKey = "snoozed_${prayerName}_${getCurrentDateKey()}"
        notificationPrefs.edit().putBoolean(snoozeKey, true).apply()
        
        // Schedule snooze alarm
        scheduleSnoozeAlarm(minutes)
        
        dismissAlert()
    }

    private fun scheduleSnoozeAlarm(minutes: Int) {
        val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
        val intent = Intent(this, PrayerAlertActivity::class.java).apply {
            putExtra(EXTRA_PRAYER_NAME, "$prayerName (Reminder)")
            putExtra(EXTRA_PRAYER_TIME, "Snoozed for $minutes minutes")
        }
        
        val pendingIntent = PendingIntent.getActivity(
            this,
            prayerName.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val triggerTime = System.currentTimeMillis() + (minutes * 60 * 1000)
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)
        } else {
            alarmManager.setExact(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)
        }
    }

    private fun dismissAlert() {
        stopAlert()
        finish()
    }

    private fun stopAlert() {
        stopVibration()
        stopSound()
    }

    private fun stopVibration() {
        vibrator?.cancel()
    }

    private fun stopSound() {
        mediaPlayer?.apply {
            if (isPlaying) {
                stop()
            }
            release()
        }
        mediaPlayer = null
    }

    private fun getCurrentDateKey(): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        return dateFormat.format(Date())
    }

    override fun onDestroy() {
        super.onDestroy()
        stopAlert()
    }

    override fun onBackPressed() {
        // Prevent back button from dismissing alert
        // User must use dismiss or snooze buttons
    }
}
