package com.muslimcore.data.repositories;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PrayerCountdownRepositoryImpl_Factory implements Factory<PrayerCountdownRepositoryImpl> {
  private final Provider<Context> contextProvider;

  public PrayerCountdownRepositoryImpl_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public PrayerCountdownRepositoryImpl get() {
    return newInstance(contextProvider.get());
  }

  public static PrayerCountdownRepositoryImpl_Factory create(Provider<Context> contextProvider) {
    return new PrayerCountdownRepositoryImpl_Factory(contextProvider);
  }

  public static PrayerCountdownRepositoryImpl newInstance(Context context) {
    return new PrayerCountdownRepositoryImpl(context);
  }
}
