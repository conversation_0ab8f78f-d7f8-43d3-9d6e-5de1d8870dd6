package com.muslimcore.presentation.viewmodels;

import com.muslimcore.domain.repositories.LocationRepository;
import com.muslimcore.domain.usecases.GetCurrentLocationUseCase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class PrayerLocationViewModel_Factory implements Factory<PrayerLocationViewModel> {
  private final Provider<LocationRepository> locationRepositoryProvider;

  private final Provider<GetCurrentLocationUseCase> getCurrentLocationUseCaseProvider;

  public PrayerLocationViewModel_Factory(Provider<LocationRepository> locationRepositoryProvider,
      Provider<GetCurrentLocationUseCase> getCurrentLocationUseCaseProvider) {
    this.locationRepositoryProvider = locationRepositoryProvider;
    this.getCurrentLocationUseCaseProvider = getCurrentLocationUseCaseProvider;
  }

  @Override
  public PrayerLocationViewModel get() {
    return newInstance(locationRepositoryProvider.get(), getCurrentLocationUseCaseProvider.get());
  }

  public static PrayerLocationViewModel_Factory create(
      Provider<LocationRepository> locationRepositoryProvider,
      Provider<GetCurrentLocationUseCase> getCurrentLocationUseCaseProvider) {
    return new PrayerLocationViewModel_Factory(locationRepositoryProvider, getCurrentLocationUseCaseProvider);
  }

  public static PrayerLocationViewModel newInstance(LocationRepository locationRepository,
      GetCurrentLocationUseCase getCurrentLocationUseCase) {
    return new PrayerLocationViewModel(locationRepository, getCurrentLocationUseCase);
  }
}
