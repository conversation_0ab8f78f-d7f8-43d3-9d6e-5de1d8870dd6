package com.muslimcore.data.repositories

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Address
import android.location.Geocoder
import android.location.Location
import androidx.core.app.ActivityCompat
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.Priority
import com.google.android.gms.tasks.CancellationTokenSource
import com.muslimcore.data.local.PreferencesManager
import com.muslimcore.domain.models.LocationResult
import com.muslimcore.domain.models.LocationState
import com.muslimcore.domain.models.UserLocation
import com.muslimcore.domain.repositories.LocationRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.coroutines.resume

@Singleton
class LocationRepositoryImpl @Inject constructor(
    @ApplicationContext private val context: Context,
    private val preferencesManager: PreferencesManager
) : LocationRepository {

    private val fusedLocationClient: FusedLocationProviderClient = 
        LocationServices.getFusedLocationProviderClient(context)
    
    private val geocoder = Geocoder(context, Locale.getDefault())
    
    private val _locationState = MutableStateFlow<LocationState>(LocationState.Idle)
    override val locationState: StateFlow<LocationState> = _locationState.asStateFlow()

    companion object {
        private const val LOCATION_TIMEOUT_MS = 30_000L // 30 seconds
        private const val LOCATION_CACHE_DURATION_MS = 24 * 60 * 60 * 1000L // 24 hours
    }

    override fun hasLocationPermission(): Boolean {
        return ActivityCompat.checkSelfPermission(
            context, Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED ||
        ActivityCompat.checkSelfPermission(
            context, Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
    }

    override suspend fun getCurrentLocation(): LocationResult = withContext(Dispatchers.IO) {
        try {
            _locationState.value = LocationState.Loading
            
            // Check permission first
            if (!hasLocationPermission()) {
                _locationState.value = LocationState.PermissionDenied
                return@withContext LocationResult.PermissionDenied
            }

            // Try cached location first
            getCachedLocation()?.let { cached ->
                if (cached.isRecent(LOCATION_CACHE_DURATION_MS)) {
                    _locationState.value = LocationState.Success(cached)
                    return@withContext LocationResult.Success(cached)
                }
            }

            // Get fresh location
            val freshLocation = getFreshLocation()
            when (freshLocation) {
                is LocationResult.Success -> {
                    saveLocation(freshLocation.location)
                    _locationState.value = LocationState.Success(freshLocation.location)
                    freshLocation
                }
                is LocationResult.Error -> {
                    // Fallback to cached location if available
                    getCachedLocation()?.let { cached ->
                        _locationState.value = LocationState.Success(cached)
                        LocationResult.Success(cached)
                    } ?: run {
                        // Fallback to default location
                        val defaultLocation = getDefaultLocation()
                        _locationState.value = LocationState.Success(defaultLocation)
                        LocationResult.Success(defaultLocation)
                    }
                }
                else -> freshLocation
            }
        } catch (e: Exception) {
            val error = LocationResult.Error("Failed to get location: ${e.message}", e)
            _locationState.value = LocationState.Error(error.message, error.throwable)
            error
        }
    }

    private suspend fun getFreshLocation(): LocationResult {
        return try {
            withTimeoutOrNull(LOCATION_TIMEOUT_MS) {
                suspendCancellableCoroutine<LocationResult> { continuation ->
                    val cancellationTokenSource = CancellationTokenSource()

                    continuation.invokeOnCancellation {
                        cancellationTokenSource.cancel()
                    }

                    try {
                        fusedLocationClient.getCurrentLocation(
                            Priority.PRIORITY_HIGH_ACCURACY,
                            cancellationTokenSource.token
                        ).addOnSuccessListener { location ->
                            if (location != null) {
                                // Get address from coordinates
                                val userLocation = getLocationFromCoordinates(location)
                                continuation.resume(LocationResult.Success(userLocation))
                            } else {
                                continuation.resume(LocationResult.Error("Location is null"))
                            }
                        }.addOnFailureListener { exception ->
                            continuation.resume(LocationResult.Error("Location failed: ${exception.message}", exception))
                        }
                    } catch (e: SecurityException) {
                        continuation.resume(LocationResult.PermissionDenied)
                    }
                }
            } ?: LocationResult.Timeout
        } catch (e: Exception) {
            LocationResult.Error("Location request failed: ${e.message}", e)
        }
    }

    private fun getLocationFromCoordinates(location: Location): UserLocation {
        return try {
            val addresses = geocoder.getFromLocation(location.latitude, location.longitude, 1)
            val address = addresses?.firstOrNull()
            
            UserLocation(
                latitude = location.latitude,
                longitude = location.longitude,
                city = address?.locality ?: address?.subAdminArea ?: "Unknown City",
                country = address?.countryName ?: "Unknown Country"
            )
        } catch (e: Exception) {
            UserLocation(
                latitude = location.latitude,
                longitude = location.longitude,
                city = "Unknown City",
                country = "Unknown Country"
            )
        }
    }

    override suspend fun getCachedLocation(): UserLocation? {
        return if (preferencesManager.hasValidLocation()) {
            UserLocation(
                latitude = preferencesManager.savedLocationLat,
                longitude = preferencesManager.savedLocationLng,
                city = preferencesManager.savedLocationName.split(",").firstOrNull()?.trim() ?: "Unknown City",
                country = preferencesManager.savedLocationName.split(",").lastOrNull()?.trim() ?: "Unknown Country"
            )
        } else null
    }

    override suspend fun saveLocation(location: UserLocation) {
        preferencesManager.savedLocationLat = location.latitude
        preferencesManager.savedLocationLng = location.longitude
        preferencesManager.savedLocationName = "${location.city}, ${location.country}"
    }

    override suspend fun clearLocation() {
        preferencesManager.savedLocationLat = 0.0
        preferencesManager.savedLocationLng = 0.0
        preferencesManager.savedLocationName = ""
        _locationState.value = LocationState.Idle
    }

    override fun getDefaultLocation(): UserLocation {
        return UserLocation(
            latitude = 21.4225,
            longitude = 39.8262,
            city = "Mecca",
            country = "Saudi Arabia"
        )
    }

    override suspend fun searchLocation(query: String): LocationResult = withContext(Dispatchers.IO) {
        try {
            val addresses = geocoder.getFromLocationName(query, 1)
            val address = addresses?.firstOrNull()
            
            if (address != null) {
                val location = UserLocation(
                    latitude = address.latitude,
                    longitude = address.longitude,
                    city = address.locality ?: address.subAdminArea ?: query,
                    country = address.countryName ?: "Unknown Country"
                )
                LocationResult.Success(location)
            } else {
                LocationResult.Error("Location not found: $query")
            }
        } catch (e: Exception) {
            LocationResult.Error("Search failed: ${e.message}", e)
        }
    }
}
