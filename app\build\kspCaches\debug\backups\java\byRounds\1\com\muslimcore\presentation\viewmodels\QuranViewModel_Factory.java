package com.muslimcore.presentation.viewmodels;

import com.muslimcore.domain.repositories.QuranRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class QuranViewModel_Factory implements Factory<QuranViewModel> {
  private final Provider<QuranRepository> quranRepositoryProvider;

  public QuranViewModel_Factory(Provider<QuranRepository> quranRepositoryProvider) {
    this.quranRepositoryProvider = quranRepositoryProvider;
  }

  @Override
  public QuranViewModel get() {
    return newInstance(quranRepositoryProvider.get());
  }

  public static QuranViewModel_Factory create(Provider<QuranRepository> quranRepositoryProvider) {
    return new QuranViewModel_Factory(quranRepositoryProvider);
  }

  public static QuranViewModel newInstance(QuranRepository quranRepository) {
    return new QuranViewModel(quranRepository);
  }
}
