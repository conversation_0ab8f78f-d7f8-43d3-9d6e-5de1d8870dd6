package com.muslimcore.data.repositories;

import com.muslimcore.data.local.datasource.QuranAssetsDataSource;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class QuranAssetsRepository_Factory implements Factory<QuranAssetsRepository> {
  private final Provider<QuranAssetsDataSource> quranAssetsDataSourceProvider;

  public QuranAssetsRepository_Factory(
      Provider<QuranAssetsDataSource> quranAssetsDataSourceProvider) {
    this.quranAssetsDataSourceProvider = quranAssetsDataSourceProvider;
  }

  @Override
  public QuranAssetsRepository get() {
    return newInstance(quranAssetsDataSourceProvider.get());
  }

  public static QuranAssetsRepository_Factory create(
      Provider<QuranAssetsDataSource> quranAssetsDataSourceProvider) {
    return new QuranAssetsRepository_Factory(quranAssetsDataSourceProvider);
  }

  public static QuranAssetsRepository newInstance(QuranAssetsDataSource quranAssetsDataSource) {
    return new QuranAssetsRepository(quranAssetsDataSource);
  }
}
