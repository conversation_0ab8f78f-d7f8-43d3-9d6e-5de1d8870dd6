package com.muslimcore.presentation.fragments;

import com.muslimcore.data.local.managers.LocationManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class LocationPickerFragment_MembersInjector implements MembersInjector<LocationPickerFragment> {
  private final Provider<LocationManager> locationManagerProvider;

  public LocationPickerFragment_MembersInjector(Provider<LocationManager> locationManagerProvider) {
    this.locationManagerProvider = locationManagerProvider;
  }

  public static MembersInjector<LocationPickerFragment> create(
      Provider<LocationManager> locationManagerProvider) {
    return new LocationPickerFragment_MembersInjector(locationManagerProvider);
  }

  @Override
  public void injectMembers(LocationPickerFragment instance) {
    injectLocationManager(instance, locationManagerProvider.get());
  }

  @InjectedFieldSignature("com.muslimcore.presentation.fragments.LocationPickerFragment.locationManager")
  public static void injectLocationManager(LocationPickerFragment instance,
      LocationManager locationManager) {
    instance.locationManager = locationManager;
  }
}
