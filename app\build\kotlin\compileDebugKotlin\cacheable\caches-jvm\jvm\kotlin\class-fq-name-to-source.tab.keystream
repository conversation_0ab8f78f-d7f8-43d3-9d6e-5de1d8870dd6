$com.muslimcore.MuslimCoreApplication.com.muslimcore.MuslimCoreApplication.Companion,com.muslimcore.data.local.PreferencesManager6com.muslimcore.data.local.PreferencesManager.Companion-com.muslimcore.data.local.QuranJsonDataSource/com.muslimcore.data.local.converters.Converters'com.muslimcore.data.local.dao.AdhkarDao'com.muslimcore.data.local.dao.PrayerDao&com.muslimcore.data.local.dao.QuranDao5com.muslimcore.data.local.database.MuslimCoreDatabase?com.muslimcore.data.local.database.MuslimCoreDatabase.Companion:com.muslimcore.data.local.datasource.QuranAssetsDataSource.com.muslimcore.data.local.entities.DhikrEntity6com.muslimcore.data.local.entities.DhikrProgressEntity7com.muslimcore.data.local.entities.TasbeehSessionEntity2com.muslimcore.data.local.entities.PrayerLogEntity4com.muslimcore.data.local.entities.PrayerTimesEntity.com.muslimcore.data.local.entities.SurahEntity-com.muslimcore.data.local.entities.AyahEntity1com.muslimcore.data.local.entities.BookmarkEntity8com.muslimcore.data.local.entities.ReadingProgressEntity7com.muslimcore.data.local.entities.KhatmaProgressEntity6com.muslimcore.data.local.entities.FavoriteSurahEntity2com.muslimcore.data.local.entities.PrayerStatistic3com.muslimcore.data.local.entities.DailyPrayerCount1com.muslimcore.data.local.entities.DhikrStatistic2com.muslimcore.data.local.entities.DailyDhikrCount2com.muslimcore.data.local.managers.LocationManager<com.muslimcore.data.local.managers.LocationManager.Companion?com.muslimcore.data.local.managers.LocationManager.LocationData4com.muslimcore.data.local.managers.PrayerTimeManager><EMAIL>?com.muslimcore.data.local.managers.PrayerTimeManager.NextPrayer;com.muslimcore.data.local.managers.PrayerTimeManager.Prayer/com.muslimcore.data.local.models.QuranJsonSurah(com.muslimcore.data.local.models.JuzInfo+com.muslimcore.data.local.models.VerseRange-com.muslimcore.data.local.models.TajweedSurah,com.muslimcore.data.local.models.TajweedRule0com.muslimcore.data.local.models.TajweedRuleType:com.muslimcore.data.local.models.TajweedRuleType.Companion.com.muslimcore.data.local.models.SurahMetadata2com.muslimcore.data.local.models.SurahJsonMetadata1com.muslimcore.data.local.models.TranslationSurah.com.muslimcore.data.local.models.QuranMetadata,com.muslimcore.data.local.models.TajweedSpan8com.muslimcore.data.local.models.ProfessionalTajweedRule0com.muslimcore.data.local.models.AyahTajweedData.com.muslimcore.data.local.models.TajweedColors4com.muslimcore.data.local.models.TajweedColors.Light3com.muslimcore.data.local.models.TajweedColors.Dark2com.muslimcore.data.local.models.TajweedLegendItem*com.muslimcore.data.receivers.BootReceiver1com.muslimcore.data.receivers.PrayerAlarmReceiver-com.muslimcore.data.remote.api.PrayerTimesApi7com.muslimcore.data.remote.api.PrayerTimesApi.Companion3com.muslimcore.data.remote.api.CalculationMethodDto'com.muslimcore.data.remote.api.QuranApi1com.muslimcore.data.remote.api.QuranApi.Companion2com.muslimcore.data.remote.dto.PrayerTimesResponse.com.muslimcore.data.remote.dto.PrayerTimesData)com.muslimcore.data.remote.dto.TimingsDto&com.muslimcore.data.remote.dto.DateDto'com.muslimcore.data.remote.dto.HijriDto+com.muslimcore.data.remote.dto.GregorianDto)com.muslimcore.data.remote.dto.WeekdayDto'com.muslimcore.data.remote.dto.MonthDto&com.muslimcore.data.remote.dto.MetaDto(com.muslimcore.data.remote.dto.MethodDto,com.muslimcore.data.remote.dto.QuranResponse,com.muslimcore.data.remote.dto.SurahResponse+com.muslimcore.data.remote.dto.AyahResponse(com.muslimcore.data.remote.dto.AyahsData'com.muslimcore.data.remote.dto.SurahDto&com.muslimcore.data.remote.dto.AyahDto+com.muslimcore.data.remote.dto.SurahInfoDto7com.muslimcore.data.repositories.LocationRepositoryImplAcom.muslimcore.data.repositories.LocationRepositoryImpl.Companion5com.muslimcore.data.repositories.PrayerRepositoryImpl6com.muslimcore.data.repositories.QuranAssetsRepository4com.muslimcore.data.repositories.QuranRepositoryImpl3com.muslimcore.data.repositories.UserRepositoryImpl2com.muslimcore.data.repository.QuranJsonRepository7com.muslimcore.data.services.MuslimCoreMessagingServiceAcom.muslimcore.data.services.MuslimCoreMessagingService.Companion6com.muslimcore.data.services.PrayerNotificationService4com.muslimcore.data.workers.PrayerNotificationWorker com.muslimcore.di.DatabaseModule com.muslimcore.di.FirebaseModule com.muslimcore.di.LocationModulecom.muslimcore.di.NetworkModule"com.muslimcore.di.RepositoryModule$com.muslimcore.domain.entities.Dhikr,com.muslimcore.domain.entities.DhikrCategory6com.muslimcore.domain.entities.DhikrCategory.Companion,com.muslimcore.domain.entities.DhikrProgress-com.muslimcore.domain.entities.TasbeehSession-com.muslimcore.domain.entities.AdhkarSettings*com.muslimcore.domain.entities.PrayerTimes(com.muslimcore.domain.entities.PrayerLog+com.muslimcore.domain.entities.PrayerStreak%com.muslimcore.domain.entities.Prayer/com.muslimcore.domain.entities.Prayer.Companion)com.muslimcore.domain.entities.NextPrayer$com.muslimcore.domain.entities.Surah#com.muslimcore.domain.entities.Ayah'com.muslimcore.domain.entities.Bookmark.com.muslimcore.domain.entities.ReadingProgress-com.muslimcore.domain.entities.KhatmaProgress,com.muslimcore.domain.entities.QuranSettings.com.muslimcore.domain.entities.RecitationSpeed#com.muslimcore.domain.entities.User.com.muslimcore.domain.entities.UserPreferences+com.muslimcore.domain.entities.UserLocation3com.muslimcore.domain.entities.NotificationSettings(com.muslimcore.domain.entities.QiblaInfo*com.muslimcore.domain.models.LocationState/com.muslimcore.domain.models.LocationState.Idle2com.muslimcore.domain.models.LocationState.Loading2com.muslimcore.domain.models.LocationState.Success0com.muslimcore.domain.models.LocationState.Error;com.muslimcore.domain.models.LocationState.PermissionDenied=com.muslimcore.domain.models.LocationState.PermissionRequired)com.muslimcore.domain.models.UserLocation+com.muslimcore.domain.models.LocationResult3com.muslimcore.domain.models.LocationResult.Success1com.muslimcore.domain.models.LocationResult.Error<com.muslimcore.domain.models.LocationResult.PermissionDenied3com.muslimcore.domain.models.LocationResult.Timeout3com.muslimcore.domain.repositories.AdhkarRepository5com.muslimcore.domain.repositories.LocationRepository3com.muslimcore.domain.repositories.PrayerRepository4com.muslimcore.domain.repositories.CalculationMethod2com.muslimcore.domain.repositories.QuranRepository1com.muslimcore.domain.repositories.UserRepository8com.muslimcore.domain.usecases.GetCurrentLocationUseCase;com.muslimcore.domain.usecases.prayer.GetPrayerTimesUseCase6com.muslimcore.domain.usecases.prayer.LogPrayerUseCase4com.muslimcore.domain.usecases.quran.GetSurahUseCase(com.muslimcore.presentation.MainActivity:com.muslimcore.presentation.activities.PrayerAlertActivityDcom.muslimcore.presentation.activities.PrayerAlertActivity.Companion0com.muslimcore.presentation.adapters.AyahAdapter?com.muslimcore.presentation.adapters.AyahAdapter.AyahViewHolderAcom.muslimcore.presentation.adapters.AyahAdapter.AyahDiffCallback0com.muslimcore.presentation.adapters.CalendarDay4com.muslimcore.presentation.adapters.CalendarAdapterJcom.muslimcore.presentation.adapters.CalendarAdapter.CalendarDayViewHolder/com.muslimcore.presentation.adapters.DuaAdapter=com.muslimcore.presentation.adapters.DuaAdapter.DuaViewHolder?<EMAIL><com.muslimcore.presentation.fragments.LocationPickerFragmentBcom.muslimcore.presentation.fragments.NotificationSettingsFragmentLcom.muslimcore.presentation.fragments.NotificationSettingsFragment.Companion4com.muslimcore.presentation.fragments.PrayerFragment;com.muslimcore.presentation.fragments.PrayerTrackerFragment3com.muslimcore.presentation.fragments.QuranFragment6com.muslimcore.presentation.fragments.SettingsFragment9com.muslimcore.presentation.fragments.SurahDetailFragmentCcom.muslimcore.presentation.fragments.SurahDetailFragment.Companion:com.muslimcore.presentation.fragments.TajweedRulesFragmentDcom.muslimcore.presentation.fragments.TajweedRulesFragment.Companion=com.muslimcore.presentation.fragments.TajweedSettingsFragment2com.muslimcore.presentation.receivers.BootReceiver2com.muslimcore.presentation.services.PrayerService<com.muslimcore.presentation.services.PrayerService.Companion4com.muslimcore.presentation.utils.AyahCircleDrawable4com.muslimcore.presentation.utils.CustomTypefaceSpan1com.muslimcore.presentation.utils.FontPreferences;com.muslimcore.presentation.utils.FontPreferences.Companion>com.muslimcore.presentation.utils.ProfessionalTajweedProcessor3com.muslimcore.presentation.utils.CenteredImageSpan0com.muslimcore.presentation.utils.QuranFontUtils=com.muslimcore.presentation.utils.QuranFontUtils.QuranSymbols7com.muslimcore.presentation.utils.QuranTajweedProcessorIcom.muslimcore.presentation.utils.QuranTajweedProcessor.TajweedLegendItem.com.muslimcore.presentation.utils.ThemeManager8com.muslimcore.presentation.utils.ThemeManager.Companion:com.muslimcore.presentation.utils.ThemeManager.ThemeColors4com.muslimcore.presentation.viewmodels.MainViewModel<com.muslimcore.presentation.viewmodels.MainViewModel.UiState>com.muslimcore.presentation.viewmodels.PrayerLocationViewModelFcom.muslimcore.presentation.viewmodels.PrayerLocationViewModel.UiState6com.muslimcore.presentation.viewmodels.PrayerViewModel>com.muslimcore.presentation.viewmodels.PrayerViewModel.UiState5com.muslimcore.presentation.viewmodels.QuranViewModel=com.muslimcore.presentation.viewmodels.QuranViewModel.UiState>com.muslimcore.presentation.viewmodels.QuranViewModel.QuranTab;com.muslimcore.presentation.viewmodels.SurahDetailViewModelCcom.muslimcore.presentation.viewmodels.SurahDetailViewModel.UiState1com.muslimcore.presentation.views.TajweedTextView0com.muslimcore.databinding.ItemPrayerTimeBinding3com.muslimcore.databinding.ItemPrayerTrackerBinding1com.muslimcore.databinding.ItemCalendarDayBinding.com.muslimcore.databinding.ItemLocationBinding5com.muslimcore.databinding.ActivityPrayerAlertBinding3com.muslimcore.databinding.ItemTajweedLegendBinding6com.muslimcore.databinding.DialogLocationPickerBinding8com.muslimcore.databinding.FragmentLocationPickerBinding+com.muslimcore.databinding.ItemSurahBinding/com.muslimcore.databinding.FragmentQuranBinding.com.muslimcore.databinding.ActivityMainBinding5com.muslimcore.databinding.FragmentDuaCategoryBinding>com.muslimcore.databinding.FragmentNotificationSettingsBinding5com.muslimcore.databinding.FragmentSurahDetailBinding7com.muslimcore.databinding.FragmentPrayerTrackerBinding*com.muslimcore.databinding.ItemAyahBinding6com.muslimcore.databinding.FragmentTajweedRulesBinding9com.muslimcore.databinding.FragmentTajweedSettingsBinding0com.muslimcore.databinding.FragmentPrayerBinding2com.muslimcore.databinding.FragmentSettingsBinding4com.muslimcore.databinding.DialogFontSettingsBinding0com.muslimcore.databinding.FragmentAdhkarBinding)com.muslimcore.databinding.ItemDuaBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      