<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/dark_green_background"
    android:orientation="vertical">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp">

        <ImageView
            android:id="@+id/button_back"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_arrow_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="4dp"
            app:tint="@android:color/white" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Select Location"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:gravity="center" />

        <View
            android:layout_width="24dp"
            android:layout_height="24dp" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Current Location Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@color/dark_green_card"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="16dp">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_location"
                        app:tint="@color/dark_green_primary"
                        android:layout_marginEnd="12dp" />

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Current Location"
                            android:textColor="@android:color/white"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/text_current_location"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Detecting..."
                            android:textColor="#B3FFFFFF"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <Button
                        android:id="@+id/button_use_current"
                        android:layout_width="wrap_content"
                        android:layout_height="36dp"
                        android:text="Use"
                        android:textColor="@android:color/white"
                        android:textSize="12sp"
                        android:background="@drawable/button_rounded_small"
                        android:minWidth="60dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Search Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardBackgroundColor="@color/dark_green_card"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Search City"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <EditText
                        android:id="@+id/edit_search"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:background="@drawable/edit_text_background"
                        android:hint="Enter city name..."
                        android:textColorHint="#80FFFFFF"
                        android:textColor="@android:color/white"
                        android:textSize="14sp"
                        android:padding="12dp"
                        android:drawableStart="@drawable/ic_search"
                        android:drawablePadding="12dp"
                        app:drawableTint="#80FFFFFF" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Popular Cities Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/dark_green_card"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Popular Islamic Cities"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <!-- Cities RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_cities"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        tools:listitem="@layout/item_location" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
