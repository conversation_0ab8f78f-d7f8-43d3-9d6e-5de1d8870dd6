package com.muslimcore.di;

import com.muslimcore.data.remote.api.PrayerTimesApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import retrofit2.Retrofit;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class NetworkModule_ProvidePrayerTimesApiFactory implements Factory<PrayerTimesApi> {
  private final Provider<Retrofit> retrofitProvider;

  public NetworkModule_ProvidePrayerTimesApiFactory(Provider<Retrofit> retrofitProvider) {
    this.retrofitProvider = retrofitProvider;
  }

  @Override
  public PrayerTimesApi get() {
    return providePrayerTimesApi(retrofitProvider.get());
  }

  public static NetworkModule_ProvidePrayerTimesApiFactory create(
      Provider<Retrofit> retrofitProvider) {
    return new NetworkModule_ProvidePrayerTimesApiFactory(retrofitProvider);
  }

  public static PrayerTimesApi providePrayerTimesApi(Retrofit retrofit) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.providePrayerTimesApi(retrofit));
  }
}
