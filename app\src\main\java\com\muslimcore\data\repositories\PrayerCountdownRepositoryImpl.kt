package com.muslimcore.data.repositories

import android.app.ActivityManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import com.muslimcore.data.services.PrayerCountdownService
import com.muslimcore.domain.models.PrayerCountdown
import com.muslimcore.domain.repositories.PrayerCountdownRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of PrayerCountdownRepository
 * Manages connection to PrayerCountdownService
 */
@Singleton
class PrayerCountdownRepositoryImpl @Inject constructor(
    @ApplicationContext private val context: Context
) : PrayerCountdownRepository {

    private var service: PrayerCountdownService? = null
    private var isBound = false
    
    private val _prayerCountdown = MutableStateFlow<PrayerCountdown?>(null)
    override val prayerCountdown: StateFlow<PrayerCountdown?> = _prayerCountdown.asStateFlow()

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, binder: IBinder?) {
            val countdownBinder = binder as PrayerCountdownService.PrayerCountdownBinder
            service = countdownBinder.getService()
            isBound = true
            
            // Start observing countdown from service
            observeServiceCountdown()
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            service = null
            isBound = false
        }
    }

    override fun startCountdownService() {
        try {
            // Start the foreground service
            PrayerCountdownService.startService(context)
            
            // Bind to service to get countdown updates
            val intent = Intent(context, PrayerCountdownService::class.java)
            context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
        } catch (e: Exception) {
            android.util.Log.e("PrayerCountdownRepo", "Error starting service", e)
        }
    }

    override fun stopCountdownService() {
        try {
            // Unbind from service
            if (isBound) {
                context.unbindService(serviceConnection)
                isBound = false
            }
            
            // Stop the service
            PrayerCountdownService.stopService(context)
            service = null
            _prayerCountdown.value = null
        } catch (e: Exception) {
            android.util.Log.e("PrayerCountdownRepo", "Error stopping service", e)
        }
    }

    override fun isServiceRunning(): Boolean {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        @Suppress("DEPRECATION")
        for (service in activityManager.getRunningServices(Integer.MAX_VALUE)) {
            if (PrayerCountdownService::class.java.name == service.service.className) {
                return true
            }
        }
        return false
    }

    override suspend fun getCurrentCountdown(): PrayerCountdown? {
        return _prayerCountdown.value
    }

    private fun observeServiceCountdown() {
        service?.let { countdownService ->
            // This would ideally use coroutines to collect from service StateFlow
            // For now, we'll implement a simple callback mechanism
            // In a real implementation, you'd set up proper StateFlow observation
        }
    }
}
