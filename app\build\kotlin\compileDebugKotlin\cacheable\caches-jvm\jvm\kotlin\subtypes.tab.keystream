2com.muslimcore.domain.repositories.QuranRepositoryandroid.os.Parcelable*com.muslimcore.domain.models.LocationState+com.muslimcore.domain.models.LocationResult(androidx.appcompat.app.AppCompatActivity1androidx.recyclerview.widget.RecyclerView.Adapter(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallback$androidx.fragment.app.DialogFragment2com.muslimcore.presentation.fragments.BaseFragmentandroidx.fragment.app.Fragment!android.content.BroadcastReceiverandroid.app.Servicekotlin.Enumandroidx.lifecycle.ViewModel androidx.viewbinding.ViewBindingandroid.app.Application$androidx.work.Configuration.Providerandroidx.room.RoomDatabase5com.muslimcore.domain.repositories.LocationRepository<com.muslimcore.domain.repositories.PrayerCountdownRepository3com.muslimcore.domain.repositories.PrayerRepository1com.muslimcore.domain.repositories.UserRepository6com.google.firebase.messaging.FirebaseMessagingServiceandroid.os.Binderandroidx.work.CoroutineWorker"android.graphics.drawable.Drawableandroid.text.style.TypefaceSpanandroid.text.style.ImageSpan+androidx.appcompat.widget.AppCompatTextView                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         