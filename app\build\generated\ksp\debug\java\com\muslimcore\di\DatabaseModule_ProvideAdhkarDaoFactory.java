package com.muslimcore.di;

import com.muslimcore.data.local.dao.AdhkarDao;
import com.muslimcore.data.local.database.MuslimCoreDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseModule_ProvideAdhkarDaoFactory implements Factory<AdhkarDao> {
  private final Provider<MuslimCoreDatabase> databaseProvider;

  public DatabaseModule_ProvideAdhkarDaoFactory(Provider<MuslimCoreDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public AdhkarDao get() {
    return provideAdhkarDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideAdhkarDaoFactory create(
      Provider<MuslimCoreDatabase> databaseProvider) {
    return new DatabaseModule_ProvideAdhkarDaoFactory(databaseProvider);
  }

  public static AdhkarDao provideAdhkarDao(MuslimCoreDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideAdhkarDao(database));
  }
}
