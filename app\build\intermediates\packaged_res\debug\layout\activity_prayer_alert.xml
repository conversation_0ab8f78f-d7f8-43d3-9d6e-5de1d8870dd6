<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/dark_green_primary"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="32dp">

    <!-- Prayer Icon -->
    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@drawable/prayer_icon"
        app:tint="@android:color/white"
        android:layout_marginBottom="32dp" />

    <!-- Prayer Time Text -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Prayer Time"
        android:textColor="@android:color/white"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <!-- Prayer Name -->
    <TextView
        android:id="@+id/text_prayer_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@android:color/white"
        android:textSize="48sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp"
        tools:text="Maghrib" />

    <!-- Prayer Time -->
    <TextView
        android:id="@+id/text_prayer_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#B3FFFFFF"
        android:textSize="20sp"
        android:layout_marginBottom="48dp"
        tools:text="6:30 PM" />

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:layout_marginTop="32dp">

        <!-- Snooze Button -->
        <Button
            android:id="@+id/button_snooze"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:background="@drawable/button_rounded_outline"
            android:text="Snooze"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Dismiss Button -->
        <Button
            android:id="@+id/button_dismiss"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:background="@drawable/button_rounded"
            android:text="Dismiss"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Snooze Options (Initially Hidden) -->
    <LinearLayout
        android:id="@+id/layout_snooze_options"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginTop="24dp"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Remind me in:"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:layout_gravity="center"
            android:layout_marginBottom="16dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/button_snooze_5"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:background="@drawable/button_rounded_small"
                android:text="5 min"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

            <Button
                android:id="@+id/button_snooze_10"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="4dp"
                android:background="@drawable/button_rounded_small"
                android:text="10 min"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

            <Button
                android:id="@+id/button_snooze_20"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:layout_marginHorizontal="4dp"
                android:background="@drawable/button_rounded_small"
                android:text="20 min"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

            <Button
                android:id="@+id/button_snooze_30"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:background="@drawable/button_rounded_small"
                android:text="30 min"
                android:textColor="@android:color/white"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
