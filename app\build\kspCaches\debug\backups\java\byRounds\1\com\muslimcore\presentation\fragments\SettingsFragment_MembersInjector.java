package com.muslimcore.presentation.fragments;

import com.muslimcore.data.local.PreferencesManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SettingsFragment_MembersInjector implements MembersInjector<SettingsFragment> {
  private final Provider<PreferencesManager> preferencesManagerProvider;

  public SettingsFragment_MembersInjector(Provider<PreferencesManager> preferencesManagerProvider) {
    this.preferencesManagerProvider = preferencesManagerProvider;
  }

  public static MembersInjector<SettingsFragment> create(
      Provider<PreferencesManager> preferencesManagerProvider) {
    return new SettingsFragment_MembersInjector(preferencesManagerProvider);
  }

  @Override
  public void injectMembers(SettingsFragment instance) {
    injectPreferencesManager(instance, preferencesManagerProvider.get());
  }

  @InjectedFieldSignature("com.muslimcore.presentation.fragments.SettingsFragment.preferencesManager")
  public static void injectPreferencesManager(SettingsFragment instance,
      PreferencesManager preferencesManager) {
    instance.preferencesManager = preferencesManager;
  }
}
