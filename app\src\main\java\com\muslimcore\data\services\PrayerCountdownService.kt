package com.muslimcore.data.services

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Binder
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import androidx.core.app.NotificationCompat
import com.muslimcore.R
import com.muslimcore.data.local.managers.PrayerTimeManager
import com.muslimcore.domain.models.PrayerCountdown
import com.muslimcore.domain.models.UserLocation
import com.muslimcore.domain.repositories.LocationRepository
import com.muslimcore.presentation.MainActivity
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import javax.inject.Inject

/**
 * Foreground service that manages prayer countdown timer
 * Provides countdown data to both UI and notification
 */
@AndroidEntryPoint
class PrayerCountdownService : Service() {

    @Inject
    lateinit var prayerTimeManager: PrayerTimeManager
    
    @Inject
    lateinit var locationRepository: LocationRepository

    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    private val binder = PrayerCountdownBinder()
    
    private var countdownHandler: Handler? = null
    private var countdownRunnable: Runnable? = null
    
    private val _prayerCountdown = MutableStateFlow<PrayerCountdown?>(null)
    val prayerCountdown: StateFlow<PrayerCountdown?> = _prayerCountdown.asStateFlow()
    
    private var currentLocation: UserLocation? = null
    private var currentPrayerTimes: PrayerTimeManager.PrayerTimes? = null
    
    companion object {
        const val NOTIFICATION_ID = 1001
        const val CHANNEL_ID = "prayer_countdown_channel"
        const val ACTION_START_COUNTDOWN = "START_COUNTDOWN"
        const val ACTION_STOP_COUNTDOWN = "STOP_COUNTDOWN"
        
        fun startService(context: Context) {
            val intent = Intent(context, PrayerCountdownService::class.java).apply {
                action = ACTION_START_COUNTDOWN
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        
        fun stopService(context: Context) {
            val intent = Intent(context, PrayerCountdownService::class.java).apply {
                action = ACTION_STOP_COUNTDOWN
            }
            context.stopService(intent)
        }
    }

    inner class PrayerCountdownBinder : Binder() {
        fun getService(): PrayerCountdownService = this@PrayerCountdownService
    }

    override fun onBind(intent: Intent?): IBinder = binder

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        initializeLocation()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_COUNTDOWN -> {
                startForegroundService()
                startCountdownTimer()
            }
            ACTION_STOP_COUNTDOWN -> {
                stopCountdownTimer()
                stopSelf()
            }
        }
        return START_STICKY // Restart if killed
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Prayer Countdown",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Shows countdown to next prayer"
                setShowBadge(false)
                setSound(null, null)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun startForegroundService() {
        val notification = createNotification("Loading prayer times...")
        startForeground(NOTIFICATION_ID, notification)
    }

    private fun createNotification(text: String): Notification {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Muslim Core")
            .setContentText(text)
            .setSmallIcon(R.drawable.ic_prayer)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setSilent(true)
            .setShowWhen(false)
            .build()
    }

    private fun initializeLocation() {
        serviceScope.launch {
            try {
                // Get cached location first
                currentLocation = locationRepository.getCachedLocation()
                    ?: locationRepository.getDefaultLocation()
                
                loadPrayerTimes()
            } catch (e: Exception) {
                // Use default location on error
                currentLocation = locationRepository.getDefaultLocation()
                loadPrayerTimes()
            }
        }
    }

    private fun loadPrayerTimes() {
        serviceScope.launch {
            try {
                currentLocation?.let { location ->
                    currentPrayerTimes = prayerTimeManager.calculatePrayerTimes(
                        location.latitude,
                        location.longitude
                    )
                }
            } catch (e: Exception) {
                android.util.Log.e("PrayerCountdownService", "Error loading prayer times", e)
            }
        }
    }

    private fun startCountdownTimer() {
        countdownHandler = Handler(Looper.getMainLooper())
        updateCountdown()
    }

    private fun updateCountdown() {
        try {
            currentPrayerTimes?.let { prayerTimes ->
                val nextPrayer = prayerTimeManager.getNextPrayer(prayerTimes)
                val timeFormat = SimpleDateFormat("h:mm a", Locale.getDefault())
                
                val countdown = PrayerCountdown(
                    currentPrayerName = getCurrentPrayerName(prayerTimes),
                    currentPrayerTime = timeFormat.format(getCurrentPrayerTime(prayerTimes).time),
                    nextPrayerName = nextPrayer.name,
                    nextPrayerTime = timeFormat.format(nextPrayer.time.time),
                    timeUntilNext = prayerTimeManager.formatTimeUntil(nextPrayer.timeUntil),
                    timeUntilNextMillis = nextPrayer.timeUntil
                )
                
                // Update StateFlow for UI
                _prayerCountdown.value = countdown
                
                // Update notification
                val notification = createNotification(countdown.getNotificationText())
                val notificationManager = getSystemService(NotificationManager::class.java)
                notificationManager.notify(NOTIFICATION_ID, notification)
                
                // Schedule next update
                countdownRunnable = Runnable { updateCountdown() }
                countdownHandler?.postDelayed(countdownRunnable!!, 1000)
            }
        } catch (e: Exception) {
            android.util.Log.e("PrayerCountdownService", "Error updating countdown", e)
            // Retry in 5 seconds
            countdownRunnable = Runnable { updateCountdown() }
            countdownHandler?.postDelayed(countdownRunnable!!, 5000)
        }
    }

    private fun getCurrentPrayerName(prayerTimes: PrayerTimeManager.PrayerTimes): String {
        val now = Calendar.getInstance()
        val prayers = listOf(
            "Fajr" to prayerTimes.fajr,
            "Dhuhr" to prayerTimes.dhuhr,
            "Asr" to prayerTimes.asr,
            "Maghrib" to prayerTimes.maghrib,
            "Isha" to prayerTimes.isha
        )

        for (i in prayers.indices) {
            val (name, time) = prayers[i]
            val nextIndex = if (i + 1 < prayers.size) i + 1 else 0
            val nextTime = if (nextIndex == 0) {
                // Tomorrow's Fajr
                val tomorrow = Calendar.getInstance().apply { add(Calendar.DAY_OF_YEAR, 1) }
                prayerTimeManager.calculatePrayerTimes(
                    currentLocation?.latitude ?: 0.0,
                    currentLocation?.longitude ?: 0.0,
                    tomorrow
                ).fajr
            } else {
                prayers[nextIndex].second
            }

            if (now.timeInMillis >= time.timeInMillis && now.timeInMillis < nextTime.timeInMillis) {
                return name
            }
        }
        return "Isha" // Default
    }

    private fun getCurrentPrayerTime(prayerTimes: PrayerTimeManager.PrayerTimes): Calendar {
        val currentName = getCurrentPrayerName(prayerTimes)
        return when (currentName) {
            "Fajr" -> prayerTimes.fajr
            "Dhuhr" -> prayerTimes.dhuhr
            "Asr" -> prayerTimes.asr
            "Maghrib" -> prayerTimes.maghrib
            else -> prayerTimes.isha
        }
    }

    private fun stopCountdownTimer() {
        countdownHandler?.removeCallbacks(countdownRunnable ?: return)
        countdownHandler = null
        countdownRunnable = null
    }

    override fun onDestroy() {
        super.onDestroy()
        stopCountdownTimer()
        serviceScope.cancel()
    }
}
