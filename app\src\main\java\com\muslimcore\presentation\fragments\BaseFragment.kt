package com.muslimcore.presentation.fragments

import android.content.res.ColorStateList
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.children
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.google.android.material.card.MaterialCardView
import com.muslimcore.data.local.PreferencesManager
import com.muslimcore.presentation.utils.ThemeManager

open class BaseFragment : Fragment() {

    protected lateinit var themeManager: ThemeManager

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val preferencesManager = PreferencesManager(requireContext())
        themeManager = ThemeManager(preferencesManager)
        applyTheme()
    }

    protected open fun applyTheme() {
        val themeColors = themeManager.getThemeColors(requireContext())

        // Apply background color to root view
        view?.setBackgroundColor(themeColors.backgroundColor)

        // Apply theme to all child views recursively
        view?.let { applyThemeToViewGroup(it, themeColors) }

        // Subclasses can override this method to apply specific theming
        onThemeApplied(themeColors)
    }

    private fun applyThemeToViewGroup(view: View, themeColors: ThemeManager.ThemeColors) {
        when (view) {
            is MaterialButton -> {
                view.backgroundTintList = ColorStateList.valueOf(themeColors.buttonBackgroundColor)
                view.setTextColor(themeColors.buttonTextColor)
                view.iconTint = ColorStateList.valueOf(themeColors.buttonTextColor)
            }
            is MaterialCardView -> {
                view.setCardBackgroundColor(themeColors.cardBackgroundColor)
            }
            is TextView -> {
                view.setTextColor(themeColors.textPrimaryColor)
            }
            is ImageView -> {
                view.imageTintList = ColorStateList.valueOf(themeColors.iconTintColor)
            }
            is RecyclerView -> {
                view.setBackgroundColor(themeColors.backgroundColor)
            }
        }

        // Apply to child views if it's a ViewGroup
        if (view is ViewGroup) {
            view.children.forEach { child ->
                applyThemeToViewGroup(child, themeColors)
            }
        }
    }

    protected open fun onThemeApplied(themeColors: ThemeManager.ThemeColors) {
        // Override in subclasses to apply specific theming
    }

    // Helper method for fragments to refresh theme
    fun refreshTheme() {
        applyTheme()
    }
}
