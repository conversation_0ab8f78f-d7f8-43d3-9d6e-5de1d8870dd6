package com.muslimcore.data.repositories;

import android.content.Context;
import com.muslimcore.data.local.PreferencesManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class LocationRepositoryImpl_Factory implements Factory<LocationRepositoryImpl> {
  private final Provider<Context> contextProvider;

  private final Provider<PreferencesManager> preferencesManagerProvider;

  public LocationRepositoryImpl_Factory(Provider<Context> contextProvider,
      Provider<PreferencesManager> preferencesManagerProvider) {
    this.contextProvider = contextProvider;
    this.preferencesManagerProvider = preferencesManagerProvider;
  }

  @Override
  public LocationRepositoryImpl get() {
    return newInstance(contextProvider.get(), preferencesManagerProvider.get());
  }

  public static LocationRepositoryImpl_Factory create(Provider<Context> contextProvider,
      Provider<PreferencesManager> preferencesManagerProvider) {
    return new LocationRepositoryImpl_Factory(contextProvider, preferencesManagerProvider);
  }

  public static LocationRepositoryImpl newInstance(Context context,
      PreferencesManager preferencesManager) {
    return new LocationRepositoryImpl(context, preferencesManager);
  }
}
