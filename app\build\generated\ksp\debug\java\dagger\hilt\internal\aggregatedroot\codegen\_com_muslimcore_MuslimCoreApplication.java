package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.muslimcore.MuslimCoreApplication",
    rootPackage = "com.muslimcore",
    originatingRoot = "com.muslimcore.MuslimCoreApplication",
    originatingRootPackage = "com.muslimcore",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "MuslimCoreApplication",
    originatingRootSimpleNames = "MuslimCoreApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_muslimcore_MuslimCoreApplication {
}
