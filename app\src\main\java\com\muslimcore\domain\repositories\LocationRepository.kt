package com.muslimcore.domain.repositories

import com.muslimcore.domain.models.LocationResult
import com.muslimcore.domain.models.LocationState
import com.muslimcore.domain.models.UserLocation
import kotlinx.coroutines.flow.StateFlow

/**
 * Repository interface for location operations
 */
interface LocationRepository {
    
    /**
     * Current location state as a reactive stream
     */
    val locationState: StateFlow<LocationState>
    
    /**
     * Check if location permissions are granted
     */
    fun hasLocationPermission(): Boolean
    
    /**
     * Get current location with caching and fallback strategies
     */
    suspend fun getCurrentLocation(): LocationResult
    
    /**
     * Get cached location if available and valid
     */
    suspend fun getCachedLocation(): UserLocation?
    
    /**
     * Save location to cache
     */
    suspend fun saveLocation(location: UserLocation)
    
    /**
     * Clear cached location
     */
    suspend fun clearLocation()
    
    /**
     * Get default location (Mecca)
     */
    fun getDefaultLocation(): UserLocation
    
    /**
     * Search for location by name
     */
    suspend fun searchLocation(query: String): LocationResult
}
