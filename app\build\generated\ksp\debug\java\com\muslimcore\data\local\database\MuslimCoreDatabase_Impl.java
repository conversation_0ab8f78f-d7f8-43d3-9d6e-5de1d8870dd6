package com.muslimcore.data.local.database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import com.muslimcore.data.local.dao.AdhkarDao;
import com.muslimcore.data.local.dao.AdhkarDao_Impl;
import com.muslimcore.data.local.dao.PrayerDao;
import com.muslimcore.data.local.dao.PrayerDao_Impl;
import com.muslimcore.data.local.dao.QuranDao;
import com.muslimcore.data.local.dao.QuranDao_Impl;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class MuslimCoreDatabase_Impl extends MuslimCoreDatabase {
  private volatile PrayerDao _prayerDao;

  private volatile QuranDao _quranDao;

  private volatile AdhkarDao _adhkarDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(2) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `prayer_times` (`date` TEXT NOT NULL, `fajr` TEXT NOT NULL, `sunrise` TEXT NOT NULL, `dhuhr` TEXT NOT NULL, `asr` TEXT NOT NULL, `maghrib` TEXT NOT NULL, `isha` TEXT NOT NULL, `hijriDate` TEXT NOT NULL, `city` TEXT NOT NULL, `country` TEXT NOT NULL, `latitude` REAL NOT NULL, `longitude` REAL NOT NULL, `timezone` TEXT NOT NULL, `method` INTEGER NOT NULL, `cachedAt` INTEGER NOT NULL, PRIMARY KEY(`date`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `prayer_logs` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `date` TEXT NOT NULL, `prayerName` TEXT NOT NULL, `isCompleted` INTEGER NOT NULL, `completedAt` INTEGER, `userId` TEXT, `createdAt` INTEGER NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `surahs` (`number` INTEGER NOT NULL, `name` TEXT NOT NULL, `englishName` TEXT NOT NULL, `englishNameTranslation` TEXT NOT NULL, `numberOfAyahs` INTEGER NOT NULL, `revelationType` TEXT NOT NULL, PRIMARY KEY(`number`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `ayahs` (`number` INTEGER NOT NULL, `text` TEXT NOT NULL, `numberInSurah` INTEGER NOT NULL, `surahNumber` INTEGER NOT NULL, `juz` INTEGER NOT NULL, `manzil` INTEGER NOT NULL, `page` INTEGER NOT NULL, `ruku` INTEGER NOT NULL, `hizbQuarter` INTEGER NOT NULL, `sajda` INTEGER NOT NULL, `translation` TEXT, `audio` TEXT, PRIMARY KEY(`number`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `bookmarks` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `surahNumber` INTEGER NOT NULL, `ayahNumber` INTEGER NOT NULL, `surahName` TEXT NOT NULL, `ayahText` TEXT NOT NULL, `note` TEXT, `createdAt` INTEGER NOT NULL, `userId` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `reading_progress` (`id` INTEGER NOT NULL, `surahNumber` INTEGER NOT NULL, `ayahNumber` INTEGER NOT NULL, `lastReadAt` INTEGER NOT NULL, `userId` TEXT, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `khatma_progress` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `name` TEXT NOT NULL, `targetDays` INTEGER NOT NULL, `startDate` INTEGER NOT NULL, `endDate` INTEGER NOT NULL, `currentSurah` INTEGER NOT NULL, `currentAyah` INTEGER NOT NULL, `isCompleted` INTEGER NOT NULL, `dailyTarget` INTEGER NOT NULL, `userId` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `favorite_surahs` (`surahNumber` INTEGER NOT NULL, `addedAt` INTEGER NOT NULL, PRIMARY KEY(`surahNumber`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `dhikr` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `arabicText` TEXT NOT NULL, `transliteration` TEXT NOT NULL, `translation` TEXT NOT NULL, `reference` TEXT, `count` INTEGER NOT NULL, `category` TEXT NOT NULL, `isFavorite` INTEGER NOT NULL, `benefits` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `dhikr_progress` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `dhikrId` INTEGER NOT NULL, `date` TEXT NOT NULL, `currentCount` INTEGER NOT NULL, `targetCount` INTEGER NOT NULL, `isCompleted` INTEGER NOT NULL, `completedAt` INTEGER, `userId` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS `tasbeeh_sessions` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `dhikrText` TEXT NOT NULL, `count` INTEGER NOT NULL, `targetCount` INTEGER NOT NULL, `startTime` INTEGER NOT NULL, `endTime` INTEGER, `isCompleted` INTEGER NOT NULL, `userId` TEXT)");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '44313e718725abb7f71e2ca8c869d3b5')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `prayer_times`");
        db.execSQL("DROP TABLE IF EXISTS `prayer_logs`");
        db.execSQL("DROP TABLE IF EXISTS `surahs`");
        db.execSQL("DROP TABLE IF EXISTS `ayahs`");
        db.execSQL("DROP TABLE IF EXISTS `bookmarks`");
        db.execSQL("DROP TABLE IF EXISTS `reading_progress`");
        db.execSQL("DROP TABLE IF EXISTS `khatma_progress`");
        db.execSQL("DROP TABLE IF EXISTS `favorite_surahs`");
        db.execSQL("DROP TABLE IF EXISTS `dhikr`");
        db.execSQL("DROP TABLE IF EXISTS `dhikr_progress`");
        db.execSQL("DROP TABLE IF EXISTS `tasbeeh_sessions`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsPrayerTimes = new HashMap<String, TableInfo.Column>(15);
        _columnsPrayerTimes.put("date", new TableInfo.Column("date", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerTimes.put("fajr", new TableInfo.Column("fajr", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerTimes.put("sunrise", new TableInfo.Column("sunrise", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerTimes.put("dhuhr", new TableInfo.Column("dhuhr", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerTimes.put("asr", new TableInfo.Column("asr", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerTimes.put("maghrib", new TableInfo.Column("maghrib", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerTimes.put("isha", new TableInfo.Column("isha", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerTimes.put("hijriDate", new TableInfo.Column("hijriDate", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerTimes.put("city", new TableInfo.Column("city", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerTimes.put("country", new TableInfo.Column("country", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerTimes.put("latitude", new TableInfo.Column("latitude", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerTimes.put("longitude", new TableInfo.Column("longitude", "REAL", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerTimes.put("timezone", new TableInfo.Column("timezone", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerTimes.put("method", new TableInfo.Column("method", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerTimes.put("cachedAt", new TableInfo.Column("cachedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysPrayerTimes = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesPrayerTimes = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoPrayerTimes = new TableInfo("prayer_times", _columnsPrayerTimes, _foreignKeysPrayerTimes, _indicesPrayerTimes);
        final TableInfo _existingPrayerTimes = TableInfo.read(db, "prayer_times");
        if (!_infoPrayerTimes.equals(_existingPrayerTimes)) {
          return new RoomOpenHelper.ValidationResult(false, "prayer_times(com.muslimcore.data.local.entities.PrayerTimesEntity).\n"
                  + " Expected:\n" + _infoPrayerTimes + "\n"
                  + " Found:\n" + _existingPrayerTimes);
        }
        final HashMap<String, TableInfo.Column> _columnsPrayerLogs = new HashMap<String, TableInfo.Column>(7);
        _columnsPrayerLogs.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerLogs.put("date", new TableInfo.Column("date", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerLogs.put("prayerName", new TableInfo.Column("prayerName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerLogs.put("isCompleted", new TableInfo.Column("isCompleted", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerLogs.put("completedAt", new TableInfo.Column("completedAt", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerLogs.put("userId", new TableInfo.Column("userId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsPrayerLogs.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysPrayerLogs = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesPrayerLogs = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoPrayerLogs = new TableInfo("prayer_logs", _columnsPrayerLogs, _foreignKeysPrayerLogs, _indicesPrayerLogs);
        final TableInfo _existingPrayerLogs = TableInfo.read(db, "prayer_logs");
        if (!_infoPrayerLogs.equals(_existingPrayerLogs)) {
          return new RoomOpenHelper.ValidationResult(false, "prayer_logs(com.muslimcore.data.local.entities.PrayerLogEntity).\n"
                  + " Expected:\n" + _infoPrayerLogs + "\n"
                  + " Found:\n" + _existingPrayerLogs);
        }
        final HashMap<String, TableInfo.Column> _columnsSurahs = new HashMap<String, TableInfo.Column>(6);
        _columnsSurahs.put("number", new TableInfo.Column("number", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSurahs.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSurahs.put("englishName", new TableInfo.Column("englishName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSurahs.put("englishNameTranslation", new TableInfo.Column("englishNameTranslation", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSurahs.put("numberOfAyahs", new TableInfo.Column("numberOfAyahs", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsSurahs.put("revelationType", new TableInfo.Column("revelationType", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysSurahs = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesSurahs = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoSurahs = new TableInfo("surahs", _columnsSurahs, _foreignKeysSurahs, _indicesSurahs);
        final TableInfo _existingSurahs = TableInfo.read(db, "surahs");
        if (!_infoSurahs.equals(_existingSurahs)) {
          return new RoomOpenHelper.ValidationResult(false, "surahs(com.muslimcore.data.local.entities.SurahEntity).\n"
                  + " Expected:\n" + _infoSurahs + "\n"
                  + " Found:\n" + _existingSurahs);
        }
        final HashMap<String, TableInfo.Column> _columnsAyahs = new HashMap<String, TableInfo.Column>(12);
        _columnsAyahs.put("number", new TableInfo.Column("number", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAyahs.put("text", new TableInfo.Column("text", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAyahs.put("numberInSurah", new TableInfo.Column("numberInSurah", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAyahs.put("surahNumber", new TableInfo.Column("surahNumber", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAyahs.put("juz", new TableInfo.Column("juz", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAyahs.put("manzil", new TableInfo.Column("manzil", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAyahs.put("page", new TableInfo.Column("page", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAyahs.put("ruku", new TableInfo.Column("ruku", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAyahs.put("hizbQuarter", new TableInfo.Column("hizbQuarter", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAyahs.put("sajda", new TableInfo.Column("sajda", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAyahs.put("translation", new TableInfo.Column("translation", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAyahs.put("audio", new TableInfo.Column("audio", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysAyahs = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesAyahs = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoAyahs = new TableInfo("ayahs", _columnsAyahs, _foreignKeysAyahs, _indicesAyahs);
        final TableInfo _existingAyahs = TableInfo.read(db, "ayahs");
        if (!_infoAyahs.equals(_existingAyahs)) {
          return new RoomOpenHelper.ValidationResult(false, "ayahs(com.muslimcore.data.local.entities.AyahEntity).\n"
                  + " Expected:\n" + _infoAyahs + "\n"
                  + " Found:\n" + _existingAyahs);
        }
        final HashMap<String, TableInfo.Column> _columnsBookmarks = new HashMap<String, TableInfo.Column>(8);
        _columnsBookmarks.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBookmarks.put("surahNumber", new TableInfo.Column("surahNumber", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBookmarks.put("ayahNumber", new TableInfo.Column("ayahNumber", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBookmarks.put("surahName", new TableInfo.Column("surahName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBookmarks.put("ayahText", new TableInfo.Column("ayahText", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBookmarks.put("note", new TableInfo.Column("note", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBookmarks.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsBookmarks.put("userId", new TableInfo.Column("userId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysBookmarks = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesBookmarks = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoBookmarks = new TableInfo("bookmarks", _columnsBookmarks, _foreignKeysBookmarks, _indicesBookmarks);
        final TableInfo _existingBookmarks = TableInfo.read(db, "bookmarks");
        if (!_infoBookmarks.equals(_existingBookmarks)) {
          return new RoomOpenHelper.ValidationResult(false, "bookmarks(com.muslimcore.data.local.entities.BookmarkEntity).\n"
                  + " Expected:\n" + _infoBookmarks + "\n"
                  + " Found:\n" + _existingBookmarks);
        }
        final HashMap<String, TableInfo.Column> _columnsReadingProgress = new HashMap<String, TableInfo.Column>(5);
        _columnsReadingProgress.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReadingProgress.put("surahNumber", new TableInfo.Column("surahNumber", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReadingProgress.put("ayahNumber", new TableInfo.Column("ayahNumber", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReadingProgress.put("lastReadAt", new TableInfo.Column("lastReadAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsReadingProgress.put("userId", new TableInfo.Column("userId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysReadingProgress = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesReadingProgress = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoReadingProgress = new TableInfo("reading_progress", _columnsReadingProgress, _foreignKeysReadingProgress, _indicesReadingProgress);
        final TableInfo _existingReadingProgress = TableInfo.read(db, "reading_progress");
        if (!_infoReadingProgress.equals(_existingReadingProgress)) {
          return new RoomOpenHelper.ValidationResult(false, "reading_progress(com.muslimcore.data.local.entities.ReadingProgressEntity).\n"
                  + " Expected:\n" + _infoReadingProgress + "\n"
                  + " Found:\n" + _existingReadingProgress);
        }
        final HashMap<String, TableInfo.Column> _columnsKhatmaProgress = new HashMap<String, TableInfo.Column>(10);
        _columnsKhatmaProgress.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKhatmaProgress.put("name", new TableInfo.Column("name", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKhatmaProgress.put("targetDays", new TableInfo.Column("targetDays", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKhatmaProgress.put("startDate", new TableInfo.Column("startDate", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKhatmaProgress.put("endDate", new TableInfo.Column("endDate", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKhatmaProgress.put("currentSurah", new TableInfo.Column("currentSurah", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKhatmaProgress.put("currentAyah", new TableInfo.Column("currentAyah", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKhatmaProgress.put("isCompleted", new TableInfo.Column("isCompleted", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKhatmaProgress.put("dailyTarget", new TableInfo.Column("dailyTarget", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsKhatmaProgress.put("userId", new TableInfo.Column("userId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysKhatmaProgress = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesKhatmaProgress = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoKhatmaProgress = new TableInfo("khatma_progress", _columnsKhatmaProgress, _foreignKeysKhatmaProgress, _indicesKhatmaProgress);
        final TableInfo _existingKhatmaProgress = TableInfo.read(db, "khatma_progress");
        if (!_infoKhatmaProgress.equals(_existingKhatmaProgress)) {
          return new RoomOpenHelper.ValidationResult(false, "khatma_progress(com.muslimcore.data.local.entities.KhatmaProgressEntity).\n"
                  + " Expected:\n" + _infoKhatmaProgress + "\n"
                  + " Found:\n" + _existingKhatmaProgress);
        }
        final HashMap<String, TableInfo.Column> _columnsFavoriteSurahs = new HashMap<String, TableInfo.Column>(2);
        _columnsFavoriteSurahs.put("surahNumber", new TableInfo.Column("surahNumber", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsFavoriteSurahs.put("addedAt", new TableInfo.Column("addedAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysFavoriteSurahs = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesFavoriteSurahs = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoFavoriteSurahs = new TableInfo("favorite_surahs", _columnsFavoriteSurahs, _foreignKeysFavoriteSurahs, _indicesFavoriteSurahs);
        final TableInfo _existingFavoriteSurahs = TableInfo.read(db, "favorite_surahs");
        if (!_infoFavoriteSurahs.equals(_existingFavoriteSurahs)) {
          return new RoomOpenHelper.ValidationResult(false, "favorite_surahs(com.muslimcore.data.local.entities.FavoriteSurahEntity).\n"
                  + " Expected:\n" + _infoFavoriteSurahs + "\n"
                  + " Found:\n" + _existingFavoriteSurahs);
        }
        final HashMap<String, TableInfo.Column> _columnsDhikr = new HashMap<String, TableInfo.Column>(9);
        _columnsDhikr.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDhikr.put("arabicText", new TableInfo.Column("arabicText", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDhikr.put("transliteration", new TableInfo.Column("transliteration", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDhikr.put("translation", new TableInfo.Column("translation", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDhikr.put("reference", new TableInfo.Column("reference", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDhikr.put("count", new TableInfo.Column("count", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDhikr.put("category", new TableInfo.Column("category", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDhikr.put("isFavorite", new TableInfo.Column("isFavorite", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDhikr.put("benefits", new TableInfo.Column("benefits", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysDhikr = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesDhikr = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoDhikr = new TableInfo("dhikr", _columnsDhikr, _foreignKeysDhikr, _indicesDhikr);
        final TableInfo _existingDhikr = TableInfo.read(db, "dhikr");
        if (!_infoDhikr.equals(_existingDhikr)) {
          return new RoomOpenHelper.ValidationResult(false, "dhikr(com.muslimcore.data.local.entities.DhikrEntity).\n"
                  + " Expected:\n" + _infoDhikr + "\n"
                  + " Found:\n" + _existingDhikr);
        }
        final HashMap<String, TableInfo.Column> _columnsDhikrProgress = new HashMap<String, TableInfo.Column>(8);
        _columnsDhikrProgress.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDhikrProgress.put("dhikrId", new TableInfo.Column("dhikrId", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDhikrProgress.put("date", new TableInfo.Column("date", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDhikrProgress.put("currentCount", new TableInfo.Column("currentCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDhikrProgress.put("targetCount", new TableInfo.Column("targetCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDhikrProgress.put("isCompleted", new TableInfo.Column("isCompleted", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDhikrProgress.put("completedAt", new TableInfo.Column("completedAt", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsDhikrProgress.put("userId", new TableInfo.Column("userId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysDhikrProgress = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesDhikrProgress = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoDhikrProgress = new TableInfo("dhikr_progress", _columnsDhikrProgress, _foreignKeysDhikrProgress, _indicesDhikrProgress);
        final TableInfo _existingDhikrProgress = TableInfo.read(db, "dhikr_progress");
        if (!_infoDhikrProgress.equals(_existingDhikrProgress)) {
          return new RoomOpenHelper.ValidationResult(false, "dhikr_progress(com.muslimcore.data.local.entities.DhikrProgressEntity).\n"
                  + " Expected:\n" + _infoDhikrProgress + "\n"
                  + " Found:\n" + _existingDhikrProgress);
        }
        final HashMap<String, TableInfo.Column> _columnsTasbeehSessions = new HashMap<String, TableInfo.Column>(8);
        _columnsTasbeehSessions.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasbeehSessions.put("dhikrText", new TableInfo.Column("dhikrText", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasbeehSessions.put("count", new TableInfo.Column("count", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasbeehSessions.put("targetCount", new TableInfo.Column("targetCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasbeehSessions.put("startTime", new TableInfo.Column("startTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasbeehSessions.put("endTime", new TableInfo.Column("endTime", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasbeehSessions.put("isCompleted", new TableInfo.Column("isCompleted", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsTasbeehSessions.put("userId", new TableInfo.Column("userId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysTasbeehSessions = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesTasbeehSessions = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoTasbeehSessions = new TableInfo("tasbeeh_sessions", _columnsTasbeehSessions, _foreignKeysTasbeehSessions, _indicesTasbeehSessions);
        final TableInfo _existingTasbeehSessions = TableInfo.read(db, "tasbeeh_sessions");
        if (!_infoTasbeehSessions.equals(_existingTasbeehSessions)) {
          return new RoomOpenHelper.ValidationResult(false, "tasbeeh_sessions(com.muslimcore.data.local.entities.TasbeehSessionEntity).\n"
                  + " Expected:\n" + _infoTasbeehSessions + "\n"
                  + " Found:\n" + _existingTasbeehSessions);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "44313e718725abb7f71e2ca8c869d3b5", "f91e180c025552111c3eff34faa64070");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "prayer_times","prayer_logs","surahs","ayahs","bookmarks","reading_progress","khatma_progress","favorite_surahs","dhikr","dhikr_progress","tasbeeh_sessions");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `prayer_times`");
      _db.execSQL("DELETE FROM `prayer_logs`");
      _db.execSQL("DELETE FROM `surahs`");
      _db.execSQL("DELETE FROM `ayahs`");
      _db.execSQL("DELETE FROM `bookmarks`");
      _db.execSQL("DELETE FROM `reading_progress`");
      _db.execSQL("DELETE FROM `khatma_progress`");
      _db.execSQL("DELETE FROM `favorite_surahs`");
      _db.execSQL("DELETE FROM `dhikr`");
      _db.execSQL("DELETE FROM `dhikr_progress`");
      _db.execSQL("DELETE FROM `tasbeeh_sessions`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(PrayerDao.class, PrayerDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(QuranDao.class, QuranDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(AdhkarDao.class, AdhkarDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public PrayerDao prayerDao() {
    if (_prayerDao != null) {
      return _prayerDao;
    } else {
      synchronized(this) {
        if(_prayerDao == null) {
          _prayerDao = new PrayerDao_Impl(this);
        }
        return _prayerDao;
      }
    }
  }

  @Override
  public QuranDao quranDao() {
    if (_quranDao != null) {
      return _quranDao;
    } else {
      synchronized(this) {
        if(_quranDao == null) {
          _quranDao = new QuranDao_Impl(this);
        }
        return _quranDao;
      }
    }
  }

  @Override
  public AdhkarDao adhkarDao() {
    if (_adhkarDao != null) {
      return _adhkarDao;
    } else {
      synchronized(this) {
        if(_adhkarDao == null) {
          _adhkarDao = new AdhkarDao_Impl(this);
        }
        return _adhkarDao;
      }
    }
  }
}
