package com.muslimcore.data.repositories;

import com.muslimcore.data.local.dao.QuranDao;
import com.muslimcore.data.remote.api.QuranApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class QuranRepositoryImpl_Factory implements Factory<QuranRepositoryImpl> {
  private final Provider<QuranDao> quranDaoProvider;

  private final Provider<QuranApi> quranApiProvider;

  public QuranRepositoryImpl_Factory(Provider<QuranDao> quranDaoProvider,
      Provider<QuranApi> quranApiProvider) {
    this.quranDaoProvider = quranDaoProvider;
    this.quranApiProvider = quranApiProvider;
  }

  @Override
  public QuranRepositoryImpl get() {
    return newInstance(quranDaoProvider.get(), quranApiProvider.get());
  }

  public static QuranRepositoryImpl_Factory create(Provider<QuranDao> quranDaoProvider,
      Provider<QuranApi> quranApiProvider) {
    return new QuranRepositoryImpl_Factory(quranDaoProvider, quranApiProvider);
  }

  public static QuranRepositoryImpl newInstance(QuranDao quranDao, QuranApi quranApi) {
    return new QuranRepositoryImpl(quranDao, quranApi);
  }
}
