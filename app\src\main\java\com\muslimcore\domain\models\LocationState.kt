package com.muslimcore.domain.models

/**
 * Represents the different states of location operations
 */
sealed class LocationState {
    object Idle : LocationState()
    object Loading : LocationState()
    data class Success(val location: UserLocation) : LocationState()
    data class Error(val message: String, val throwable: Throwable? = null) : LocationState()
    object PermissionDenied : LocationState()
    object PermissionRequired : LocationState()
}

/**
 * Domain model for user location
 */
data class UserLocation(
    val latitude: Double,
    val longitude: Double,
    val city: String,
    val country: String,
    val timestamp: Long = System.currentTimeMillis()
) {
    fun isValid(): Boolean {
        return latitude != 0.0 && longitude != 0.0 && city.isNotBlank() && country.isNotBlank()
    }
    
    fun isRecent(maxAgeMillis: Long = 24 * 60 * 60 * 1000L): Boolean {
        return System.currentTimeMillis() - timestamp < maxAgeMillis
    }
}

/**
 * Location operation result
 */
sealed class LocationResult {
    data class Success(val location: UserLocation) : LocationResult()
    data class Error(val message: String, val throwable: Throwable? = null) : LocationResult()
    object PermissionDenied : LocationResult()
    object Timeout : LocationResult()
}
