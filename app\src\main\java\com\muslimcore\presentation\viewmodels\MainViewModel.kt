package com.muslimcore.presentation.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.muslimcore.data.local.PreferencesManager
import com.muslimcore.domain.models.LocationState
import com.muslimcore.domain.repositories.LocationRepository
import com.muslimcore.domain.repositories.UserRepository
import com.muslimcore.domain.usecases.GetCurrentLocationUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MainViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val locationRepository: LocationRepository,
    private val getCurrentLocationUseCase: GetCurrentLocationUseCase,
    private val preferencesManager: PreferencesManager
) : ViewModel() {

    data class UiState(
        val isLoading: Boolean = false,
        val shouldRequestLocationPermission: Boolean = false,
        val shouldRequestNotificationPermission: Boolean = false,
        val error: String? = null
    )

    private val _uiState = MutableStateFlow(UiState())
    val uiState: StateFlow<UiState> = _uiState.asStateFlow()

    // Expose location state from repository
    val locationState: StateFlow<LocationState> = locationRepository.locationState

    init {
        initializeApp()
    }

    private fun initializeApp() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)

                // Initialize user session
                if (!userRepository.isUserSignedIn()) {
                    userRepository.signInAnonymously()
                }

                _uiState.value = _uiState.value.copy(isLoading = false)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message
                )
            }
        }
    }

    fun setLocationPermissionNeeded(needed: Boolean) {
        _uiState.value = _uiState.value.copy(shouldRequestLocationPermission = needed)
    }

    fun checkAndRequestLocationPermission() {
        // Only request if not previously asked or if user has valid location
        if (!preferencesManager.locationPermissionAsked && !preferencesManager.hasValidLocation()) {
            _uiState.value = _uiState.value.copy(shouldRequestLocationPermission = true)
            preferencesManager.locationPermissionAsked = true
        }
    }

    fun setNotificationPermissionNeeded(needed: Boolean) {
        _uiState.value = _uiState.value.copy(shouldRequestNotificationPermission = needed)
    }

    fun onLocationPermissionGranted() {
        _uiState.value = _uiState.value.copy(shouldRequestLocationPermission = false)
        viewModelScope.launch {
            try {
                // Use the new location system with retry logic
                getCurrentLocationUseCase.executeWithRetry()
            } catch (e: Exception) {
                // Handle error silently or show message
            }
        }
    }

    fun onLocationPermissionDenied() {
        _uiState.value = _uiState.value.copy(shouldRequestLocationPermission = false)
        // Could show a message about manual location setting
    }

    fun onNotificationPermissionResult(granted: Boolean) {
        _uiState.value = _uiState.value.copy(shouldRequestNotificationPermission = false)
        viewModelScope.launch {
            // Update notification settings
            val preferences = userRepository.getPreferences()
            // Update preferences based on permission result
        }
    }

    fun onResume() {
        // Handle app resume
    }

    fun onPause() {
        // Handle app pause
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}
